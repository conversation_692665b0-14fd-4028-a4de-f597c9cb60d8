//! Advanced Node System Demo
//!
//! This example demonstrates the comprehensive Godot-compatible node system
//! including Sprite2D, Label, Button, CollisionShape2D, Area2D, and RigidBody2D.
//! It showcases practical game development scenarios with UI, graphics, and physics.

use verturion::core::scene::Node;
use verturion::core::scene::nodes::{Node2D, Sprite2D};
use verturion::core::scene::nodes::ui::{Label, Button};
use verturion::core::scene::nodes::physics::{CollisionShape2D, Area2D, RigidBody2D};
use verturion::core::scene::nodes::ui::label::{TextAlign, VerticalAlign};
use verturion::core::scene::nodes::ui::button::ButtonActionMode;
use verturion::core::scene::nodes::physics::collision_shape2d::Shape2D;
use verturion::core::scene::nodes::physics::area2d::SpaceOverride;
use verturion::core::scene::nodes::physics::rigid_body2d::RigidBodyMode;
use verturion::core::variant::{Variant, Color, String as GodotString, Array, Dictionary};
use verturion::core::math::{Vector2, Rect2};

fn main() {
    println!("=== Advanced Verturion Node System Demo ===\n");

    // Demonstrate 2D graphics and UI
    demo_graphics_and_ui();

    // Demonstrate physics system
    demo_physics_system();

    // Demonstrate Variant integration
    demo_variant_integration();

    // Demonstrate complex scene setup
    demo_game_scene();
}

fn demo_graphics_and_ui() {
    println!("1. Graphics and UI System:");

    // Create a UI scene
    let mut ui_root = Node::new("UIRoot");

    // Create a sprite for the background
    let mut background = Sprite2D::new("Background");
    background.set_texture(Some("res://textures/background.png".to_string()));
    background.set_centered(true);
    background.base_mut().set_position(Vector2::new(400.0, 300.0));
    background.set_modulate(Color::new(0.8, 0.9, 1.0, 1.0)); // Slight blue tint

    // Create a player sprite
    let mut player_sprite = Sprite2D::new("PlayerSprite");
    player_sprite.set_texture(Some("res://sprites/player.png".to_string()));
    player_sprite.set_region_enabled(true);
    player_sprite.set_region_rect(Rect2::new(0.0, 0.0, 64.0, 64.0));
    player_sprite.set_flip_h(false);
    player_sprite.base_mut().set_position(Vector2::new(100.0, 200.0));

    // Create UI labels
    let mut title_label = Label::new("TitleLabel");
    title_label.set_text(GodotString::from("Verturion Game Engine"));
    title_label.set_font_size(32);
    title_label.set_font_color(Color::new(1.0, 1.0, 0.0, 1.0)); // Yellow
    title_label.set_text_align(TextAlign::Center);
    title_label.set_vertical_align(VerticalAlign::Center);
    title_label.set_size(Vector2::new(400.0, 60.0));
    title_label.base_mut().set_position(Vector2::new(200.0, 50.0));

    let mut score_label = Label::new("ScoreLabel");
    score_label.set_text(GodotString::from("Score: 1,250"));
    score_label.set_font_size(18);
    score_label.set_font_color(Color::WHITE);
    score_label.set_text_align(TextAlign::Right);
    score_label.set_size(Vector2::new(150.0, 30.0));
    score_label.base_mut().set_position(Vector2::new(650.0, 20.0));

    // Create interactive buttons
    let mut play_button = Button::new("PlayButton");
    play_button.set_text(GodotString::from("Play Game"));
    play_button.set_font_size(20);
    play_button.set_font_color(Color::WHITE);
    play_button.set_action_mode(ButtonActionMode::Release);
    play_button.set_size(Vector2::new(120.0, 40.0));
    play_button.base_mut().set_position(Vector2::new(300.0, 400.0));

    let mut settings_button = Button::new("SettingsButton");
    settings_button.set_text(GodotString::from("Settings"));
    settings_button.set_font_size(16);
    settings_button.set_toggle_mode(true);
    settings_button.set_size(Vector2::new(100.0, 35.0));
    settings_button.base_mut().set_position(Vector2::new(450.0, 400.0));

    // Build the scene tree
    ui_root.add_child(background.base().base().clone());
    ui_root.add_child(player_sprite.base().base().clone());
    ui_root.add_child(title_label.base().base().clone());
    ui_root.add_child(score_label.base().base().clone());
    ui_root.add_child(play_button.base().base().clone());
    ui_root.add_child(settings_button.base().base().clone());

    println!("   Created UI scene with {} nodes", ui_root.get_child_count());
    println!("   Background: {}", background);
    println!("   Player Sprite: {}", player_sprite);
    println!("   Title: {}", title_label);
    println!("   Play Button: {}", play_button);
    println!();
}

fn demo_physics_system() {
    println!("2. Physics System:");

    // Create a physics world
    let mut physics_root = Node::new("PhysicsWorld");

    // Create a player physics body
    let mut player_body = RigidBody2D::new("PlayerBody");
    player_body.set_mode(RigidBodyMode::Dynamic);
    player_body.set_mass(75.0); // 75kg character
    player_body.set_gravity_scale(1.0);
    player_body.set_linear_damp(0.1);
    player_body.base_mut().set_position(Vector2::new(100.0, 300.0));

    // Create collision shape for player
    let mut player_collision = CollisionShape2D::new("PlayerCollision");
    player_collision.set_shape(Some(Shape2D::Capsule {
        height: 60.0,
        radius: 20.0
    }));
    player_collision.set_debug_color(Some(Color::new(0.0, 1.0, 0.0, 0.5)));

    // Create a platform (static body)
    let mut platform = RigidBody2D::new("Platform");
    platform.set_mode(RigidBodyMode::Static);
    platform.base_mut().set_position(Vector2::new(400.0, 500.0));

    let mut platform_collision = CollisionShape2D::new("PlatformCollision");
    platform_collision.set_shape(Some(Shape2D::Rectangle {
        size: Vector2::new(200.0, 20.0)
    }));
    platform_collision.set_debug_color(Some(Color::new(0.5, 0.3, 0.1, 0.8)));

    // Create a trigger area
    let mut trigger_area = Area2D::new("TriggerZone");
    trigger_area.set_monitoring(true);
    trigger_area.set_collision_layer(4);
    trigger_area.set_collision_mask(1);
    trigger_area.set_space_override_mode(SpaceOverride::Replace);
    trigger_area.set_gravity_vector(Vector2::new(0.0, -200.0)); // Reduced gravity
    trigger_area.base_mut().set_position(Vector2::new(600.0, 400.0));

    let mut trigger_collision = CollisionShape2D::new("TriggerCollision");
    trigger_collision.set_shape(Some(Shape2D::Circle { radius: 50.0 }));
    trigger_collision.set_debug_color(Some(Color::new(1.0, 0.0, 1.0, 0.3)));

    // Apply some forces to demonstrate physics
    player_body.apply_central_force(Vector2::new(200.0, -100.0));
    player_body.apply_torque(10.0);

    // Simulate area interactions (using a dummy ID for demonstration)
    trigger_area._on_body_entered(12345);

    // Build physics scene
    physics_root.add_child(player_body.base().base().clone());
    physics_root.add_child(player_collision.base().base().clone());
    physics_root.add_child(platform.base().base().clone());
    physics_root.add_child(platform_collision.base().base().clone());
    physics_root.add_child(trigger_area.base().base().clone());
    physics_root.add_child(trigger_collision.base().base().clone());

    println!("   Created physics world with {} nodes", physics_root.get_child_count());
    println!("   Player Body: {}", player_body);
    println!("   Platform: {}", platform);
    println!("   Trigger Area: {}", trigger_area);
    println!("   Bodies in trigger: {}", trigger_area.get_overlapping_body_count());
    println!();
}

fn demo_variant_integration() {
    println!("3. Variant System Integration:");

    // Create various node types
    let sprite = Sprite2D::new("VariantSprite");
    let label = Label::new("VariantLabel");
    let button = Button::new("VariantButton");
    let collision = CollisionShape2D::new("VariantCollision");
    let area = Area2D::new("VariantArea");
    let body = RigidBody2D::new("VariantBody");

    // Convert to variants
    let sprite_var = Variant::from(sprite);
    let label_var = Variant::from(label);
    let button_var = Variant::from(button);
    let collision_var = Variant::from(collision);
    let area_var = Variant::from(area);
    let body_var = Variant::from(body);

    println!("   Created node variants:");
    println!("   Sprite2D variant: {}", sprite_var);
    println!("   Label variant: {}", label_var);
    println!("   Button variant: {}", button_var);

    // Test type checking
    println!("   Type checking:");
    println!("   sprite_var.is_sprite2d(): {}", sprite_var.is_sprite2d());
    println!("   label_var.is_label(): {}", label_var.is_label());
    println!("   button_var.is_button(): {}", button_var.is_button());
    println!("   collision_var.is_collision_shape2d(): {}", collision_var.is_collision_shape2d());
    println!("   area_var.is_area2d(): {}", area_var.is_area2d());
    println!("   body_var.is_rigid_body2d(): {}", body_var.is_rigid_body2d());

    // Use in collections
    let mut node_array = Array::new();
    node_array.push_back(sprite_var);
    node_array.push_back(label_var);
    node_array.push_back(button_var);
    node_array.push_back(collision_var);
    node_array.push_back(area_var);
    node_array.push_back(body_var);

    println!("   Array with {} node variants", node_array.size());

    let mut node_dict = Dictionary::new();
    node_dict.set(Variant::from("graphics"), Variant::from("sprite"));
    node_dict.set(Variant::from("ui"), Variant::from("button"));
    node_dict.set(Variant::from("physics"), Variant::from("body"));

    println!("   Dictionary with {} node categories", node_dict.size());
    println!();
}

fn demo_game_scene() {
    println!("4. Complete Game Scene:");

    // Create a complete game scene
    let mut game_scene = Node::new("GameScene");

    // Graphics layer
    let mut graphics_layer = Node2D::new("Graphics");

    let mut world_sprite = Sprite2D::new("WorldBackground");
    world_sprite.set_texture(Some("res://world/background.png".to_string()));
    world_sprite.set_centered(true);
    world_sprite.base_mut().set_position(Vector2::new(400.0, 300.0));

    let mut character_sprite = Sprite2D::new("Character");
    character_sprite.set_texture(Some("res://characters/hero.png".to_string()));
    character_sprite.set_region_enabled(true);
    character_sprite.set_region_rect(Rect2::new(0.0, 0.0, 32.0, 48.0));
    character_sprite.base_mut().set_position(Vector2::new(200.0, 400.0));

    // Physics layer
    let mut physics_layer = Node::new("Physics");

    let mut character_body = RigidBody2D::new("CharacterBody");
    character_body.set_mode(RigidBodyMode::Character);
    character_body.set_mass(60.0);
    character_body.base_mut().set_position(Vector2::new(200.0, 400.0));

    let mut character_shape = CollisionShape2D::new("CharacterShape");
    character_shape.set_shape(Some(Shape2D::Rectangle {
        size: Vector2::new(24.0, 40.0)
    }));

    // UI layer
    let mut ui_layer = Node::new("UI");

    let mut health_label = Label::new("HealthLabel");
    health_label.set_text(GodotString::from("Health: 100/100"));
    health_label.set_font_size(16);
    health_label.set_font_color(Color::new(0.0, 1.0, 0.0, 1.0));
    health_label.set_size(Vector2::new(150.0, 25.0));
    health_label.base_mut().set_position(Vector2::new(10.0, 10.0));

    let mut pause_button = Button::new("PauseButton");
    pause_button.set_text(GodotString::from("Pause"));
    pause_button.set_toggle_mode(true);
    pause_button.set_size(Vector2::new(80.0, 30.0));
    pause_button.base_mut().set_position(Vector2::new(700.0, 10.0));

    // Build the complete scene
    graphics_layer.base_mut().add_child(world_sprite.base().base().clone());
    graphics_layer.base_mut().add_child(character_sprite.base().base().clone());

    physics_layer.add_child(character_body.base().base().clone());
    physics_layer.add_child(character_shape.base().base().clone());

    ui_layer.add_child(health_label.base().base().clone());
    ui_layer.add_child(pause_button.base().base().clone());

    game_scene.add_child(graphics_layer.base().clone());
    game_scene.add_child(physics_layer);
    game_scene.add_child(ui_layer);

    println!("   Complete game scene structure:");
    print_scene_tree(&game_scene, 0);

    // Demonstrate node finding
    if let Some(character) = game_scene.get_node("Graphics/Character") {
        println!("   Found character sprite: {}", character.get_name());
    }

    if let Some(health) = game_scene.get_node("UI/HealthLabel") {
        println!("   Found health label: {}", health.get_name());
    }

    println!("   Total nodes in scene: {}", count_nodes_recursive(&game_scene));
    println!();
}

fn print_scene_tree(node: &Node, depth: usize) {
    let indent = "  ".repeat(depth);
    println!("{}├─ {}", indent, node.get_name());

    let children = node.get_children();
    for child in children {
        print_scene_tree(&child, depth + 1);
    }
}

fn count_nodes_recursive(node: &Node) -> usize {
    let mut count = 1; // Count this node
    let children = node.get_children();
    for child in children {
        count += count_nodes_recursive(&child);
    }
    count
}
