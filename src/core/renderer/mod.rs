//! Graphics rendering system for Verturion game engine.
//!
//! This module provides comprehensive graphics rendering capabilities for the Verturion
//! game engine, including window management, 2D rendering, UI element rendering, and
//! integration with the node system. It uses wgpu for cross-platform graphics rendering
//! with support for modern graphics APIs.

pub mod window;
pub mod renderer;
pub mod ui_renderer;
pub mod text_renderer;
pub mod shape_renderer;
pub mod camera;
pub mod font_manager;

// Re-export main types - Complete API for external users
#[allow(unused_imports)]
pub use window::{Window, WindowConfig};
#[allow(unused_imports)]
pub use renderer::{Renderer, RenderContext};
#[allow(unused_imports)]
pub use ui_renderer::UIRenderer;
#[allow(unused_imports)]
pub use text_renderer::TextRenderer;
#[allow(unused_imports)]
pub use shape_renderer::ShapeRenderer;
#[allow(unused_imports)]
pub use camera::Camera2DRenderer;
#[allow(unused_imports)]
pub use font_manager::{FontManager, FontInfo, FontFormat};
