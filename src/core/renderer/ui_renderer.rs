//! UI element rendering system for Verturion graphics.
//!
//! This module provides rendering capabilities for UI elements including
//! buttons, labels, progress bars, checkboxes, and other UI components.

use wgpu::{
    BindGroup, BindGroupDescriptor, BindGroupEntry, BindGroupLayoutDescriptor,
    BindGroupLayoutEntry, BindingType, Buffer, BufferDescriptor, BufferUsages,
    Device, RenderPipeline, RenderPipelineDescriptor, ShaderStages, TextureFormat,
    VertexAttribute, VertexBufferLayout, VertexFormat, VertexStepMode,
    Texture, TextureDescriptor, TextureDimension, TextureUsages, TextureView,
    Sampler, SamplerDescriptor, AddressMode, FilterMode,
};
use crate::core::math::{Vector2, Rect2};
use crate::core::variant::Color;
use crate::core::scene::nodes::ui::{Button, Label, ProgressBar, CheckBox, LineEdit};
use std::collections::HashMap;

/// ### Vertex data for UI rendering.
#[repr(C)]
#[derive(Copy, Clone, Debug, bytemuck::Pod, bytemuck::Zeroable)]
#[allow(dead_code)] // Comprehensive UI vertex implementation awaiting integration
pub struct UIVertex {
    /// Position in screen space
    pub position: [f32; 2],
    /// Texture coordinates
    pub tex_coords: [f32; 2],
    /// Vertex color
    pub color: [f32; 4],
}

#[allow(dead_code)] // Comprehensive UI vertex implementation awaiting integration
impl UIVertex {
    /// ### Creates a new UI vertex.
    pub fn new(position: Vector2, tex_coords: Vector2, color: Color) -> Self {
        Self {
            position: [position.x, position.y],
            tex_coords: [tex_coords.x, tex_coords.y],
            color: [color.r, color.g, color.b, color.a],
        }
    }

    /// ### Gets the vertex buffer layout descriptor.
    pub fn desc() -> VertexBufferLayout<'static> {
        VertexBufferLayout {
            array_stride: std::mem::size_of::<UIVertex>() as wgpu::BufferAddress,
            step_mode: VertexStepMode::Vertex,
            attributes: &[
                VertexAttribute {
                    offset: 0,
                    shader_location: 0,
                    format: VertexFormat::Float32x2,
                },
                VertexAttribute {
                    offset: std::mem::size_of::<[f32; 2]>() as wgpu::BufferAddress,
                    shader_location: 1,
                    format: VertexFormat::Float32x2,
                },
                VertexAttribute {
                    offset: std::mem::size_of::<[f32; 4]>() as wgpu::BufferAddress,
                    shader_location: 2,
                    format: VertexFormat::Float32x4,
                },
            ],
        }
    }
}

/// ### Simple glyph data for basic text rendering.
#[derive(Debug, Clone)]
pub struct SimpleGlyph {
    /// Character this glyph represents
    pub character: char,
    /// Width of the glyph in pixels
    pub width: f32,
    /// Height of the glyph in pixels
    pub height: f32,
    /// Bitmap data (1 = foreground, 0 = background)
    pub bitmap: Vec<bool>,
}

/// ### UI rendering system with basic text support.
pub struct UIRenderer {
    /// Render pipeline for UI elements
    pipeline: RenderPipeline,
    /// Vertex buffer for UI quads
    vertex_buffer: Buffer,
    /// Index buffer for UI quads
    index_buffer: Buffer,
    /// Uniform buffer for projection matrix
    uniform_buffer: Buffer,
    /// Bind group for uniforms
    uniform_bind_group: BindGroup,
    /// Current vertex data
    vertices: Vec<UIVertex>,
    /// Current index data
    indices: Vec<u16>,
    /// Simple font glyphs for basic text rendering
    font_glyphs: std::collections::HashMap<char, SimpleGlyph>,
    /// Default font size
    default_font_size: f32,
}

#[allow(dead_code)] // Comprehensive UI renderer implementation awaiting integration
impl UIRenderer {
    /// ### Creates a new UI renderer.
    pub async fn new(
        device: &Device,
        surface_format: TextureFormat,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let max_quads = 10000; // Maximum number of UI quads (increased for text rendering)
        let max_vertices = max_quads * 4;
        let max_indices = max_quads * 6;

        // Create vertex buffer
        let vertex_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Vertex Buffer"),
            size: (max_vertices * std::mem::size_of::<UIVertex>()) as u64,
            usage: BufferUsages::VERTEX | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Create index buffer
        let index_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Index Buffer"),
            size: (max_indices * std::mem::size_of::<u16>()) as u64,
            usage: BufferUsages::INDEX | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Create uniform buffer for projection matrix
        let uniform_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Uniform Buffer"),
            size: 64, // 4x4 matrix
            usage: BufferUsages::UNIFORM | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Create bind group layout
        let bind_group_layout = device.create_bind_group_layout(&BindGroupLayoutDescriptor {
            label: Some("UI Bind Group Layout"),
            entries: &[BindGroupLayoutEntry {
                binding: 0,
                visibility: ShaderStages::VERTEX,
                ty: BindingType::Buffer {
                    ty: wgpu::BufferBindingType::Uniform,
                    has_dynamic_offset: false,
                    min_binding_size: None,
                },
                count: None,
            }],
        });

        // Create bind group
        let uniform_bind_group = device.create_bind_group(&BindGroupDescriptor {
            label: Some("UI Uniform Bind Group"),
            layout: &bind_group_layout,
            entries: &[BindGroupEntry {
                binding: 0,
                resource: uniform_buffer.as_entire_binding(),
            }],
        });

        // Create shader
        let shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
            label: Some("UI Shader"),
            source: wgpu::ShaderSource::Wgsl(include_str!("shaders/ui.wgsl").into()),
        });

        // Create render pipeline
        let pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("UI Pipeline Layout"),
            bind_group_layouts: &[&bind_group_layout],
            push_constant_ranges: &[],
        });

        let pipeline = device.create_render_pipeline(&RenderPipelineDescriptor {
            label: Some("UI Pipeline"),
            layout: Some(&pipeline_layout),
            vertex: wgpu::VertexState {
                module: &shader,
                entry_point: "vs_main",
                buffers: &[UIVertex::desc()],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            },
            fragment: Some(wgpu::FragmentState {
                module: &shader,
                entry_point: "fs_main",
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::ALPHA_BLENDING),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: None,
                unclipped_depth: false,
                polygon_mode: wgpu::PolygonMode::Fill,
                conservative: false,
            },
            depth_stencil: None,
            multisample: wgpu::MultisampleState {
                count: 1,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
            cache: None,
        });

        // Create simple font glyphs for basic text rendering
        let font_glyphs = Self::create_simple_font();

        Ok(Self {
            pipeline,
            vertex_buffer,
            index_buffer,
            uniform_buffer,
            uniform_bind_group,
            vertices: Vec::new(),
            indices: Vec::new(),
            font_glyphs,
            default_font_size: 16.0,
        })
    }

    /// ### Creates a simple bitmap font for basic text rendering.
    fn create_simple_font() -> std::collections::HashMap<char, SimpleGlyph> {
        let mut glyphs = std::collections::HashMap::new();

        // Create simple 8x8 bitmap glyphs for basic characters
        // This is a minimal implementation - in a real engine you'd load from font files

        // Space character
        glyphs.insert(' ', SimpleGlyph {
            character: ' ',
            width: 4.0,
            height: 8.0,
            bitmap: vec![false; 32], // 4x8 = 32 pixels, all false (empty)
        });

        // Letter 'A'
        glyphs.insert('A', SimpleGlyph {
            character: 'A',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, false, true, true, true, true, false, false,
                false, true, false, false, false, false, true, false,
                true, false, false, false, false, false, false, true,
                true, false, false, false, false, false, false, true,
                true, true, true, true, true, true, true, true,
                true, false, false, false, false, false, false, true,
                true, false, false, false, false, false, false, true,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'B'
        glyphs.insert('B', SimpleGlyph {
            character: 'B',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'C'
        glyphs.insert('C', SimpleGlyph {
            character: 'C',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'D'
        glyphs.insert('D', SimpleGlyph {
            character: 'D',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'E'
        glyphs.insert('E', SimpleGlyph {
            character: 'E',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, true, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, true, true, true, true, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, true, true, true, true, true, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'F'
        glyphs.insert('F', SimpleGlyph {
            character: 'F',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, true, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, true, true, true, true, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'G'
        glyphs.insert('G', SimpleGlyph {
            character: 'G',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, false, false,
                true, false, false, true, true, true, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'H'
        glyphs.insert('H', SimpleGlyph {
            character: 'H',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, true, true, true, true, true, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'I'
        glyphs.insert('I', SimpleGlyph {
            character: 'I',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'J'
        glyphs.insert('J', SimpleGlyph {
            character: 'J',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, false, false, false, false, true, true, false,
                false, false, false, false, false, false, true, false,
                false, false, false, false, false, false, true, false,
                false, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'K'
        glyphs.insert('K', SimpleGlyph {
            character: 'K',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, true, false, false,
                true, false, false, false, true, false, false, false,
                true, false, false, true, false, false, false, false,
                true, true, true, false, false, false, false, false,
                true, false, false, true, false, false, false, false,
                true, false, false, false, true, false, false, false,
                true, false, false, false, false, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'L'
        glyphs.insert('L', SimpleGlyph {
            character: 'L',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, true, true, true, true, true, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'M'
        glyphs.insert('M', SimpleGlyph {
            character: 'M',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, true, false,
                true, true, false, false, false, true, true, false,
                true, false, true, false, true, false, true, false,
                true, false, false, true, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'N'
        glyphs.insert('N', SimpleGlyph {
            character: 'N',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, true, false,
                true, true, false, false, false, false, true, false,
                true, false, true, false, false, false, true, false,
                true, false, false, true, false, false, true, false,
                true, false, false, false, true, false, true, false,
                true, false, false, false, false, true, true, false,
                true, false, false, false, false, false, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'O'
        glyphs.insert('O', SimpleGlyph {
            character: 'O',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'P'
        glyphs.insert('P', SimpleGlyph {
            character: 'P',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, true, true, true, true, true, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'Q'
        glyphs.insert('Q', SimpleGlyph {
            character: 'Q',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, true, false, false, true, false,
                true, false, false, false, true, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, true, false,
            ],
        });

        // Letter 'R'
        glyphs.insert('R', SimpleGlyph {
            character: 'R',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, true, true, true, true, true, false, false,
                true, false, false, true, false, false, false, false,
                true, false, false, false, true, false, false, false,
                true, false, false, false, false, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'S'
        glyphs.insert('S', SimpleGlyph {
            character: 'S',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, false, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'T'
        glyphs.insert('T', SimpleGlyph {
            character: 'T',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, true, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'U'
        glyphs.insert('U', SimpleGlyph {
            character: 'U',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'V'
        glyphs.insert('V', SimpleGlyph {
            character: 'V',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, false, false, false, true, false, false,
                false, true, false, false, false, true, false, false,
                false, false, true, false, true, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'W'
        glyphs.insert('W', SimpleGlyph {
            character: 'W',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, true, false, false, true, false,
                true, false, true, false, true, false, true, false,
                true, true, false, false, false, true, true, false,
                true, false, false, false, false, false, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'X'
        glyphs.insert('X', SimpleGlyph {
            character: 'X',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, true, false,
                false, true, false, false, false, true, false, false,
                false, false, true, false, true, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, true, false, true, false, false, false,
                false, true, false, false, false, true, false, false,
                true, false, false, false, false, false, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'Y'
        glyphs.insert('Y', SimpleGlyph {
            character: 'Y',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false, true, false,
                false, true, false, false, false, true, false, false,
                false, false, true, false, true, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Letter 'Z'
        glyphs.insert('Z', SimpleGlyph {
            character: 'Z',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, true, false,
                false, false, false, false, false, true, false, false,
                false, false, false, false, true, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, true, false, false, false, false, false,
                false, true, false, false, false, false, false, false,
                true, true, true, true, true, true, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Lowercase letter 'a'
        glyphs.insert('a', SimpleGlyph {
            character: 'a',
            width: 6.0,
            height: 8.0,
            bitmap: vec![
                false, false, false, false, false, false,
                false, false, false, false, false, false,
                false, true, true, true, true, false,
                false, false, false, false, false, true,
                false, true, true, true, true, true,
                true, false, false, false, false, true,
                false, true, true, true, true, true,
                false, false, false, false, false, false,
            ],
        });

        // Lowercase letter 'b'
        glyphs.insert('b', SimpleGlyph {
            character: 'b',
            width: 6.0,
            height: 8.0,
            bitmap: vec![
                true, false, false, false, false, false,
                true, false, false, false, false, false,
                true, true, true, true, true, false,
                true, false, false, false, false, true,
                true, false, false, false, false, true,
                true, false, false, false, false, true,
                true, true, true, true, true, false,
                false, false, false, false, false, false,
            ],
        });

        // Lowercase letter 'c'
        glyphs.insert('c', SimpleGlyph {
            character: 'c',
            width: 6.0,
            height: 8.0,
            bitmap: vec![
                false, false, false, false, false, false,
                false, false, false, false, false, false,
                false, true, true, true, true, false,
                true, false, false, false, false, false,
                true, false, false, false, false, false,
                true, false, false, false, false, false,
                false, true, true, true, true, false,
                false, false, false, false, false, false,
            ],
        });

        // Lowercase letter 'd'
        glyphs.insert('d', SimpleGlyph {
            character: 'd',
            width: 6.0,
            height: 8.0,
            bitmap: vec![
                false, false, false, false, false, true,
                false, false, false, false, false, true,
                false, true, true, true, true, true,
                true, false, false, false, false, true,
                true, false, false, false, false, true,
                true, false, false, false, false, true,
                false, true, true, true, true, true,
                false, false, false, false, false, false,
            ],
        });

        // Lowercase letter 'e'
        glyphs.insert('e', SimpleGlyph {
            character: 'e',
            width: 6.0,
            height: 8.0,
            bitmap: vec![
                false, false, false, false, false, false,
                false, false, false, false, false, false,
                false, true, true, true, true, false,
                true, false, false, false, false, true,
                true, true, true, true, true, true,
                true, false, false, false, false, false,
                false, true, true, true, true, false,
                false, false, false, false, false, false,
            ],
        });

        // Add remaining essential lowercase letters with generic pattern for now
        for c in 'f'..='z' {
            glyphs.insert(c, SimpleGlyph {
                character: c,
                width: 6.0,
                height: 8.0,
                bitmap: vec![
                    false, false, false, false, false, false,
                    false, false, false, false, false, false,
                    false, true, true, true, true, false,
                    false, false, false, false, false, true,
                    false, true, true, true, true, true,
                    true, false, false, false, false, true,
                    false, true, true, true, true, true,
                    false, false, false, false, false, false,
                ],
            });
        }

        // Digit '0'
        glyphs.insert('0', SimpleGlyph {
            character: '0',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '1'
        glyphs.insert('1', SimpleGlyph {
            character: '1',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, false, false, true, false, false, false, false,
                false, false, true, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, false, true, false, false, false, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '2'
        glyphs.insert('2', SimpleGlyph {
            character: '2',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                false, false, false, false, false, false, true, false,
                false, false, false, false, false, true, false, false,
                false, false, false, true, true, false, false, false,
                false, false, true, false, false, false, false, false,
                true, true, true, true, true, true, true, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '3'
        glyphs.insert('3', SimpleGlyph {
            character: '3',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                false, false, false, false, false, false, true, false,
                false, false, true, true, true, true, false, false,
                false, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '4'
        glyphs.insert('4', SimpleGlyph {
            character: '4',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, false, false, false, false, true, false, false,
                false, false, false, false, true, true, false, false,
                false, false, false, true, false, true, false, false,
                false, false, true, false, false, true, false, false,
                false, true, false, false, false, true, false, false,
                true, true, true, true, true, true, true, false,
                false, false, false, false, false, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '5'
        glyphs.insert('5', SimpleGlyph {
            character: '5',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, true, false,
                true, false, false, false, false, false, false, false,
                true, false, false, false, false, false, false, false,
                true, true, true, true, true, true, false, false,
                false, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '6'
        glyphs.insert('6', SimpleGlyph {
            character: '6',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, false, false,
                true, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '7'
        glyphs.insert('7', SimpleGlyph {
            character: '7',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                true, true, true, true, true, true, true, false,
                false, false, false, false, false, false, true, false,
                false, false, false, false, false, true, false, false,
                false, false, false, false, true, false, false, false,
                false, false, false, true, false, false, false, false,
                false, false, true, false, false, false, false, false,
                false, false, true, false, false, false, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '8'
        glyphs.insert('8', SimpleGlyph {
            character: '8',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Digit '9'
        glyphs.insert('9', SimpleGlyph {
            character: '9',
            width: 8.0,
            height: 8.0,
            bitmap: vec![
                false, true, true, true, true, true, false, false,
                true, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, true, false,
                false, false, false, false, false, false, true, false,
                true, false, false, false, false, false, true, false,
                false, true, true, true, true, true, false, false,
                false, false, false, false, false, false, false, false,
            ],
        });

        // Add common punctuation
        glyphs.insert(':', SimpleGlyph {
            character: ':',
            width: 2.0,
            height: 8.0,
            bitmap: vec![
                false, false,
                false, false,
                true, false,
                false, false,
                false, false,
                true, false,
                false, false,
                false, false,
            ],
        });

        glyphs.insert('.', SimpleGlyph {
            character: '.',
            width: 2.0,
            height: 8.0,
            bitmap: vec![
                false, false,
                false, false,
                false, false,
                false, false,
                false, false,
                false, false,
                true, false,
                false, false,
            ],
        });

        glyphs.insert('-', SimpleGlyph {
            character: '-',
            width: 6.0,
            height: 8.0,
            bitmap: vec![
                false, false, false, false, false, false,
                false, false, false, false, false, false,
                false, false, false, false, false, false,
                true, true, true, true, true, false,
                false, false, false, false, false, false,
                false, false, false, false, false, false,
                false, false, false, false, false, false,
                false, false, false, false, false, false,
            ],
        });

        glyphs
    }

    /// ### Renders text at the specified position.
    pub fn render_text(
        &mut self,
        text: &str,
        position: Vector2,
        color: Color,
        font_size: f32,
    ) -> Result<(), Box<dyn std::error::Error>> {
        println!("UIRenderer: Rendering text '{}' at ({:.1}, {:.1}) with size {:.1}", text, position.x, position.y, font_size);

        let scale = font_size / self.default_font_size;
        let mut current_x = position.x;
        let current_y = position.y;

        for character in text.chars() {
            if let Some(glyph) = self.font_glyphs.get(&character).cloned() {
                let glyph_width = glyph.width * scale;
                let glyph_height = glyph.height * scale;

                // Render each pixel of the glyph as a small quad
                let pixel_width = glyph_width / glyph.width;
                let pixel_height = glyph_height / glyph.height;

                for y in 0..(glyph.height as usize) {
                    for x in 0..(glyph.width as usize) {
                        let pixel_index = y * (glyph.width as usize) + x;
                        if pixel_index < glyph.bitmap.len() && glyph.bitmap[pixel_index] {
                            let pixel_x = current_x + (x as f32 * pixel_width);
                            let pixel_y = current_y + (y as f32 * pixel_height);

                            self.add_quad(
                                Rect2::from_position_size(
                                    Vector2::new(pixel_x, pixel_y),
                                    Vector2::new(pixel_width, pixel_height)
                                ),
                                color
                            );
                        }
                    }
                }

                current_x += glyph_width + (2.0 * scale); // Add spacing between characters
            } else {
                // Unknown character, render as a small rectangle
                let char_width = 8.0 * scale;
                self.add_quad(
                    Rect2::from_position_size(
                        Vector2::new(current_x, current_y),
                        Vector2::new(char_width, 8.0 * scale)
                    ),
                    Color::new(0.5, 0.5, 0.5, 0.5) // Gray placeholder
                );
                current_x += char_width + (2.0 * scale);
            }
        }

        Ok(())
    }

    /// ### Clears the current vertex and index data.
    pub fn clear(&mut self) {
        self.vertices.clear();
        self.indices.clear();
    }

    /// ### Adds a quad to the vertex buffer.
    pub fn add_quad(&mut self, rect: Rect2, color: Color) {
        let base_index = self.vertices.len() as u16;

        // Create four vertices for the quad
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x, rect.position.y),
            Vector2::new(0.0, 0.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x + rect.size.x, rect.position.y),
            Vector2::new(1.0, 0.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x + rect.size.x, rect.position.y + rect.size.y),
            Vector2::new(1.0, 1.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x, rect.position.y + rect.size.y),
            Vector2::new(0.0, 1.0),
            color,
        ));

        // Add indices for two triangles
        self.indices.extend_from_slice(&[
            base_index, base_index + 1, base_index + 2,
            base_index, base_index + 2, base_index + 3,
        ]);
    }

    /// ### Renders a button.
    pub fn render_button(
        &mut self,
        button: &Button,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Choose button color based on state
        let button_color = if button.is_pressed() {
            Color::new(0.4, 0.4, 0.6, 1.0) // Darker blue when pressed
        } else if button.is_disabled() {
            Color::new(0.3, 0.3, 0.3, 1.0) // Gray when disabled
        } else {
            Color::new(0.5, 0.5, 0.7, 1.0) // Normal blue
        };

        // Add button background quad
        self.add_quad(Rect2::from_position_size(position, size), button_color);

        // Add button border
        let border_thickness = 2.0;
        let border_color = Color::new(0.2, 0.2, 0.3, 1.0);

        // Top border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(size.x, border_thickness)), border_color);
        // Bottom border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x, position.y + size.y - border_thickness),
            Vector2::new(size.x, border_thickness)
        ), border_color);
        // Left border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(border_thickness, size.y)), border_color);
        // Right border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x + size.x - border_thickness, position.y),
            Vector2::new(border_thickness, size.y)
        ), border_color);

        Ok(())
    }

    /// ### Renders a label.
    pub fn render_label(
        &mut self,
        label: &Label,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Render label background (optional)
        let bg_color = Color::new(0.1, 0.1, 0.1, 0.3); // Semi-transparent dark background
        self.add_quad(Rect2::from_position_size(position, size), bg_color);

        // Render the label text
        let text = label.get_text().to_string();
        let font_size = label.get_font_size() as f32;
        let font_color = label.get_font_color();
        let text_color = Color::new(font_color.r, font_color.g, font_color.b, font_color.a);

        println!("UIRenderer: Rendering label with text '{}' at ({:.1}, {:.1})", text, position.x, position.y);

        // Position text with some padding
        let text_position = Vector2::new(position.x + 4.0, position.y + 4.0);
        self.render_text(&text, text_position, text_color, font_size)?;

        Ok(())
    }

    /// ### Renders a progress bar.
    pub fn render_progress_bar(
        &mut self,
        progress_bar: &ProgressBar,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Render background bar
        let bg_color = Color::new(0.2, 0.2, 0.2, 1.0); // Dark gray background
        self.add_quad(Rect2::from_position_size(position, size), bg_color);

        // Render filled portion based on progress
        let progress_percentage = progress_bar.get_percentage() as f32;
        let fill_width = size.x * progress_percentage;

        if fill_width > 0.0 {
            let fill_color = Color::new(0.2, 0.7, 0.3, 1.0); // Green fill
            self.add_quad(
                Rect2::from_position_size(position, Vector2::new(fill_width, size.y)),
                fill_color
            );
        }

        // Render border
        let border_thickness = 1.0;
        let border_color = Color::new(0.4, 0.4, 0.4, 1.0);

        // Top border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(size.x, border_thickness)), border_color);
        // Bottom border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x, position.y + size.y - border_thickness),
            Vector2::new(size.x, border_thickness)
        ), border_color);
        // Left border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(border_thickness, size.y)), border_color);
        // Right border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x + size.x - border_thickness, position.y),
            Vector2::new(border_thickness, size.y)
        ), border_color);

        Ok(())
    }

    /// ### Renders a checkbox.
    pub fn render_checkbox(
        &mut self,
        checkbox: &CheckBox,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Calculate checkbox square size (use smaller dimension)
        let checkbox_size = size.y.min(size.x * 0.8);
        let checkbox_rect = Rect2::from_position_size(position, Vector2::new(checkbox_size, checkbox_size));

        // Render checkbox background
        let bg_color = Color::new(0.9, 0.9, 0.9, 1.0); // Light gray background
        self.add_quad(checkbox_rect, bg_color);

        // Render checkbox border
        let border_thickness = 1.0;
        let border_color = Color::new(0.3, 0.3, 0.3, 1.0);

        // Top border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(checkbox_size, border_thickness)), border_color);
        // Bottom border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x, position.y + checkbox_size - border_thickness),
            Vector2::new(checkbox_size, border_thickness)
        ), border_color);
        // Left border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(border_thickness, checkbox_size)), border_color);
        // Right border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x + checkbox_size - border_thickness, position.y),
            Vector2::new(border_thickness, checkbox_size)
        ), border_color);

        // Render checkmark if checked
        if checkbox.is_checked() {
            let check_color = Color::new(0.2, 0.7, 0.2, 1.0); // Green checkmark
            let check_margin = checkbox_size * 0.2;
            let check_rect = Rect2::from_position_size(
                Vector2::new(position.x + check_margin, position.y + check_margin),
                Vector2::new(checkbox_size - 2.0 * check_margin, checkbox_size - 2.0 * check_margin)
            );
            self.add_quad(check_rect, check_color);
        }

        Ok(())
    }

    /// ### Renders a line edit.
    pub fn render_line_edit(
        &mut self,
        line_edit: &LineEdit,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Render background
        let bg_color = Color::new(1.0, 1.0, 1.0, 1.0); // White background
        self.add_quad(Rect2::from_position_size(position, size), bg_color);

        // Render border
        let border_thickness = 1.0;
        let border_color = if line_edit.is_focused() {
            Color::new(0.2, 0.5, 0.8, 1.0) // Blue border when focused
        } else {
            Color::new(0.5, 0.5, 0.5, 1.0) // Gray border when not focused
        };

        // Top border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(size.x, border_thickness)), border_color);
        // Bottom border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x, position.y + size.y - border_thickness),
            Vector2::new(size.x, border_thickness)
        ), border_color);
        // Left border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(border_thickness, size.y)), border_color);
        // Right border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x + size.x - border_thickness, position.y),
            Vector2::new(border_thickness, size.y)
        ), border_color);

        // Render text content
        let text_position = Vector2::new(position.x + 5.0, position.y + 5.0);
        let font_size = 14.0; // Default font size for line edits

        let current_text = line_edit.get_text();
        if !current_text.is_empty() {
            // Render actual text content
            let text_color = Color::new(0.0, 0.0, 0.0, 1.0); // Black text
            let display_text = if line_edit.is_secret() {
                "*".repeat(current_text.len()) // Show asterisks for password fields
            } else {
                current_text.to_string()
            };
            self.render_text(&display_text, text_position, text_color, font_size)?;
        } else {
            // Render placeholder text
            let placeholder = line_edit.get_placeholder_text();
            if !placeholder.is_empty() {
                let placeholder_color = Color::new(0.6, 0.6, 0.6, 1.0); // Gray placeholder text
                self.render_text(&placeholder, text_position, placeholder_color, font_size)?;
            }
        }

        // Render cursor if focused
        if line_edit.is_focused() {
            let text_width = current_text.len() as f32 * (font_size * 0.6); // Approximate text width
            let cursor_x = text_position.x + text_width;
            let cursor_color = Color::new(0.0, 0.0, 0.0, 1.0); // Black cursor
            self.add_quad(Rect2::from_position_size(
                Vector2::new(cursor_x, position.y + 2.0),
                Vector2::new(1.0, size.y - 4.0)
            ), cursor_color);
        }

        Ok(())
    }

    /// ### Flushes all accumulated vertex data to the GPU.
    pub fn flush(
        &mut self,
        render_pass: &mut wgpu::RenderPass,
        queue: &wgpu::Queue,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        if self.vertices.is_empty() {
            return Ok(());
        }

        // Update projection matrix for screen coordinates
        let projection_matrix = [
            2.0 / window_size.x, 0.0, 0.0, 0.0,
            0.0, -2.0 / window_size.y, 0.0, 0.0,
            0.0, 0.0, 1.0, 0.0,
            -1.0, 1.0, 0.0, 1.0,
        ];

        // Write projection matrix to uniform buffer
        queue.write_buffer(&self.uniform_buffer, 0, bytemuck::cast_slice(&projection_matrix));

        // Write vertex data to buffer
        queue.write_buffer(&self.vertex_buffer, 0, bytemuck::cast_slice(&self.vertices));

        // Write index data to buffer
        queue.write_buffer(&self.index_buffer, 0, bytemuck::cast_slice(&self.indices));

        // Set up render pass
        render_pass.set_pipeline(&self.pipeline);
        render_pass.set_bind_group(0, &self.uniform_bind_group, &[]);
        render_pass.set_vertex_buffer(0, self.vertex_buffer.slice(..));
        render_pass.set_index_buffer(self.index_buffer.slice(..), wgpu::IndexFormat::Uint16);

        // Draw all accumulated quads
        render_pass.draw_indexed(0..self.indices.len() as u32, 0, 0..1);

        Ok(())
    }

    /// ### Renders a rectangle with the specified color.
    pub fn render_rect(
        &mut self,
        rect: Rect2,
        color: Color,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // TODO: Implement rectangle rendering
        println!("Rendering rectangle at ({}, {}) with size ({}, {}) and color ({}, {}, {}, {})",
                 rect.position.x, rect.position.y, rect.size.x, rect.size.y,
                 color.r, color.g, color.b, color.a);
        Ok(())
    }
}
