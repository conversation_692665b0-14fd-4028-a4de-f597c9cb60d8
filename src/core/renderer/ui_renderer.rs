//! Modern UI element rendering system for Verturion graphics.
//!
//! This module provides high-performance rendering capabilities for UI elements including
//! buttons, labels, progress bars, checkboxes, and other UI components with modern text rendering.

use wgpu::{
    BindGroup, BindGroupDescriptor, BindGroupEntry, BindGroupLayoutDescriptor,
    BindGroupLayoutEntry, BindingType, Buffer, BufferDescriptor, BufferUsages,
    Device, RenderPipeline, RenderPipelineDescriptor, ShaderStages, TextureFormat,
    VertexAttribute, VertexBufferLayout, VertexFormat, VertexStepMode,
    Texture, TextureDescriptor, TextureDimension, TextureUsages, TextureView,
    Sampler, SamplerDescriptor, AddressMode, FilterMode, Queue,
};
use crate::core::math::{Vector2, Rect2};
use crate::core::variant::Color;
use crate::core::scene::nodes::ui::{<PERSON><PERSON>, Label, ProgressBar, CheckBox, LineEdit};
use std::collections::HashMap;
use fontdue::{Font, FontSettings, LineMetrics};
use super::font_manager::{FontManager, FontInfo};

/// ### Vertex data for UI rendering.
#[repr(C)]
#[derive(Copy, Clone, Debug, bytemuck::Pod, bytemuck::Zeroable)]
pub struct UIVertex {
    /// Position in screen space
    pub position: [f32; 2],
    /// Texture coordinates
    pub tex_coords: [f32; 2],
    /// Vertex color
    pub color: [f32; 4],
}

impl UIVertex {
    /// ### Creates a new UI vertex.
    pub fn new(position: Vector2, tex_coords: Vector2, color: Color) -> Self {
        Self {
            position: [position.x, position.y],
            tex_coords: [tex_coords.x, tex_coords.y],
            color: [color.r, color.g, color.b, color.a],
        }
    }

    /// ### Gets the vertex buffer layout descriptor.
    pub fn desc() -> VertexBufferLayout<'static> {
        VertexBufferLayout {
            array_stride: std::mem::size_of::<UIVertex>() as wgpu::BufferAddress,
            step_mode: VertexStepMode::Vertex,
            attributes: &[
                VertexAttribute {
                    offset: 0,
                    shader_location: 0,
                    format: VertexFormat::Float32x2,
                },
                VertexAttribute {
                    offset: std::mem::size_of::<[f32; 2]>() as wgpu::BufferAddress,
                    shader_location: 1,
                    format: VertexFormat::Float32x2,
                },
                VertexAttribute {
                    offset: std::mem::size_of::<[f32; 4]>() as wgpu::BufferAddress,
                    shader_location: 2,
                    format: VertexFormat::Float32x4,
                },
            ],
        }
    }
}

/// ### Cached glyph information for efficient text rendering.
#[derive(Debug, Clone)]
pub struct CachedGlyph {
    /// Character this glyph represents
    pub character: char,
    /// Font size used for this glyph
    pub font_size: u32,
    /// Position in texture atlas
    pub atlas_x: u32,
    pub atlas_y: u32,
    /// Glyph dimensions
    pub width: u32,
    pub height: u32,
    /// Bearing offsets for proper positioning
    pub bearing_x: i32,
    pub bearing_y: i32,
    /// Horizontal advance for next character
    pub advance: f32,
}

/// ### Pending glyph upload data for deferred GPU texture upload.
struct PendingGlyphUpload {
    atlas_x: u32,
    atlas_y: u32,
    width: u32,
    height: u32,
    bitmap: Vec<u8>,
}

/// ### Font atlas for efficient GPU text rendering.
pub struct FontAtlas {
    /// GPU texture containing all cached glyphs
    pub texture: Texture,
    pub texture_view: TextureView,
    pub sampler: Sampler,
    /// Atlas dimensions
    pub width: u32,
    pub height: u32,
    /// Current packing position
    pub current_x: u32,
    pub current_y: u32,
    pub row_height: u32,
}

/// ### Modern UI renderer with high-performance text rendering.
pub struct UIRenderer {
    // Core rendering components
    pipeline: RenderPipeline,
    vertex_buffer: Buffer,
    index_buffer: Buffer,
    uniform_buffer: Buffer,
    uniform_bind_group: BindGroup,
    vertices: Vec<UIVertex>,
    indices: Vec<u16>,

    // Modern text rendering system
    font: Font,
    font_atlas: FontAtlas,
    glyph_cache: HashMap<(char, u32), CachedGlyph>,
    pending_glyph_uploads: Vec<PendingGlyphUpload>,
    default_font_size: f32,
}

impl UIRenderer {
    /// ### Creates a new UI renderer with modern text support.
    pub async fn new(
        device: &Device,
        _queue: &Queue,
        surface_format: TextureFormat,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // Load default font (embedded or from system)
        let font_data = Self::load_default_font()?;
        let font = Font::from_bytes(font_data, FontSettings::default())?;

        // Create font atlas texture
        let atlas_size = 1024u32;
        let font_atlas = Self::create_font_atlas(device, atlas_size)?;

        // Create shader and pipeline
        let shader = device.create_shader_module(wgpu::ShaderModuleDescriptor {
            label: Some("UI Shader"),
            source: wgpu::ShaderSource::Wgsl(include_str!("shaders/ui.wgsl").into()),
        });

        // Create uniform buffer for projection matrix
        let uniform_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Uniform Buffer"),
            size: 64, // 4x4 matrix
            usage: BufferUsages::UNIFORM | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        // Create bind group layout
        let bind_group_layout = device.create_bind_group_layout(&BindGroupLayoutDescriptor {
            label: Some("UI Bind Group Layout"),
            entries: &[
                BindGroupLayoutEntry {
                    binding: 0,
                    visibility: ShaderStages::VERTEX,
                    ty: BindingType::Buffer {
                        ty: wgpu::BufferBindingType::Uniform,
                        has_dynamic_offset: false,
                        min_binding_size: None,
                    },
                    count: None,
                },
                BindGroupLayoutEntry {
                    binding: 1,
                    visibility: ShaderStages::FRAGMENT,
                    ty: BindingType::Texture {
                        multisampled: false,
                        view_dimension: wgpu::TextureViewDimension::D2,
                        sample_type: wgpu::TextureSampleType::Float { filterable: true },
                    },
                    count: None,
                },
                BindGroupLayoutEntry {
                    binding: 2,
                    visibility: ShaderStages::FRAGMENT,
                    ty: BindingType::Sampler(wgpu::SamplerBindingType::Filtering),
                    count: None,
                },
            ],
        });

        // Create bind group
        let uniform_bind_group = device.create_bind_group(&BindGroupDescriptor {
            label: Some("UI Bind Group"),
            layout: &bind_group_layout,
            entries: &[
                BindGroupEntry {
                    binding: 0,
                    resource: uniform_buffer.as_entire_binding(),
                },
                BindGroupEntry {
                    binding: 1,
                    resource: wgpu::BindingResource::TextureView(&font_atlas.texture_view),
                },
                BindGroupEntry {
                    binding: 2,
                    resource: wgpu::BindingResource::Sampler(&font_atlas.sampler),
                },
            ],
        });

        // Create render pipeline
        let pipeline_layout = device.create_pipeline_layout(&wgpu::PipelineLayoutDescriptor {
            label: Some("UI Pipeline Layout"),
            bind_group_layouts: &[&bind_group_layout],
            push_constant_ranges: &[],
        });

        let pipeline = device.create_render_pipeline(&RenderPipelineDescriptor {
            label: Some("UI Pipeline"),
            layout: Some(&pipeline_layout),
            vertex: wgpu::VertexState {
                module: &shader,
                entry_point: "vs_main",
                buffers: &[UIVertex::desc()],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            },
            fragment: Some(wgpu::FragmentState {
                module: &shader,
                entry_point: "fs_main",
                targets: &[Some(wgpu::ColorTargetState {
                    format: surface_format,
                    blend: Some(wgpu::BlendState::ALPHA_BLENDING),
                    write_mask: wgpu::ColorWrites::ALL,
                })],
                compilation_options: wgpu::PipelineCompilationOptions::default(),
            }),
            primitive: wgpu::PrimitiveState {
                topology: wgpu::PrimitiveTopology::TriangleList,
                strip_index_format: None,
                front_face: wgpu::FrontFace::Ccw,
                cull_mode: None,
                unclipped_depth: false,
                polygon_mode: wgpu::PolygonMode::Fill,
                conservative: false,
            },
            depth_stencil: None,
            multisample: wgpu::MultisampleState {
                count: 1,
                mask: !0,
                alpha_to_coverage_enabled: false,
            },
            multiview: None,
            cache: None,
        });

        // Create vertex and index buffers
        let vertex_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Vertex Buffer"),
            size: 65536, // 64KB initial size
            usage: BufferUsages::VERTEX | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        let index_buffer = device.create_buffer(&BufferDescriptor {
            label: Some("UI Index Buffer"),
            size: 32768, // 32KB initial size
            usage: BufferUsages::INDEX | BufferUsages::COPY_DST,
            mapped_at_creation: false,
        });

        Ok(Self {
            pipeline,
            vertex_buffer,
            index_buffer,
            uniform_buffer,
            uniform_bind_group,
            vertices: Vec::new(),
            indices: Vec::new(),
            font,
            font_atlas,
            glyph_cache: HashMap::new(),
            pending_glyph_uploads: Vec::new(),
            default_font_size: 16.0,
        })
    }

    /// ### Loads the default embedded font.
    fn load_default_font() -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // Try embedded font first
        if let Ok(font_data) = std::fs::read("assets/fonts/default.ttf") {
            println!("UIRenderer: Loaded embedded font from assets/fonts/default.ttf");
            return Ok(font_data);
        }

        println!("UIRenderer: Embedded font not found, trying system fonts...");

        // Try to load a system font as fallback
        #[cfg(target_os = "windows")]
        {
            let font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "C:/Windows/Fonts/segoeui.ttf",
            ];

            for path in &font_paths {
                if let Ok(font_data) = std::fs::read(path) {
                    println!("UIRenderer: Loaded system font from {}", path);
                    return Ok(font_data);
                }
            }
        }

        #[cfg(target_os = "linux")]
        {
            let font_paths = [
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/TTF/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                "/usr/share/fonts/TTF/LiberationSans-Regular.ttf",
                "/usr/share/fonts/truetype/ubuntu/Ubuntu-R.ttf",
                "/usr/share/fonts/TTF/Ubuntu-R.ttf",
                "/usr/share/fonts/noto/NotoSans-Regular.ttf",
                "/usr/share/fonts/TTF/NotoSans-Regular.ttf",
            ];

            for path in &font_paths {
                if let Ok(font_data) = std::fs::read(path) {
                    println!("UIRenderer: Loaded system font from {}", path);
                    return Ok(font_data);
                }
            }
        }

        #[cfg(target_os = "macos")]
        {
            let font_paths = [
                "/System/Library/Fonts/Arial.ttf",
                "/System/Library/Fonts/Helvetica.ttc",
                "/System/Library/Fonts/Times.ttc",
            ];

            for path in &font_paths {
                if let Ok(font_data) = std::fs::read(path) {
                    println!("UIRenderer: Loaded system font from {}", path);
                    return Ok(font_data);
                }
            }
        }

        // If no system font found, provide helpful error message
        Err(format!(
            "No suitable font found. Please:\n\
            1. Add a TTF font file to assets/fonts/default.ttf, or\n\
            2. Install system fonts (DejaVu, Liberation, Ubuntu, or Noto on Linux)\n\
            3. Ensure font files are readable by the application"
        ).into())
    }

    /// ### Creates a font atlas texture for GPU rendering.
    fn create_font_atlas(device: &Device, size: u32) -> Result<FontAtlas, Box<dyn std::error::Error>> {
        let texture = device.create_texture(&TextureDescriptor {
            label: Some("Font Atlas"),
            size: wgpu::Extent3d {
                width: size,
                height: size,
                depth_or_array_layers: 1,
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: TextureDimension::D2,
            format: TextureFormat::R8Unorm,
            usage: TextureUsages::TEXTURE_BINDING | TextureUsages::COPY_DST,
            view_formats: &[],
        });

        let texture_view = texture.create_view(&wgpu::TextureViewDescriptor::default());

        let sampler = device.create_sampler(&SamplerDescriptor {
            label: Some("Font Atlas Sampler"),
            address_mode_u: AddressMode::ClampToEdge,
            address_mode_v: AddressMode::ClampToEdge,
            address_mode_w: AddressMode::ClampToEdge,
            mag_filter: FilterMode::Linear,
            min_filter: FilterMode::Linear,
            mipmap_filter: FilterMode::Nearest,
            ..Default::default()
        });

        Ok(FontAtlas {
            texture,
            texture_view,
            sampler,
            width: size,
            height: size,
            current_x: 0,
            current_y: 0,
            row_height: 0,
        })
    }

    /// ### Adds a quad to the vertex buffer.
    pub fn add_quad(&mut self, rect: Rect2, color: Color) {
        let base_index = self.vertices.len() as u16;

        // Create four vertices for the quad with special texture coordinates for solid color
        // Use negative texture coordinates to signal solid color rendering
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x, rect.position.y),
            Vector2::new(-1.0, -1.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x + rect.size.x, rect.position.y),
            Vector2::new(-1.0, -1.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x + rect.size.x, rect.position.y + rect.size.y),
            Vector2::new(-1.0, -1.0),
            color,
        ));
        self.vertices.push(UIVertex::new(
            Vector2::new(rect.position.x, rect.position.y + rect.size.y),
            Vector2::new(-1.0, -1.0),
            color,
        ));

        // Add indices for two triangles
        self.indices.extend_from_slice(&[
            base_index, base_index + 1, base_index + 2,
            base_index, base_index + 2, base_index + 3,
        ]);
    }

    /// ### Renders text at the specified position using modern font rendering.
    pub fn render_text(
        &mut self,
        text: &str,
        position: Vector2,
        color: Color,
        font_size: f32,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Use default font size if none specified
        let effective_font_size = if font_size > 0.0 { font_size } else { self.default_font_size };

        println!("UIRenderer: Rendering text '{}' at ({:.1}, {:.1}) with size {:.1}", text, position.x, position.y, effective_font_size);

        let mut current_x = position.x;
        let font_size_u32 = effective_font_size as u32;

        // Get font line metrics for baseline calculation
        let line_metrics = self.font.horizontal_line_metrics(effective_font_size)
            .unwrap_or_else(|| {
                // Fallback if no line metrics available
                LineMetrics {
                    ascent: effective_font_size * 0.8,
                    descent: effective_font_size * 0.2,
                    line_gap: 0.0,
                    new_line_size: effective_font_size,
                }
            });

        // Calculate baseline position - position.y is the top of the text line
        // We need to add the ascent to get to the baseline
        let baseline_y = position.y + line_metrics.ascent;



        for character in text.chars() {
            // Get or cache the glyph
            let glyph = self.get_or_cache_glyph(character, font_size_u32)?;

            // Calculate glyph position relative to baseline
            // For consistent baseline alignment, all characters should have their bottoms
            // align to the same baseline position, regardless of individual bearing_y values.
            // We use the baseline_y as the common bottom alignment point for all glyphs.
            let glyph_x = current_x + glyph.bearing_x as f32;
            let glyph_y = baseline_y - glyph.height as f32;

            // Add glyph quad with texture coordinates
            if glyph.width > 0 && glyph.height > 0 {
                let atlas_u1 = glyph.atlas_x as f32 / self.font_atlas.width as f32;
                let atlas_v1 = glyph.atlas_y as f32 / self.font_atlas.height as f32;
                let atlas_u2 = (glyph.atlas_x + glyph.width) as f32 / self.font_atlas.width as f32;
                let atlas_v2 = (glyph.atlas_y + glyph.height) as f32 / self.font_atlas.height as f32;

                let base_index = self.vertices.len() as u16;

                self.vertices.push(UIVertex::new(
                    Vector2::new(glyph_x, glyph_y),
                    Vector2::new(atlas_u1, atlas_v1),
                    color,
                ));
                self.vertices.push(UIVertex::new(
                    Vector2::new(glyph_x + glyph.width as f32, glyph_y),
                    Vector2::new(atlas_u2, atlas_v1),
                    color,
                ));
                self.vertices.push(UIVertex::new(
                    Vector2::new(glyph_x + glyph.width as f32, glyph_y + glyph.height as f32),
                    Vector2::new(atlas_u2, atlas_v2),
                    color,
                ));
                self.vertices.push(UIVertex::new(
                    Vector2::new(glyph_x, glyph_y + glyph.height as f32),
                    Vector2::new(atlas_u1, atlas_v2),
                    color,
                ));

                self.indices.extend_from_slice(&[
                    base_index, base_index + 1, base_index + 2,
                    base_index, base_index + 2, base_index + 3,
                ]);
            }

            current_x += glyph.advance;
        }

        Ok(())
    }

    /// ### Gets or caches a glyph for the specified character and font size.
    fn get_or_cache_glyph(&mut self, character: char, font_size: u32) -> Result<CachedGlyph, Box<dyn std::error::Error>> {
        let key = (character, font_size);

        if let Some(glyph) = self.glyph_cache.get(&key) {
            return Ok(glyph.clone());
        }

        // Rasterize the glyph using fontdue
        let (metrics, bitmap) = self.font.rasterize(character, font_size as f32);

        // Find space in the atlas
        let (atlas_x, atlas_y) = self.find_atlas_space(metrics.width, metrics.height)?;

        // Store bitmap for later GPU upload during flush
        let glyph = CachedGlyph {
            character,
            font_size,
            atlas_x,
            atlas_y,
            width: metrics.width as u32,
            height: metrics.height as u32,
            bearing_x: metrics.xmin,
            bearing_y: metrics.ymin,
            advance: metrics.advance_width,
        };

        // Store the bitmap data for upload during flush
        if !bitmap.is_empty() && metrics.width > 0 && metrics.height > 0 {
            self.pending_glyph_uploads.push(PendingGlyphUpload {
                atlas_x,
                atlas_y,
                width: metrics.width as u32,
                height: metrics.height as u32,
                bitmap,
            });

            println!("UIRenderer: Queued glyph '{}' ({}x{}) for upload to atlas at ({}, {})",
                character, metrics.width, metrics.height, atlas_x, atlas_y);
        }

        self.glyph_cache.insert(key, glyph.clone());
        Ok(glyph)
    }

    /// ### Finds space in the font atlas for a new glyph.
    fn find_atlas_space(&mut self, width: usize, height: usize) -> Result<(u32, u32), Box<dyn std::error::Error>> {
        // Simple row-based packing algorithm
        if self.font_atlas.current_x + width as u32 > self.font_atlas.width {
            // Move to next row
            self.font_atlas.current_x = 0;
            self.font_atlas.current_y += self.font_atlas.row_height;
            self.font_atlas.row_height = 0;
        }

        if self.font_atlas.current_y + height as u32 > self.font_atlas.height {
            return Err("Font atlas is full".into());
        }

        let result = (self.font_atlas.current_x, self.font_atlas.current_y);
        self.font_atlas.current_x += width as u32;
        self.font_atlas.row_height = self.font_atlas.row_height.max(height as u32);

        Ok(result)
    }

    /// ### Clears all accumulated vertex data.
    pub fn clear(&mut self) {
        self.vertices.clear();
        self.indices.clear();
    }

    /// ### Processes pending glyph uploads to the GPU texture atlas.
    fn process_pending_glyph_uploads(&mut self, queue: &wgpu::Queue) -> Result<(), Box<dyn std::error::Error>> {
        for upload in self.pending_glyph_uploads.drain(..) {
            // Upload bitmap data to GPU texture atlas
            queue.write_texture(
                wgpu::ImageCopyTexture {
                    texture: &self.font_atlas.texture,
                    mip_level: 0,
                    origin: wgpu::Origin3d {
                        x: upload.atlas_x,
                        y: upload.atlas_y,
                        z: 0,
                    },
                    aspect: wgpu::TextureAspect::All,
                },
                &upload.bitmap,
                wgpu::ImageDataLayout {
                    offset: 0,
                    bytes_per_row: Some(upload.width),
                    rows_per_image: Some(upload.height),
                },
                wgpu::Extent3d {
                    width: upload.width,
                    height: upload.height,
                    depth_or_array_layers: 1,
                },
            );

            println!("UIRenderer: Uploaded glyph bitmap ({}x{}) to atlas at ({}, {})",
                upload.width, upload.height, upload.atlas_x, upload.atlas_y);
        }

        Ok(())
    }

    /// ### Sets the default font size for text rendering.
    pub fn set_default_font_size(&mut self, font_size: f32) {
        self.default_font_size = font_size.max(1.0); // Ensure minimum font size
    }

    /// ### Gets the current default font size.
    pub fn get_default_font_size(&self) -> f32 {
        self.default_font_size
    }

    /// ### Renders a button with modern styling.
    pub fn render_button(
        &mut self,
        button: &Button,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use crate::core::math::Rect2;

        // Choose button color based on state
        let button_color = if button.is_pressed() {
            Color::new(0.4, 0.4, 0.6, 1.0) // Darker blue when pressed
        } else if button.is_disabled() {
            Color::new(0.3, 0.3, 0.3, 1.0) // Gray when disabled
        } else {
            Color::new(0.5, 0.5, 0.7, 1.0) // Normal blue
        };

        // Add button background quad
        self.add_quad(Rect2::from_position_size(position, size), button_color);

        // Add button border
        let border_thickness = 2.0;
        let border_color = Color::new(0.2, 0.2, 0.3, 1.0);

        // Top border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(size.x, border_thickness)), border_color);
        // Bottom border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x, position.y + size.y - border_thickness),
            Vector2::new(size.x, border_thickness)
        ), border_color);
        // Left border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(border_thickness, size.y)), border_color);
        // Right border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x + size.x - border_thickness, position.y),
            Vector2::new(border_thickness, size.y)
        ), border_color);

        Ok(())
    }

    /// ### Renders a label with high-quality text.
    pub fn render_label(
        &mut self,
        label: &Label,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use crate::core::math::Rect2;

        // Render label background (optional)
        let bg_color = Color::new(0.1, 0.1, 0.1, 0.3); // Semi-transparent dark background
        self.add_quad(Rect2::from_position_size(position, size), bg_color);

        // Render the label text
        let text = label.get_text().to_string();
        let font_size = label.get_font_size() as f32;
        let font_color = label.get_font_color();
        let text_color = Color::new(font_color.r, font_color.g, font_color.b, font_color.a);

        println!("UIRenderer: Rendering label with text '{}' at ({:.1}, {:.1})", text, position.x, position.y);

        // Position text with some padding
        let text_position = Vector2::new(position.x + 4.0, position.y + 4.0);
        self.render_text(&text, text_position, text_color, font_size)?;

        Ok(())
    }

    /// ### Renders a progress bar with smooth fill.
    pub fn render_progress_bar(
        &mut self,
        progress_bar: &ProgressBar,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use crate::core::math::Rect2;

        // Render background bar
        let bg_color = Color::new(0.2, 0.2, 0.2, 1.0); // Dark gray background
        self.add_quad(Rect2::from_position_size(position, size), bg_color);

        // Render filled portion based on progress
        let progress_percentage = progress_bar.get_percentage() as f32;
        let fill_width = size.x * progress_percentage;

        if fill_width > 0.0 {
            let fill_color = Color::new(0.2, 0.7, 0.3, 1.0); // Green fill
            self.add_quad(
                Rect2::from_position_size(position, Vector2::new(fill_width, size.y)),
                fill_color
            );
        }

        // Render border
        let border_thickness = 1.0;
        let border_color = Color::new(0.4, 0.4, 0.4, 1.0);

        // Top border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(size.x, border_thickness)), border_color);
        // Bottom border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x, position.y + size.y - border_thickness),
            Vector2::new(size.x, border_thickness)
        ), border_color);
        // Left border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(border_thickness, size.y)), border_color);
        // Right border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x + size.x - border_thickness, position.y),
            Vector2::new(border_thickness, size.y)
        ), border_color);

        Ok(())
    }

    /// ### Renders a checkbox with modern styling.
    pub fn render_checkbox(
        &mut self,
        checkbox: &CheckBox,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use crate::core::math::Rect2;

        // Calculate checkbox square size (use smaller dimension)
        let checkbox_size = size.y.min(size.x * 0.8);
        let checkbox_rect = Rect2::from_position_size(position, Vector2::new(checkbox_size, checkbox_size));

        // Render checkbox background
        let bg_color = Color::new(0.9, 0.9, 0.9, 1.0); // Light gray background
        self.add_quad(checkbox_rect, bg_color);

        // Render checkbox border
        let border_thickness = 1.0;
        let border_color = Color::new(0.3, 0.3, 0.3, 1.0);

        // Top border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(checkbox_size, border_thickness)), border_color);
        // Bottom border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x, position.y + checkbox_size - border_thickness),
            Vector2::new(checkbox_size, border_thickness)
        ), border_color);
        // Left border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(border_thickness, checkbox_size)), border_color);
        // Right border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x + checkbox_size - border_thickness, position.y),
            Vector2::new(border_thickness, checkbox_size)
        ), border_color);

        // Render checkmark if checked
        if checkbox.is_checked() {
            let check_color = Color::new(0.2, 0.7, 0.2, 1.0); // Green checkmark
            let check_margin = checkbox_size * 0.2;
            let check_rect = Rect2::from_position_size(
                Vector2::new(position.x + check_margin, position.y + check_margin),
                Vector2::new(checkbox_size - 2.0 * check_margin, checkbox_size - 2.0 * check_margin)
            );
            self.add_quad(check_rect, check_color);
        }

        Ok(())
    }

    /// ### Renders a line edit with modern text input styling.
    pub fn render_line_edit(
        &mut self,
        line_edit: &LineEdit,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        use crate::core::math::Rect2;

        // Render background
        let bg_color = Color::new(1.0, 1.0, 1.0, 1.0); // White background
        self.add_quad(Rect2::from_position_size(position, size), bg_color);

        // Render border
        let border_thickness = 1.0;
        let border_color = if line_edit.is_focused() {
            Color::new(0.2, 0.5, 0.8, 1.0) // Blue border when focused
        } else {
            Color::new(0.5, 0.5, 0.5, 1.0) // Gray border when not focused
        };

        // Top border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(size.x, border_thickness)), border_color);
        // Bottom border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x, position.y + size.y - border_thickness),
            Vector2::new(size.x, border_thickness)
        ), border_color);
        // Left border
        self.add_quad(Rect2::from_position_size(position, Vector2::new(border_thickness, size.y)), border_color);
        // Right border
        self.add_quad(Rect2::from_position_size(
            Vector2::new(position.x + size.x - border_thickness, position.y),
            Vector2::new(border_thickness, size.y)
        ), border_color);

        // Render text content
        let text_position = Vector2::new(position.x + 5.0, position.y + 5.0);
        let font_size = 14.0; // Default font size for line edits

        let current_text = line_edit.get_text();
        if !current_text.is_empty() {
            // Render actual text content
            let text_color = Color::new(0.0, 0.0, 0.0, 1.0); // Black text
            let display_text = if line_edit.is_secret() {
                "*".repeat(current_text.len()) // Show asterisks for password fields
            } else {
                current_text.to_string()
            };
            self.render_text(&display_text, text_position, text_color, font_size)?;
        } else {
            // Render placeholder text
            let placeholder = line_edit.get_placeholder_text();
            if !placeholder.is_empty() {
                let placeholder_color = Color::new(0.6, 0.6, 0.6, 1.0); // Gray placeholder text
                self.render_text(&placeholder, text_position, placeholder_color, font_size)?;
            }
        }

        // Render cursor if focused
        if line_edit.is_focused() {
            let text_width = current_text.len() as f32 * (font_size * 0.6); // Approximate text width
            let cursor_x = text_position.x + text_width;
            let cursor_color = Color::new(0.0, 0.0, 0.0, 1.0); // Black cursor
            self.add_quad(Rect2::from_position_size(
                Vector2::new(cursor_x, position.y + 2.0),
                Vector2::new(1.0, size.y - 4.0)
            ), cursor_color);
        }

        Ok(())
    }

    /// ### Renders a rectangle with the specified color.
    pub fn render_rect(
        &mut self,
        rect: Rect2,
        color: Color,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.add_quad(rect, color);
        Ok(())
    }

    /// ### Flushes all accumulated vertex data to the GPU.
    pub fn flush(
        &mut self,
        render_pass: &mut wgpu::RenderPass,
        queue: &wgpu::Queue,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Process pending glyph uploads first
        self.process_pending_glyph_uploads(queue)?;

        if self.vertices.is_empty() {
            return Ok(());
        }

        // Update projection matrix for screen coordinates
        let projection_matrix = [
            2.0 / window_size.x, 0.0, 0.0, 0.0,
            0.0, -2.0 / window_size.y, 0.0, 0.0,
            0.0, 0.0, 1.0, 0.0,
            -1.0, 1.0, 0.0, 1.0,
        ];

        // Write projection matrix to uniform buffer
        queue.write_buffer(&self.uniform_buffer, 0, bytemuck::cast_slice(&projection_matrix));

        // Write vertex data to buffer
        queue.write_buffer(&self.vertex_buffer, 0, bytemuck::cast_slice(&self.vertices));

        // Write index data to buffer
        queue.write_buffer(&self.index_buffer, 0, bytemuck::cast_slice(&self.indices));

        // Set up render pass
        render_pass.set_pipeline(&self.pipeline);
        render_pass.set_bind_group(0, &self.uniform_bind_group, &[]);
        render_pass.set_vertex_buffer(0, self.vertex_buffer.slice(..));
        render_pass.set_index_buffer(self.index_buffer.slice(..), wgpu::IndexFormat::Uint16);

        // Draw all accumulated quads
        render_pass.draw_indexed(0..self.indices.len() as u32, 0, 0..1);

        Ok(())
    }
}