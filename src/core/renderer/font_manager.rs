//! Font Manager for Multi-Font Support
//!
//! This module provides comprehensive font management capabilities for the Verturion
//! game engine, supporting both OTF (OpenType) and TTF (TrueType) font formats.
//! It enables dynamic font loading, switching, and validation to ensure robust
//! baseline alignment testing across different font files.

use ab_glyph::{FontRef, FontArc};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::fs;

/// ### Supported font formats for comprehensive testing.
#[derive(Debug, Clone, PartialEq)]
pub enum FontFormat {
    /// TrueType Font format (.ttf)
    TrueType,
    /// OpenType Font format (.otf)
    OpenType,
    /// TrueType Collection (.ttc)
    TrueTypeCollection,
}

impl FontFormat {
    /// ### Determines font format from file extension.
    pub fn from_extension(extension: &str) -> Option<Self> {
        match extension.to_lowercase().as_str() {
            "ttf" => Some(FontFormat::TrueType),
            "otf" => Some(FontFormat::OpenType),
            "ttc" => Some(FontFormat::TrueTypeCollection),
            _ => None,
        }
    }

    /// ### Gets the file extension for this font format.
    pub fn extension(&self) -> &'static str {
        match self {
            FontFormat::TrueType => "ttf",
            FontFormat::OpenType => "otf",
            FontFormat::TrueTypeCollection => "ttc",
        }
    }

    /// ### Gets a human-readable name for this font format.
    pub fn name(&self) -> &'static str {
        match self {
            FontFormat::TrueType => "TrueType",
            FontFormat::OpenType => "OpenType",
            FontFormat::TrueTypeCollection => "TrueType Collection",
        }
    }
}

/// ### Font metadata for display and validation purposes.
#[derive(Debug, Clone)]
pub struct FontInfo {
    /// Display name of the font
    pub name: String,
    /// File path to the font
    pub path: PathBuf,
    /// Font format (TTF, OTF, TTC)
    pub format: FontFormat,
    /// File size in bytes
    pub file_size: u64,
    /// Whether the font loaded successfully
    pub is_valid: bool,
    /// Error message if font failed to load
    pub error_message: Option<String>,
}

impl FontInfo {
    /// ### Creates a new FontInfo from a file path.
    pub fn from_path(path: PathBuf) -> Self {
        let name = path.file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("Unknown")
            .to_string();

        let format = path.extension()
            .and_then(|ext| ext.to_str())
            .and_then(FontFormat::from_extension)
            .unwrap_or(FontFormat::TrueType);

        let file_size = fs::metadata(&path)
            .map(|m| m.len())
            .unwrap_or(0);

        Self {
            name,
            path,
            format,
            file_size,
            is_valid: false,
            error_message: None,
        }
    }

    /// ### Gets a display string for this font.
    pub fn display_name(&self) -> String {
        format!("{} ({}, {:.1}KB)", 
            self.name, 
            self.format.name(), 
            self.file_size as f64 / 1024.0
        )
    }
}

/// ### Comprehensive font manager supporting multiple font formats.
pub struct FontManager {
    /// Available fonts discovered in the fonts directory
    available_fonts: Vec<FontInfo>,
    /// Currently loaded fonts (path -> FontArc)
    loaded_fonts: HashMap<PathBuf, FontArc>,
    /// Currently selected font index
    current_font_index: usize,
    /// Font search directories
    font_directories: Vec<PathBuf>,
}

impl FontManager {
    /// ### Creates a new font manager with default search directories.
    pub fn new() -> Self {
        let mut font_directories = vec![
            PathBuf::from("assets/fonts"),
        ];

        // Add system font directories based on platform
        #[cfg(target_os = "windows")]
        {
            font_directories.push(PathBuf::from("C:/Windows/Fonts"));
        }

        #[cfg(target_os = "linux")]
        {
            font_directories.extend([
                PathBuf::from("/usr/share/fonts"),
                PathBuf::from("/usr/local/share/fonts"),
                PathBuf::from("/home/<USER>/share/fonts"),
            ]);
        }

        #[cfg(target_os = "macos")]
        {
            font_directories.extend([
                PathBuf::from("/System/Library/Fonts"),
                PathBuf::from("/Library/Fonts"),
                PathBuf::from("/Users/<USER>/Fonts"),
            ]);
        }

        Self {
            available_fonts: Vec::new(),
            loaded_fonts: HashMap::new(),
            current_font_index: 0,
            font_directories,
        }
    }

    /// ### Scans for available fonts in all search directories.
    pub fn scan_fonts(&mut self) -> Result<usize, Box<dyn std::error::Error>> {
        self.available_fonts.clear();
        let mut total_found = 0;

        // Clone directories to avoid borrowing issues
        let directories = self.font_directories.clone();
        for directory in &directories {
            if let Ok(found) = self.scan_directory(directory) {
                total_found += found;
            }
        }

        // Sort fonts by name for consistent ordering
        self.available_fonts.sort_by(|a, b| a.name.cmp(&b.name));

        println!("FontManager: Found {} font files across {} directories",
            total_found, directories.len());

        Ok(total_found)
    }

    /// ### Scans a specific directory for font files.
    fn scan_directory(&mut self, directory: &Path) -> Result<usize, Box<dyn std::error::Error>> {
        if !directory.exists() {
            return Ok(0);
        }

        let mut found = 0;
        let entries = fs::read_dir(directory)?;

        for entry in entries {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() {
                if let Some(extension) = path.extension().and_then(|s| s.to_str()) {
                    if FontFormat::from_extension(extension).is_some() {
                        let mut font_info = FontInfo::from_path(path);
                        
                        // Validate the font by attempting to load it
                        match self.validate_font(&font_info.path) {
                            Ok(_) => {
                                font_info.is_valid = true;
                                println!("FontManager: Found valid {} font: {}", 
                                    font_info.format.name(), font_info.display_name());
                            }
                            Err(e) => {
                                font_info.error_message = Some(e.to_string());
                                println!("FontManager: Invalid font {}: {}", 
                                    font_info.display_name(), e);
                            }
                        }

                        self.available_fonts.push(font_info);
                        found += 1;
                    }
                }
            }
        }

        Ok(found)
    }

    /// ### Validates a font file by attempting to load it with ab_glyph.
    fn validate_font(&self, path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let font_data = fs::read(path)?;

        // Attempt to parse with ab_glyph
        FontRef::try_from_slice(&font_data)
            .map_err(|e| format!("ab_glyph parsing error: {}", e))?;

        Ok(())
    }

    /// ### Loads a font from the specified path.
    pub fn load_font(&mut self, path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        // Check if already loaded
        if self.loaded_fonts.contains_key(path) {
            return Ok(());
        }

        // Load font data
        let font_data = fs::read(path)
            .map_err(|e| format!("Failed to read font file {}: {}", path.display(), e))?;

        // Parse with ab_glyph and create FontArc (which owns the data)
        let font_arc = FontArc::try_from_vec(font_data)
            .map_err(|e| format!("Failed to parse font {}: {}", path.display(), e))?;

        // Store the loaded font
        self.loaded_fonts.insert(path.to_path_buf(), font_arc);

        println!("FontManager: Successfully loaded font: {}", path.display());

        Ok(())
    }

    /// ### Gets a reference to a loaded font.
    pub fn get_font(&self, path: &Path) -> Option<&FontArc> {
        self.loaded_fonts.get(path)
    }

    /// ### Gets the currently selected font.
    pub fn current_font(&mut self) -> Result<&FontArc, Box<dyn std::error::Error>> {
        if self.available_fonts.is_empty() {
            return Err("No fonts available. Please run scan_fonts() first.".into());
        }

        let current_font_path = self.available_fonts[self.current_font_index].path.clone();

        if !self.available_fonts[self.current_font_index].is_valid {
            return Err(format!("Current font is invalid: {}",
                self.available_fonts[self.current_font_index].error_message.as_deref().unwrap_or("Unknown error")).into());
        }

        // Load the font if not already loaded
        self.load_font(&current_font_path)?;

        // Return reference to the loaded font
        Ok(self.loaded_fonts.get(&current_font_path).unwrap())
    }

    /// ### Gets information about the currently selected font.
    pub fn current_font_info(&self) -> Option<&FontInfo> {
        self.available_fonts.get(self.current_font_index)
    }

    /// ### Switches to the next available font.
    pub fn next_font(&mut self) -> Result<&FontInfo, Box<dyn std::error::Error>> {
        if self.available_fonts.is_empty() {
            return Err("No fonts available".into());
        }

        // Find next valid font
        let start_index = self.current_font_index;
        loop {
            self.current_font_index = (self.current_font_index + 1) % self.available_fonts.len();
            
            if self.available_fonts[self.current_font_index].is_valid {
                break;
            }

            // Prevent infinite loop if no valid fonts
            if self.current_font_index == start_index {
                return Err("No valid fonts available".into());
            }
        }

        Ok(&self.available_fonts[self.current_font_index])
    }

    /// ### Switches to the previous available font.
    pub fn previous_font(&mut self) -> Result<&FontInfo, Box<dyn std::error::Error>> {
        if self.available_fonts.is_empty() {
            return Err("No fonts available".into());
        }

        // Find previous valid font
        let start_index = self.current_font_index;
        loop {
            self.current_font_index = if self.current_font_index == 0 {
                self.available_fonts.len() - 1
            } else {
                self.current_font_index - 1
            };
            
            if self.available_fonts[self.current_font_index].is_valid {
                break;
            }

            // Prevent infinite loop if no valid fonts
            if self.current_font_index == start_index {
                return Err("No valid fonts available".into());
            }
        }

        Ok(&self.available_fonts[self.current_font_index])
    }

    /// ### Gets a list of all available fonts.
    pub fn available_fonts(&self) -> &[FontInfo] {
        &self.available_fonts
    }

    /// ### Gets the number of valid fonts available.
    pub fn valid_font_count(&self) -> usize {
        self.available_fonts.iter().filter(|f| f.is_valid).count()
    }

    /// ### Gets the current font index.
    pub fn current_font_index(&self) -> usize {
        self.current_font_index
    }

    /// ### Adds a custom font directory to search.
    pub fn add_font_directory(&mut self, directory: PathBuf) {
        if !self.font_directories.contains(&directory) {
            self.font_directories.push(directory);
        }
    }
}
