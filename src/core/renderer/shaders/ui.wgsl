// UI rendering shader for Verturion graphics system

struct Uniforms {
    projection: mat4x4<f32>,
}

@group(0) @binding(0)
var<uniform> uniforms: Uniforms;

@group(0) @binding(1)
var font_texture: texture_2d<f32>;

@group(0) @binding(2)
var font_sampler: sampler;

struct VertexInput {
    @location(0) position: vec2<f32>,
    @location(1) tex_coords: vec2<f32>,
    @location(2) color: vec4<f32>,
}

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) tex_coords: vec2<f32>,
    @location(1) color: vec4<f32>,
}

@vertex
fn vs_main(input: VertexInput) -> VertexOutput {
    var out: VertexOutput;

    // Transform position to clip space
    out.clip_position = uniforms.projection * vec4<f32>(input.position, 0.0, 1.0);
    out.tex_coords = input.tex_coords;
    out.color = input.color;

    return out;
}

@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
    // Sample the font atlas texture
    let alpha = textureSample(font_texture, font_sampler, in.tex_coords).r;

    // Check if this is a text glyph or solid color quad
    // Text glyphs will have non-zero alpha from the texture
    // Solid color quads will have zero alpha from the texture
    if (alpha > 0.01) {
        // Text rendering - modulate color with texture alpha
        return vec4<f32>(in.color.rgb, in.color.a * alpha);
    } else {
        // Solid color quad (UI elements like buttons, backgrounds)
        return in.color;
    }
}
