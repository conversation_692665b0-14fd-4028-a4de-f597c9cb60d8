/// ### 64-bit Floating-Point Wrapper
///
/// A Godot-compatible wrapper around Rust's f64 primitive type that provides
/// type safety, utility methods, and seamless integration with the Variant system.
/// This type is designed to match <PERSON><PERSON>'s float behavior while maintaining Rust's
/// performance characteristics and IEEE 754 compliance.
///
/// ## Features
///
/// - **Type Safety**: Prevents accidental mixing of different numeric types
/// - **Godot Compatibility**: Matches Godot's float API and behavior patterns
/// - **Performance**: Zero-cost abstraction with Copy semantics
/// - **Integration**: Works seamlessly with Dictionary, Array, and Variant systems
/// - **IEEE 754 Compliance**: Proper handling of NaN, infinity, and special values
/// - **Mathematical Operations**: Comprehensive floating-point operations
///
/// ## Examples
///
/// ```
/// # use verturion::core::variant::Float;
/// // Create floats
/// let a = Float::new(3.14159);
/// let b = Float::from_primitive(2.71828);
/// let c = Float::default(); // 0.0
///
/// // Mathematical operations
/// let abs_val = Float::new(-2.5).abs();
/// let rounded = Float::new(3.7).round();
/// let clamped = Float::new(150.0).clamp(Float::new(0.0), Float::new(100.0));
///
/// // Special value checks
/// let is_finite = a.is_finite();
/// let is_nan = Float::NAN.is_nan();
/// ```

use std::fmt;
use std::hash::{Hash, Hasher};

/// ### 64-bit floating-point wrapper type.
///
/// Provides a type-safe wrapper around f64 with Godot-compatible methods
/// and seamless integration with the Variant system.
#[derive(Debug, Clone, Copy, PartialEq, PartialOrd)]
pub struct Float {
    /// The wrapped floating-point value
    value: f64,
}

impl Float {
    /// ### Positive infinity constant.
    pub const INFINITY: Self = Self { value: f64::INFINITY };

    /// ### Negative infinity constant.
    pub const NEG_INFINITY: Self = Self { value: f64::NEG_INFINITY };

    /// ### Not-a-Number constant.
    pub const NAN: Self = Self { value: f64::NAN };

    /// ### Mathematical constant π (pi).
    pub const PI: Self = Self { value: std::f64::consts::PI };

    /// ### Mathematical constant e (Euler's number).
    pub const E: Self = Self { value: std::f64::consts::E };

    /// ### Creates a new Float with the specified value.
    ///
    /// This is the primary constructor for creating Float instances.
    /// The value can be any f64 including special values like NaN and infinity.
    ///
    /// # Arguments
    /// * `value` - The floating-point value to wrap
    ///
    /// # Returns
    /// A new Float instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let pi = Float::new(3.14159);
    /// let negative = Float::new(-2.5);
    /// let zero = Float::new(0.0);
    /// let infinity = Float::new(f64::INFINITY);
    /// ```
    #[inline]
    pub const fn new(value: f64) -> Self {
        Self { value }
    }

    /// ### Creates a new Float from a primitive f64 value.
    ///
    /// This method provides an alternative constructor that makes the
    /// conversion from primitive types explicit in the code.
    ///
    /// # Arguments
    /// * `value` - The primitive f64 value to wrap
    ///
    /// # Returns
    /// A new Float instance containing the specified value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let from_literal = Float::from_primitive(2.718);
    /// let some_f64_value = 3.14;
    /// let from_variable = Float::from_primitive(some_f64_value);
    /// ```
    #[inline]
    pub const fn from_primitive(value: f64) -> Self {
        Self::new(value)
    }

    /// ### Gets the wrapped floating-point value.
    ///
    /// Returns the underlying f64 value contained in this Float wrapper.
    /// This is the primary method for extracting the primitive value.
    ///
    /// # Returns
    /// The wrapped f64 value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let float_val = Float::new(3.14);
    /// let primitive: f64 = float_val.get();
    /// assert_eq!(primitive, 3.14);
    /// ```
    #[inline]
    pub const fn get(self) -> f64 {
        self.value
    }

    /// ### Sets the wrapped floating-point value.
    ///
    /// Updates the value contained in this Float wrapper.
    /// This method provides mutable access to the wrapped value.
    ///
    /// # Arguments
    /// * `value` - The new value to store
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let mut float_val = Float::new(1.0);
    /// float_val.set(2.5);
    /// assert_eq!(float_val.get(), 2.5);
    /// ```
    #[inline]
    pub fn set(&mut self, value: f64) {
        self.value = value;
    }

    /// ### Returns the wrapped value as a primitive f64.
    ///
    /// This method provides an alternative to `get()` with a more explicit name
    /// indicating the conversion to primitive type.
    ///
    /// # Returns
    /// The wrapped f64 value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let float_val = Float::new(-3.14);
    /// let primitive = float_val.as_primitive();
    /// assert_eq!(primitive, -3.14);
    /// ```
    #[inline]
    pub const fn as_primitive(self) -> f64 {
        self.value
    }
}

impl Float {
    /// ### Returns the absolute value of the float.
    ///
    /// Computes the absolute value, returning a positive Float.
    /// For NaN values, returns NaN.
    ///
    /// # Returns
    /// A Float containing the absolute value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let negative = Float::new(-3.14);
    /// let positive = Float::new(2.71);
    ///
    /// assert_eq!(negative.abs(), Float::new(3.14));
    /// assert_eq!(positive.abs(), Float::new(2.71));
    /// assert_eq!(Float::new(0.0).abs(), Float::new(0.0));
    /// ```
    #[inline]
    pub fn abs(self) -> Self {
        Self::new(self.value.abs())
    }

    /// ### Returns the sign of the float.
    ///
    /// Returns -1.0 for negative numbers, 0.0 for zero, and 1.0 for positive numbers.
    /// For NaN, returns NaN.
    ///
    /// # Returns
    /// A Float containing -1.0, 0.0, or 1.0 representing the sign.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// assert_eq!(Float::new(-3.14).sign(), Float::new(-1.0));
    /// assert_eq!(Float::new(0.0).sign(), Float::new(0.0));
    /// assert_eq!(Float::new(3.14).sign(), Float::new(1.0));
    /// ```
    #[inline]
    pub fn sign(self) -> Self {
        if self.value == 0.0 {
            Self::new(0.0)
        } else {
            Self::new(self.value.signum())
        }
    }

    /// ### Rounds the float to the nearest integer.
    ///
    /// Rounds to the nearest integer value. Halfway cases are rounded
    /// away from zero (e.g., 0.5 becomes 1.0, -0.5 becomes -1.0).
    ///
    /// # Returns
    /// A Float containing the rounded value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// assert_eq!(Float::new(3.7).round(), Float::new(4.0));
    /// assert_eq!(Float::new(3.2).round(), Float::new(3.0));
    /// assert_eq!(Float::new(-2.8).round(), Float::new(-3.0));
    /// ```
    #[inline]
    pub fn round(self) -> Self {
        Self::new(self.value.round())
    }

    /// ### Returns the largest integer less than or equal to the float.
    ///
    /// Computes the floor function, rounding down to the nearest integer.
    ///
    /// # Returns
    /// A Float containing the floor value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// assert_eq!(Float::new(3.7).floor(), Float::new(3.0));
    /// assert_eq!(Float::new(-2.3).floor(), Float::new(-3.0));
    /// ```
    #[inline]
    pub fn floor(self) -> Self {
        Self::new(self.value.floor())
    }

    /// ### Returns the smallest integer greater than or equal to the float.
    ///
    /// Computes the ceiling function, rounding up to the nearest integer.
    ///
    /// # Returns
    /// A Float containing the ceiling value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// assert_eq!(Float::new(3.2).ceil(), Float::new(4.0));
    /// assert_eq!(Float::new(-2.8).ceil(), Float::new(-2.0));
    /// ```
    #[inline]
    pub fn ceil(self) -> Self {
        Self::new(self.value.ceil())
    }

    /// ### Clamps the float to the specified range.
    ///
    /// Returns a value that is constrained to be between min and max (inclusive).
    /// If the value is less than min, returns min. If greater than max, returns max.
    /// Otherwise returns the original value.
    ///
    /// # Arguments
    /// * `min` - The minimum allowed value
    /// * `max` - The maximum allowed value
    ///
    /// # Returns
    /// A Float clamped to the specified range.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let value = Float::new(150.0);
    /// let min = Float::new(0.0);
    /// let max = Float::new(100.0);
    ///
    /// assert_eq!(value.clamp(min, max), Float::new(100.0));
    /// assert_eq!(Float::new(-10.0).clamp(min, max), Float::new(0.0));
    /// assert_eq!(Float::new(50.0).clamp(min, max), Float::new(50.0));
    /// ```
    #[inline]
    pub fn clamp(self, min: Self, max: Self) -> Self {
        Self::new(self.value.clamp(min.value, max.value))
    }

    /// ### Returns the minimum of two floats.
    ///
    /// Compares two Float values and returns the smaller one.
    /// NaN values are handled according to IEEE 754 semantics.
    ///
    /// # Arguments
    /// * `other` - The other Float to compare with
    ///
    /// # Returns
    /// The smaller of the two Float values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let a = Float::new(10.5);
    /// let b = Float::new(20.3);
    ///
    /// assert_eq!(a.min(b), Float::new(10.5));
    /// assert_eq!(b.min(a), Float::new(10.5));
    /// ```
    #[inline]
    pub fn min(self, other: Self) -> Self {
        Self::new(self.value.min(other.value))
    }

    /// ### Returns the maximum of two floats.
    ///
    /// Compares two Float values and returns the larger one.
    /// NaN values are handled according to IEEE 754 semantics.
    ///
    /// # Arguments
    /// * `other` - The other Float to compare with
    ///
    /// # Returns
    /// The larger of the two Float values.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let a = Float::new(10.5);
    /// let b = Float::new(20.3);
    ///
    /// assert_eq!(a.max(b), Float::new(20.3));
    /// assert_eq!(b.max(a), Float::new(20.3));
    /// ```
    #[inline]
    pub fn max(self, other: Self) -> Self {
        Self::new(self.value.max(other.value))
    }
}

impl Float {
    /// ### Checks if the float is finite.
    ///
    /// Returns true if the value is neither infinite nor NaN.
    ///
    /// # Returns
    /// True if the float is finite, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// assert!(Float::new(3.14).is_finite());
    /// assert!(!Float::INFINITY.is_finite());
    /// assert!(!Float::NAN.is_finite());
    /// ```
    #[inline]
    pub fn is_finite(self) -> bool {
        self.value.is_finite()
    }

    /// ### Checks if the float is infinite.
    ///
    /// Returns true if the value is positive or negative infinity.
    ///
    /// # Returns
    /// True if the float is infinite, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// assert!(!Float::new(3.14).is_infinite());
    /// assert!(Float::INFINITY.is_infinite());
    /// assert!(Float::NEG_INFINITY.is_infinite());
    /// ```
    #[inline]
    pub fn is_infinite(self) -> bool {
        self.value.is_infinite()
    }

    /// ### Checks if the float is NaN (Not a Number).
    ///
    /// Returns true if the value is NaN.
    ///
    /// # Returns
    /// True if the float is NaN, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// assert!(!Float::new(3.14).is_nan());
    /// assert!(Float::NAN.is_nan());
    /// assert!((Float::new(0.0) / Float::new(0.0)).is_nan());
    /// ```
    #[inline]
    pub fn is_nan(self) -> bool {
        self.value.is_nan()
    }

    /// ### Checks if the float is approximately zero.
    ///
    /// Returns true if the absolute value is less than a small epsilon.
    ///
    /// # Returns
    /// True if the float is approximately zero, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// assert!(Float::new(0.0).is_zero_approx());
    /// assert!(Float::new(1e-6).is_zero_approx());
    /// assert!(!Float::new(0.1).is_zero_approx());
    /// ```
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        self.value.abs() < 1e-5
    }

    /// ### Checks if two floats are approximately equal.
    ///
    /// Returns true if the absolute difference between the values
    /// is less than a small epsilon.
    ///
    /// # Arguments
    /// * `other` - The other Float to compare with
    ///
    /// # Returns
    /// True if the floats are approximately equal, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let a = Float::new(1.0);
    /// let b = Float::new(1.000001);
    ///
    /// assert!(a.is_equal_approx(b));
    /// assert!(!a.is_equal_approx(Float::new(1.1)));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        (self.value - other.value).abs() < 1e-5
    }
}

impl Float {
    /// ### Converts the float to an integer value.
    ///
    /// Creates a new Int wrapper containing the float value converted
    /// to i64. The conversion truncates towards zero.
    ///
    /// # Returns
    /// An Int containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Float, Int};
    /// let float_val = Float::new(42.7);
    /// let int_val = float_val.to_int();
    /// assert_eq!(int_val.get(), 42);
    /// ```
    #[inline]
    pub fn to_int(self) -> super::Int {
        super::Int::new(self.value as i64)
    }

    /// ### Converts the float to a Short (16-bit integer).
    ///
    /// Creates a new Short wrapper containing the float value converted
    /// to i16. Values outside the i16 range will be truncated.
    ///
    /// # Returns
    /// A Short containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Float, Short};
    /// let float_val = Float::new(1000.5);
    /// let short_val = float_val.to_short();
    /// assert_eq!(short_val.get(), 1000);
    /// ```
    #[inline]
    pub fn to_short(self) -> super::Short {
        super::Short::new(self.value as i16)
    }

    /// ### Converts the float to an Int8 (8-bit integer).
    ///
    /// Creates a new Int8 wrapper containing the float value converted
    /// to i8. Values outside the i8 range will be truncated.
    ///
    /// # Returns
    /// An Int8 containing the converted value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Float, Int8};
    /// let float_val = Float::new(100.9);
    /// let int8_val = float_val.to_int8();
    /// assert_eq!(int8_val.get(), 100);
    /// ```
    #[inline]
    pub fn to_int8(self) -> super::Int8 {
        super::Int8::new(self.value as i8)
    }
}

impl Default for Float {
    /// ### Creates a default Float with value 0.0.
    ///
    /// This implementation allows Float to be used in contexts where
    /// a default value is needed, such as in collections or when
    /// using the `Default::default()` method.
    ///
    /// # Returns
    /// A Float with value 0.0.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let default_float = Float::default();
    /// assert_eq!(default_float.get(), 0.0);
    ///
    /// let also_default: Float = Default::default();
    /// assert_eq!(also_default, Float::new(0.0));
    /// ```
    #[inline]
    fn default() -> Self {
        Self::new(0.0)
    }
}

impl fmt::Display for Float {
    /// ### Formats the Float for display.
    ///
    /// Provides a human-readable string representation of the floating-point value.
    /// The output matches the standard formatting of f64 values.
    ///
    /// # Arguments
    /// * `f` - The formatter to write to
    ///
    /// # Returns
    /// A formatting result.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::Float;
    /// let float_val = Float::new(3.14159);
    /// assert_eq!(format!("{}", float_val), "3.14159");
    ///
    /// let negative = Float::new(-2.5);
    /// assert_eq!(format!("{}", negative), "-2.5");
    /// ```
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.value)
    }
}

impl Hash for Float {
    /// ### Computes the hash of the Float.
    ///
    /// Provides hash functionality for use as dictionary keys.
    /// The hash is computed from the bit representation of the f64 value
    /// to ensure consistent hashing for equal values.
    ///
    /// # Arguments
    /// * `state` - The hasher to write to
    fn hash<H: Hasher>(&self, state: &mut H) {
        // Use to_bits() to ensure consistent hashing for equal values
        self.value.to_bits().hash(state);
    }
}

// Note: Eq is not implemented for Float because f64 doesn't implement Eq
// due to NaN != NaN according to IEEE 754 standard

// Arithmetic operations
impl std::ops::Add for Float {
    type Output = Self;

    #[inline]
    fn add(self, rhs: Self) -> Self::Output {
        Self::new(self.value + rhs.value)
    }
}

impl std::ops::Sub for Float {
    type Output = Self;

    #[inline]
    fn sub(self, rhs: Self) -> Self::Output {
        Self::new(self.value - rhs.value)
    }
}

impl std::ops::Mul for Float {
    type Output = Self;

    #[inline]
    fn mul(self, rhs: Self) -> Self::Output {
        Self::new(self.value * rhs.value)
    }
}

impl std::ops::Div for Float {
    type Output = Self;

    #[inline]
    fn div(self, rhs: Self) -> Self::Output {
        Self::new(self.value / rhs.value)
    }
}

impl std::ops::Rem for Float {
    type Output = Self;

    #[inline]
    fn rem(self, rhs: Self) -> Self::Output {
        Self::new(self.value % rhs.value)
    }
}

impl std::ops::Neg for Float {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self::Output {
        Self::new(-self.value)
    }
}

// Assignment operations
impl std::ops::AddAssign for Float {
    #[inline]
    fn add_assign(&mut self, rhs: Self) {
        self.value += rhs.value;
    }
}

impl std::ops::SubAssign for Float {
    #[inline]
    fn sub_assign(&mut self, rhs: Self) {
        self.value -= rhs.value;
    }
}

impl std::ops::MulAssign for Float {
    #[inline]
    fn mul_assign(&mut self, rhs: Self) {
        self.value *= rhs.value;
    }
}

impl std::ops::DivAssign for Float {
    #[inline]
    fn div_assign(&mut self, rhs: Self) {
        self.value /= rhs.value;
    }
}

impl std::ops::RemAssign for Float {
    #[inline]
    fn rem_assign(&mut self, rhs: Self) {
        self.value %= rhs.value;
    }
}

// Conversions from primitive types
impl From<f64> for Float {
    #[inline]
    fn from(value: f64) -> Self {
        Self::new(value)
    }
}

impl From<f32> for Float {
    #[inline]
    fn from(value: f32) -> Self {
        Self::new(value as f64)
    }
}

impl From<i64> for Float {
    #[inline]
    fn from(value: i64) -> Self {
        Self::new(value as f64)
    }
}

impl From<i32> for Float {
    #[inline]
    fn from(value: i32) -> Self {
        Self::new(value as f64)
    }
}

impl From<i16> for Float {
    #[inline]
    fn from(value: i16) -> Self {
        Self::new(value as f64)
    }
}

impl From<i8> for Float {
    #[inline]
    fn from(value: i8) -> Self {
        Self::new(value as f64)
    }
}

// Conversions to primitive types
impl From<Float> for f64 {
    #[inline]
    fn from(float: Float) -> Self {
        float.value
    }
}

impl From<Float> for f32 {
    #[inline]
    fn from(float: Float) -> Self {
        float.value as f32
    }
}

// Variant system integration - From<Float> is already implemented in variant.rs
impl TryFrom<super::Variant> for Float {
    type Error = &'static str;

    /// ### Attempts to extract a Float from a Variant.
    ///
    /// Tries to convert a Variant to a Float. Succeeds if the Variant
    /// contains a FloatWrapper, otherwise returns an error.
    ///
    /// # Parameters
    /// - `variant`: The Variant to convert
    ///
    /// # Returns
    /// Result containing the Float on success, error message on failure.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::variant::{Float, Variant};
    /// let variant = Variant::FloatWrapper(Float::new(3.14));
    /// let float_val: Float = variant.try_into().unwrap();
    /// assert_eq!(float_val.get(), 3.14);
    /// ```
    fn try_from(variant: super::Variant) -> Result<Self, Self::Error> {
        match variant {
            super::Variant::FloatWrapper(float) => Ok(float),
            _ => Err("Variant is not a FloatWrapper"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_float_creation() {
        let float1 = Float::new(3.14159);
        assert_eq!(float1.get(), 3.14159);

        let float2 = Float::from_primitive(-2.71828);
        assert_eq!(float2.get(), -2.71828);

        let float3 = Float::default();
        assert_eq!(float3.get(), 0.0);

        let float4: Float = 100.5.into();
        assert_eq!(float4.get(), 100.5);
    }

    #[test]
    fn test_float_constants() {
        assert!(Float::INFINITY.is_infinite());
        assert!(Float::NEG_INFINITY.is_infinite());
        assert!(Float::NAN.is_nan());
        assert_eq!(Float::PI.get(), std::f64::consts::PI);
        assert_eq!(Float::E.get(), std::f64::consts::E);
    }

    #[test]
    fn test_float_value_access() {
        let mut float_val = Float::new(1.5);
        assert_eq!(float_val.get(), 1.5);
        assert_eq!(float_val.as_primitive(), 1.5);

        float_val.set(2.5);
        assert_eq!(float_val.get(), 2.5);
    }

    #[test]
    fn test_float_mathematical_operations() {
        let positive = Float::new(3.14);
        let negative = Float::new(-2.71);
        let zero = Float::new(0.0);

        // Test abs
        assert_eq!(positive.abs(), Float::new(3.14));
        assert_eq!(negative.abs(), Float::new(2.71));
        assert_eq!(zero.abs(), Float::new(0.0));

        // Test sign
        assert_eq!(positive.sign(), Float::new(1.0));
        assert_eq!(negative.sign(), Float::new(-1.0));
        assert_eq!(zero.sign(), Float::new(0.0));

        // Test min/max
        assert_eq!(positive.min(negative), negative);
        assert_eq!(positive.max(negative), positive);
        assert_eq!(positive.min(positive), positive);
    }

    #[test]
    fn test_float_rounding() {
        assert_eq!(Float::new(3.7).round(), Float::new(4.0));
        assert_eq!(Float::new(3.2).round(), Float::new(3.0));
        assert_eq!(Float::new(-2.8).round(), Float::new(-3.0));

        assert_eq!(Float::new(3.7).floor(), Float::new(3.0));
        assert_eq!(Float::new(-2.3).floor(), Float::new(-3.0));

        assert_eq!(Float::new(3.2).ceil(), Float::new(4.0));
        assert_eq!(Float::new(-2.8).ceil(), Float::new(-2.0));
    }

    #[test]
    fn test_float_clamp() {
        let min = Float::new(0.0);
        let max = Float::new(100.0);

        assert_eq!(Float::new(50.0).clamp(min, max), Float::new(50.0));
        assert_eq!(Float::new(-10.0).clamp(min, max), Float::new(0.0));
        assert_eq!(Float::new(150.0).clamp(min, max), Float::new(100.0));
        assert_eq!(Float::new(0.0).clamp(min, max), Float::new(0.0));
        assert_eq!(Float::new(100.0).clamp(min, max), Float::new(100.0));
    }

    #[test]
    fn test_float_special_values() {
        // Test finite
        assert!(Float::new(3.14).is_finite());
        assert!(!Float::INFINITY.is_finite());
        assert!(!Float::NEG_INFINITY.is_finite());
        assert!(!Float::NAN.is_finite());

        // Test infinite
        assert!(!Float::new(3.14).is_infinite());
        assert!(Float::INFINITY.is_infinite());
        assert!(Float::NEG_INFINITY.is_infinite());
        assert!(!Float::NAN.is_infinite());

        // Test NaN
        assert!(!Float::new(3.14).is_nan());
        assert!(Float::NAN.is_nan());
        assert!((Float::new(0.0) / Float::new(0.0)).is_nan());
    }

    #[test]
    fn test_float_approximate_equality() {
        assert!(Float::new(0.0).is_zero_approx());
        assert!(Float::new(1e-16).is_zero_approx());
        assert!(!Float::new(0.1).is_zero_approx());

        let a = Float::new(1.0);
        let b = Float::new(1.0 + f64::EPSILON / 2.0);
        assert!(a.is_equal_approx(b));
        assert!(!a.is_equal_approx(Float::new(1.1)));
    }

    #[test]
    fn test_float_arithmetic_operations() {
        let a = Float::new(10.5);
        let b = Float::new(3.2);

        assert_eq!(a + b, Float::new(13.7));
        assert_eq!(a - b, Float::new(7.3));
        assert_eq!(a * b, Float::new(33.6));
        assert!((a / b - Float::new(3.28125)).abs().get() < 1e-10);
        assert_eq!(-a, Float::new(-10.5));
    }

    #[test]
    fn test_float_assignment_operations() {
        let mut a = Float::new(10.0);
        let b = Float::new(3.0);

        a += b;
        assert_eq!(a, Float::new(13.0));

        a -= b;
        assert_eq!(a, Float::new(10.0));

        a *= b;
        assert_eq!(a, Float::new(30.0));

        a /= b;
        assert_eq!(a, Float::new(10.0));

        a %= b;
        assert_eq!(a, Float::new(1.0));
    }

    #[test]
    fn test_float_conversions() {
        let float_val = Float::new(42.7);

        // Test to other wrapper types
        let int_val = float_val.to_int();
        assert_eq!(int_val.get(), 42);

        // Test from primitive types
        let from_f32: Float = (42.5f32).into();
        assert_eq!(from_f32.get(), 42.5);

        let from_i32: Float = (42i32).into();
        assert_eq!(from_i32.get(), 42.0);

        let from_i16: Float = (42i16).into();
        assert_eq!(from_i16.get(), 42.0);

        // Test to primitive types
        let to_f64: f64 = float_val.into();
        assert_eq!(to_f64, 42.7);

        let to_f32: f32 = float_val.into();
        assert_eq!(to_f32, 42.7f32);
    }

    #[test]
    fn test_float_comparison() {
        let a = Float::new(10.5);
        let b = Float::new(20.3);
        let c = Float::new(10.5);

        assert!(a < b);
        assert!(b > a);
        assert_eq!(a, c);
        assert_ne!(a, b);
        assert!(a <= c);
        assert!(a >= c);
    }

    #[test]
    fn test_float_display() {
        assert_eq!(format!("{}", Float::new(3.14159)), "3.14159");
        assert_eq!(format!("{}", Float::new(-2.5)), "-2.5");
        assert_eq!(format!("{}", Float::new(0.0)), "0");
        assert_eq!(format!("{}", Float::INFINITY), "inf");
        assert_eq!(format!("{}", Float::NAN), "NaN");
    }

    #[test]
    fn test_float_hash() {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        // Test that equal floats have the same hash
        let float1 = Float::new(3.14);
        let float2 = Float::new(3.14);

        let mut hasher1 = DefaultHasher::new();
        let mut hasher2 = DefaultHasher::new();

        float1.hash(&mut hasher1);
        float2.hash(&mut hasher2);

        assert_eq!(hasher1.finish(), hasher2.finish());

        // Test that different floats have different hashes (usually)
        let float3 = Float::new(2.71);
        let mut hasher3 = DefaultHasher::new();
        float3.hash(&mut hasher3);

        assert_ne!(hasher1.finish(), hasher3.finish());
    }

    #[test]
    fn test_float_copy_semantics() {
        let original = Float::new(3.14);
        let copied = original;

        // Both should have the same value
        assert_eq!(original.get(), 3.14);
        assert_eq!(copied.get(), 3.14);

        // Modifying one shouldn't affect the other
        let mut mutable_copy = original;
        mutable_copy.set(2.71);
        assert_eq!(original.get(), 3.14);
        assert_eq!(mutable_copy.get(), 2.71);
    }
}
