//! Camera2D implementation for viewport management and player following.
//!
//! This module provides the Camera2D class that extends Node2D with
//! comprehensive viewport management functionality including player following,
//! camera smoothing, zoom controls, and viewport limits. It maintains full
//! compatibility with <PERSON><PERSON>'s Camera2D class while providing efficient
//! camera management for 2D games.

use std::fmt;
use crate::core::scene::nodes::Node2D;
use crate::core::math::Vector2;

/// ### Camera anchor modes for positioning behavior.
///
/// Defines how the camera positions itself relative to its target.
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
#[allow(dead_code)] // Complete anchor mode options for comprehensive camera API
pub enum AnchorMode {
    /// Camera follows target with fixed offset
    FixedTopLeft,
    /// Camera centers on target
    DragCenter,
}

/// ### Camera2D for viewport management and player following.
///
/// Camera2D extends Node2D with comprehensive viewport management
/// functionality, providing player following, camera smoothing, zoom controls,
/// viewport limits, and screen shake effects. It maintains full compatibility
/// with <PERSON><PERSON>'s Camera2D class while ensuring smooth camera movement and
/// optimal viewport control for 2D games.
///
/// ## Core Features
///
/// - **Player Following**: Smooth camera following with customizable behavior
/// - **Zoom Control**: Dynamic zoom in/out with smooth transitions
/// - **Viewport Limits**: Constrain camera movement within defined bounds
/// - **Camera Smoothing**: Smooth camera movement with configurable speed
/// - **Screen Shake**: Built-in screen shake effects for game events
/// - **Anchor Modes**: Different camera positioning behaviors
/// - **Godot Compatibility**: API matching Godot's Camera2D class
///
/// ## Camera Properties
///
/// Camera2D provides comprehensive camera control:
/// - **Current**: Whether this camera is the active viewport camera
/// - **Zoom**: Camera zoom level (1.0 = normal, 2.0 = 2x zoom)
/// - **Offset**: Camera offset from target position
/// - **Anchor Mode**: Camera positioning behavior
/// - **Smoothing**: Camera movement smoothing settings
/// - **Limits**: Viewport boundary constraints
/// - **Drag Margins**: Screen edge margins for camera movement
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::camera2d::Camera2D;
/// # use verturion::core::math::Vector2;
/// // Create a camera
/// let mut camera = Camera2D::new("MainCamera");
///
/// // Configure camera
/// camera.set_current(true);
/// camera.set_zoom(Vector2::new(1.5, 1.5)); // 1.5x zoom
/// camera.set_smoothing_enabled(true);
/// camera.set_smoothing_speed(5.0);
///
/// // Set viewport limits
/// camera.set_limit_left(-1000.0);
/// camera.set_limit_right(1000.0);
/// camera.set_limit_top(-500.0);
/// camera.set_limit_bottom(500.0);
///
/// assert!(camera.is_current());
/// assert_eq!(camera.get_zoom(), Vector2::new(1.5, 1.5));
/// ```
#[derive(Debug, Clone)]
#[allow(dead_code)] // Comprehensive 2D camera implementation awaiting integration
pub struct Camera2D {
    /// Base Node2D functionality
    base: Node2D,
    /// Whether this camera is the current active camera
    current: bool,
    /// Camera zoom level (1.0 = normal)
    zoom: Vector2,
    /// Camera offset from target position
    offset: Vector2,
    /// Camera anchor mode
    anchor_mode: AnchorMode,
    /// Whether smoothing is enabled
    smoothing_enabled: bool,
    /// Camera smoothing speed
    smoothing_speed: f32,
    /// Left viewport limit
    limit_left: f32,
    /// Right viewport limit
    limit_right: f32,
    /// Top viewport limit
    limit_top: f32,
    /// Bottom viewport limit
    limit_bottom: f32,
    /// Whether to use viewport limits
    limit_smoothed: bool,
    /// Drag margin on left edge
    drag_margin_left: f32,
    /// Drag margin on right edge
    drag_margin_right: f32,
    /// Drag margin on top edge
    drag_margin_top: f32,
    /// Drag margin on bottom edge
    drag_margin_bottom: f32,
    /// Whether drag margins are enabled
    drag_margin_enabled: bool,
    /// Screen shake offset
    shake_offset: Vector2,
    /// Screen shake intensity
    shake_intensity: f32,
    /// Screen shake duration remaining
    shake_duration: f32,
}

#[allow(dead_code)] // Comprehensive 2D camera implementation awaiting integration
impl Camera2D {
    /// ### Creates a new Camera2D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this camera node
    ///
    /// # Returns
    /// A new Camera2D instance with default camera properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::camera2d::Camera2D;
    /// # use verturion::core::math::Vector2;
    /// let camera = Camera2D::new("MainCamera");
    /// assert_eq!(camera.get_name(), "MainCamera");
    /// assert!(!camera.is_current());
    /// assert_eq!(camera.get_zoom(), Vector2::new(1.0, 1.0));
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Node2D::new(name),
            current: false,
            zoom: Vector2::new(1.0, 1.0),
            offset: Vector2::new(0.0, 0.0),
            anchor_mode: AnchorMode::DragCenter,
            smoothing_enabled: false,
            smoothing_speed: 5.0,
            limit_left: -10000000.0,
            limit_right: 10000000.0,
            limit_top: -10000000.0,
            limit_bottom: 10000000.0,
            limit_smoothed: false,
            drag_margin_left: 0.2,
            drag_margin_right: 0.2,
            drag_margin_top: 0.2,
            drag_margin_bottom: 0.2,
            drag_margin_enabled: true,
            shake_offset: Vector2::new(0.0, 0.0),
            shake_intensity: 0.0,
            shake_duration: 0.0,
        }
    }

    /// ### Checks if this camera is the current active camera.
    ///
    /// # Returns
    /// True if this camera is active, false otherwise.
    #[inline]
    pub fn is_current(&self) -> bool {
        self.current
    }

    /// ### Sets whether this camera is the current active camera.
    ///
    /// # Parameters
    /// - `current`: Whether this camera should be active
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Camera2D;
    /// let mut camera = Camera2D::new("Camera");
    /// camera.set_current(true);
    /// assert!(camera.is_current());
    /// ```
    #[inline]
    pub fn set_current(&mut self, current: bool) {
        self.current = current;
    }

    /// ### Gets the camera zoom level.
    ///
    /// # Returns
    /// The current zoom level as a Vector2.
    #[inline]
    pub fn get_zoom(&self) -> Vector2 {
        self.zoom
    }

    /// ### Sets the camera zoom level.
    ///
    /// # Parameters
    /// - `zoom`: The new zoom level (1.0 = normal, 2.0 = 2x zoom)
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Camera2D;
    /// # use verturion::core::math::Vector2;
    /// let mut camera = Camera2D::new("Camera");
    /// camera.set_zoom(Vector2::new(2.0, 2.0));
    /// assert_eq!(camera.get_zoom(), Vector2::new(2.0, 2.0));
    /// ```
    #[inline]
    pub fn set_zoom(&mut self, zoom: Vector2) {
        self.zoom = Vector2::new(zoom.x.max(0.01), zoom.y.max(0.01));
    }

    /// ### Gets the camera offset.
    ///
    /// # Returns
    /// The current camera offset from target position.
    #[inline]
    pub fn get_offset(&self) -> Vector2 {
        self.offset
    }

    /// ### Sets the camera offset.
    ///
    /// # Parameters
    /// - `offset`: The new camera offset
    #[inline]
    pub fn set_offset(&mut self, offset: Vector2) {
        self.offset = offset;
    }

    /// ### Gets the camera anchor mode.
    ///
    /// # Returns
    /// The current anchor mode.
    #[inline]
    pub fn get_anchor_mode(&self) -> AnchorMode {
        self.anchor_mode
    }

    /// ### Sets the camera anchor mode.
    ///
    /// # Parameters
    /// - `mode`: The new anchor mode
    #[inline]
    pub fn set_anchor_mode(&mut self, mode: AnchorMode) {
        self.anchor_mode = mode;
    }

    /// ### Checks if camera smoothing is enabled.
    ///
    /// # Returns
    /// True if smoothing is enabled, false otherwise.
    #[inline]
    pub fn is_smoothing_enabled(&self) -> bool {
        self.smoothing_enabled
    }

    /// ### Sets whether camera smoothing is enabled.
    ///
    /// # Parameters
    /// - `enabled`: Whether to enable camera smoothing
    #[inline]
    pub fn set_smoothing_enabled(&mut self, enabled: bool) {
        self.smoothing_enabled = enabled;
    }

    /// ### Gets the camera smoothing speed.
    ///
    /// # Returns
    /// The current smoothing speed.
    #[inline]
    pub fn get_smoothing_speed(&self) -> f32 {
        self.smoothing_speed
    }

    /// ### Sets the camera smoothing speed.
    ///
    /// # Parameters
    /// - `speed`: The new smoothing speed (higher = faster)
    #[inline]
    pub fn set_smoothing_speed(&mut self, speed: f32) {
        self.smoothing_speed = speed.max(0.0);
    }

    /// ### Gets the left viewport limit.
    ///
    /// # Returns
    /// The left boundary of the camera viewport.
    #[inline]
    pub fn get_limit_left(&self) -> f32 {
        self.limit_left
    }

    /// ### Sets the left viewport limit.
    ///
    /// # Parameters
    /// - `limit`: The new left boundary
    #[inline]
    pub fn set_limit_left(&mut self, limit: f32) {
        self.limit_left = limit;
    }

    /// ### Gets the right viewport limit.
    ///
    /// # Returns
    /// The right boundary of the camera viewport.
    #[inline]
    pub fn get_limit_right(&self) -> f32 {
        self.limit_right
    }

    /// ### Sets the right viewport limit.
    ///
    /// # Parameters
    /// - `limit`: The new right boundary
    #[inline]
    pub fn set_limit_right(&mut self, limit: f32) {
        self.limit_right = limit;
    }

    /// ### Gets the top viewport limit.
    ///
    /// # Returns
    /// The top boundary of the camera viewport.
    #[inline]
    pub fn get_limit_top(&self) -> f32 {
        self.limit_top
    }

    /// ### Sets the top viewport limit.
    ///
    /// # Parameters
    /// - `limit`: The new top boundary
    #[inline]
    pub fn set_limit_top(&mut self, limit: f32) {
        self.limit_top = limit;
    }

    /// ### Gets the bottom viewport limit.
    ///
    /// # Returns
    /// The bottom boundary of the camera viewport.
    #[inline]
    pub fn get_limit_bottom(&self) -> f32 {
        self.limit_bottom
    }

    /// ### Sets the bottom viewport limit.
    ///
    /// # Parameters
    /// - `limit`: The new bottom boundary
    #[inline]
    pub fn set_limit_bottom(&mut self, limit: f32) {
        self.limit_bottom = limit;
    }

    /// ### Checks if limit smoothing is enabled.
    ///
    /// # Returns
    /// True if limit smoothing is enabled, false otherwise.
    #[inline]
    pub fn is_limit_smoothed(&self) -> bool {
        self.limit_smoothed
    }

    /// ### Sets whether limit smoothing is enabled.
    ///
    /// # Parameters
    /// - `smoothed`: Whether to enable limit smoothing
    #[inline]
    pub fn set_limit_smoothed(&mut self, smoothed: bool) {
        self.limit_smoothed = smoothed;
    }

    /// ### Gets the drag margin for the left edge.
    ///
    /// # Returns
    /// The left drag margin (0.0 to 1.0).
    #[inline]
    pub fn get_drag_margin_left(&self) -> f32 {
        self.drag_margin_left
    }

    /// ### Sets the drag margin for the left edge.
    ///
    /// # Parameters
    /// - `margin`: The new left drag margin (0.0 to 1.0)
    #[inline]
    pub fn set_drag_margin_left(&mut self, margin: f32) {
        self.drag_margin_left = margin.clamp(0.0, 1.0);
    }

    /// ### Gets the drag margin for the right edge.
    ///
    /// # Returns
    /// The right drag margin (0.0 to 1.0).
    #[inline]
    pub fn get_drag_margin_right(&self) -> f32 {
        self.drag_margin_right
    }

    /// ### Sets the drag margin for the right edge.
    ///
    /// # Parameters
    /// - `margin`: The new right drag margin (0.0 to 1.0)
    #[inline]
    pub fn set_drag_margin_right(&mut self, margin: f32) {
        self.drag_margin_right = margin.clamp(0.0, 1.0);
    }

    /// ### Gets the drag margin for the top edge.
    ///
    /// # Returns
    /// The top drag margin (0.0 to 1.0).
    #[inline]
    pub fn get_drag_margin_top(&self) -> f32 {
        self.drag_margin_top
    }

    /// ### Sets the drag margin for the top edge.
    ///
    /// # Parameters
    /// - `margin`: The new top drag margin (0.0 to 1.0)
    #[inline]
    pub fn set_drag_margin_top(&mut self, margin: f32) {
        self.drag_margin_top = margin.clamp(0.0, 1.0);
    }

    /// ### Gets the drag margin for the bottom edge.
    ///
    /// # Returns
    /// The bottom drag margin (0.0 to 1.0).
    #[inline]
    pub fn get_drag_margin_bottom(&self) -> f32 {
        self.drag_margin_bottom
    }

    /// ### Sets the drag margin for the bottom edge.
    ///
    /// # Parameters
    /// - `margin`: The new bottom drag margin (0.0 to 1.0)
    #[inline]
    pub fn set_drag_margin_bottom(&mut self, margin: f32) {
        self.drag_margin_bottom = margin.clamp(0.0, 1.0);
    }

    /// ### Checks if drag margins are enabled.
    ///
    /// # Returns
    /// True if drag margins are enabled, false otherwise.
    #[inline]
    pub fn is_drag_margin_enabled(&self) -> bool {
        self.drag_margin_enabled
    }

    /// ### Sets whether drag margins are enabled.
    ///
    /// # Parameters
    /// - `enabled`: Whether to enable drag margins
    #[inline]
    pub fn set_drag_margin_enabled(&mut self, enabled: bool) {
        self.drag_margin_enabled = enabled;
    }

    /// ### Starts a screen shake effect.
    ///
    /// # Parameters
    /// - `intensity`: Shake intensity (pixels)
    /// - `duration`: Shake duration in seconds
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Camera2D;
    /// let mut camera = Camera2D::new("Camera");
    /// camera.shake(10.0, 0.5); // 10 pixel shake for 0.5 seconds
    /// ```
    #[inline]
    pub fn shake(&mut self, intensity: f32, duration: f32) {
        self.shake_intensity = intensity.max(0.0);
        self.shake_duration = duration.max(0.0);
    }

    /// ### Updates the camera with delta time.
    ///
    /// This should be called every frame to update camera smoothing,
    /// screen shake, and other time-based effects.
    ///
    /// # Parameters
    /// - `delta`: Time elapsed since last update in seconds
    /// - `target_position`: Optional target position to follow
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::camera2d::Camera2D;
    /// # use verturion::core::math::Vector2;
    /// let mut camera = Camera2D::new("Camera");
    /// let player_pos = Vector2::new(100.0, 50.0);
    /// camera.update(0.016, Some(player_pos)); // 60 FPS update
    /// ```
    #[inline]
    pub fn update(&mut self, delta: f32, target_position: Option<Vector2>) {
        // Update screen shake
        if self.shake_duration > 0.0 {
            self.shake_duration -= delta;
            if self.shake_duration <= 0.0 {
                self.shake_offset = Vector2::new(0.0, 0.0);
                self.shake_intensity = 0.0;
            } else {
                // Simple random shake offset
                let shake_x = (self.shake_intensity * (self.shake_duration.sin() * 1000.0).sin()).clamp(-self.shake_intensity, self.shake_intensity);
                let shake_y = (self.shake_intensity * (self.shake_duration.cos() * 1000.0).cos()).clamp(-self.shake_intensity, self.shake_intensity);
                self.shake_offset = Vector2::new(shake_x, shake_y);
            }
        }

        // Update camera position based on target
        if let Some(target) = target_position {
            let desired_position = self.calculate_desired_position(target);

            if self.smoothing_enabled && self.smoothing_speed > 0.0 {
                // Smooth camera movement
                let current_pos = self.get_position();
                let lerp_factor = (self.smoothing_speed * delta).min(1.0);
                let new_position = current_pos.lerp(desired_position, lerp_factor);
                self.set_position(new_position);
            } else {
                // Instant camera movement
                self.set_position(desired_position);
            }
        }
    }

    /// ### Calculates the desired camera position based on target and settings.
    ///
    /// # Parameters
    /// - `target`: The target position to follow
    ///
    /// # Returns
    /// The calculated desired camera position.
    #[inline]
    pub fn calculate_desired_position(&self, target: Vector2) -> Vector2 {
        let mut desired = match self.anchor_mode {
            AnchorMode::FixedTopLeft => target + self.offset,
            AnchorMode::DragCenter => target + self.offset,
        };

        // Apply viewport limits
        desired.x = desired.x.clamp(self.limit_left, self.limit_right);
        desired.y = desired.y.clamp(self.limit_top, self.limit_bottom);

        // Apply screen shake
        desired + self.shake_offset
    }

    /// ### Forces the camera to a specific position immediately.
    ///
    /// This bypasses smoothing and immediately sets the camera position.
    ///
    /// # Parameters
    /// - `position`: The new camera position
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::Camera2D;
    /// # use verturion::core::math::Vector2;
    /// let mut camera = Camera2D::new("Camera");
    /// camera.force_update_scroll(Vector2::new(500.0, 300.0));
    /// assert_eq!(camera.get_position(), Vector2::new(500.0, 300.0));
    /// ```
    #[inline]
    pub fn force_update_scroll(&mut self, position: Vector2) {
        self.set_position(position);
    }

    /// ### Resets camera smoothing.
    ///
    /// This clears any smoothing state and makes the camera immediately
    /// snap to its target on the next update.
    #[inline]
    pub fn reset_smoothing(&mut self) {
        // In a more complex implementation, this would clear smoothing state
        // For now, it's a placeholder for future smoothing state management
    }

    /// ### Gets the current screen shake offset.
    ///
    /// # Returns
    /// The current shake offset applied to the camera.
    #[inline]
    pub fn get_shake_offset(&self) -> Vector2 {
        self.shake_offset
    }

    /// ### Checks if the camera is currently shaking.
    ///
    /// # Returns
    /// True if screen shake is active, false otherwise.
    #[inline]
    pub fn is_shaking(&self) -> bool {
        self.shake_duration > 0.0
    }

    /// ### Provides access to the base Node2D functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node2D.
    #[inline]
    pub fn base(&self) -> &Node2D {
        &self.base
    }

    /// ### Provides mutable access to the base Node2D functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node2D.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node2D {
        &mut self.base
    }

    /// ### Gets the node name from the base Node2D.
    ///
    /// # Returns
    /// The name of this camera node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }

    /// ### Gets the position from the base Node2D.
    ///
    /// # Returns
    /// The current position of the camera.
    #[inline]
    pub fn get_position(&self) -> Vector2 {
        self.base.get_position()
    }

    /// ### Sets the position in the base Node2D.
    ///
    /// # Parameters
    /// - `position`: The new position for the camera
    #[inline]
    pub fn set_position(&mut self, position: Vector2) {
        self.base.set_position(position);
    }
}

impl fmt::Display for Camera2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Camera2D({}, current: {}, zoom: {}, pos: {})",
               self.get_name(),
               self.current,
               self.zoom,
               self.get_position())
    }
}

impl PartialEq for Camera2D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Camera2D {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_camera2d_creation() {
        let camera = Camera2D::new("TestCamera");
        assert_eq!(camera.get_name(), "TestCamera");
        assert!(!camera.is_current());
        assert_eq!(camera.get_zoom(), Vector2::new(1.0, 1.0));
        assert_eq!(camera.get_offset(), Vector2::new(0.0, 0.0));
        assert_eq!(camera.get_anchor_mode(), AnchorMode::DragCenter);
        assert!(!camera.is_smoothing_enabled());
        assert_eq!(camera.get_smoothing_speed(), 5.0);
        assert!(camera.is_drag_margin_enabled());
        assert!(!camera.is_shaking());
    }

    #[test]
    fn test_camera2d_current() {
        let mut camera = Camera2D::new("Camera");

        // Initially not current
        assert!(!camera.is_current());

        // Set current
        camera.set_current(true);
        assert!(camera.is_current());

        // Unset current
        camera.set_current(false);
        assert!(!camera.is_current());
    }

    #[test]
    fn test_camera2d_zoom() {
        let mut camera = Camera2D::new("Camera");

        // Initially 1.0, 1.0
        assert_eq!(camera.get_zoom(), Vector2::new(1.0, 1.0));

        // Set custom zoom
        camera.set_zoom(Vector2::new(2.0, 1.5));
        assert_eq!(camera.get_zoom(), Vector2::new(2.0, 1.5));

        // Test minimum zoom clamping
        camera.set_zoom(Vector2::new(0.0, -1.0));
        assert_eq!(camera.get_zoom(), Vector2::new(0.01, 0.01));
    }

    #[test]
    fn test_camera2d_offset() {
        let mut camera = Camera2D::new("Camera");

        // Initially zero
        assert_eq!(camera.get_offset(), Vector2::new(0.0, 0.0));

        // Set custom offset
        camera.set_offset(Vector2::new(50.0, -25.0));
        assert_eq!(camera.get_offset(), Vector2::new(50.0, -25.0));
    }

    #[test]
    fn test_camera2d_anchor_mode() {
        let mut camera = Camera2D::new("Camera");

        // Initially DragCenter
        assert_eq!(camera.get_anchor_mode(), AnchorMode::DragCenter);

        // Set FixedTopLeft
        camera.set_anchor_mode(AnchorMode::FixedTopLeft);
        assert_eq!(camera.get_anchor_mode(), AnchorMode::FixedTopLeft);

        // Set back to DragCenter
        camera.set_anchor_mode(AnchorMode::DragCenter);
        assert_eq!(camera.get_anchor_mode(), AnchorMode::DragCenter);
    }

    #[test]
    fn test_camera2d_smoothing() {
        let mut camera = Camera2D::new("Camera");

        // Initially disabled
        assert!(!camera.is_smoothing_enabled());
        assert_eq!(camera.get_smoothing_speed(), 5.0);

        // Enable smoothing
        camera.set_smoothing_enabled(true);
        assert!(camera.is_smoothing_enabled());

        // Set custom speed
        camera.set_smoothing_speed(10.0);
        assert_eq!(camera.get_smoothing_speed(), 10.0);

        // Test negative speed clamping
        camera.set_smoothing_speed(-5.0);
        assert_eq!(camera.get_smoothing_speed(), 0.0);
    }

    #[test]
    fn test_camera2d_limits() {
        let mut camera = Camera2D::new("Camera");

        // Test default limits (very large)
        assert_eq!(camera.get_limit_left(), -10000000.0);
        assert_eq!(camera.get_limit_right(), 10000000.0);
        assert_eq!(camera.get_limit_top(), -10000000.0);
        assert_eq!(camera.get_limit_bottom(), 10000000.0);
        assert!(!camera.is_limit_smoothed());

        // Set custom limits
        camera.set_limit_left(-500.0);
        camera.set_limit_right(500.0);
        camera.set_limit_top(-300.0);
        camera.set_limit_bottom(300.0);
        camera.set_limit_smoothed(true);

        assert_eq!(camera.get_limit_left(), -500.0);
        assert_eq!(camera.get_limit_right(), 500.0);
        assert_eq!(camera.get_limit_top(), -300.0);
        assert_eq!(camera.get_limit_bottom(), 300.0);
        assert!(camera.is_limit_smoothed());
    }

    #[test]
    fn test_camera2d_drag_margins() {
        let mut camera = Camera2D::new("Camera");

        // Test default margins
        assert_eq!(camera.get_drag_margin_left(), 0.2);
        assert_eq!(camera.get_drag_margin_right(), 0.2);
        assert_eq!(camera.get_drag_margin_top(), 0.2);
        assert_eq!(camera.get_drag_margin_bottom(), 0.2);
        assert!(camera.is_drag_margin_enabled());

        // Set custom margins
        camera.set_drag_margin_left(0.1);
        camera.set_drag_margin_right(0.3);
        camera.set_drag_margin_top(0.15);
        camera.set_drag_margin_bottom(0.25);
        camera.set_drag_margin_enabled(false);

        assert_eq!(camera.get_drag_margin_left(), 0.1);
        assert_eq!(camera.get_drag_margin_right(), 0.3);
        assert_eq!(camera.get_drag_margin_top(), 0.15);
        assert_eq!(camera.get_drag_margin_bottom(), 0.25);
        assert!(!camera.is_drag_margin_enabled());

        // Test clamping (should be 0.0 to 1.0)
        camera.set_drag_margin_left(-0.5);
        camera.set_drag_margin_right(1.5);
        assert_eq!(camera.get_drag_margin_left(), 0.0);
        assert_eq!(camera.get_drag_margin_right(), 1.0);
    }

    #[test]
    fn test_camera2d_screen_shake() {
        let mut camera = Camera2D::new("Camera");

        // Initially not shaking
        assert!(!camera.is_shaking());
        assert_eq!(camera.get_shake_offset(), Vector2::new(0.0, 0.0));

        // Start shake
        camera.shake(10.0, 1.0);
        assert!(camera.is_shaking());

        // Update to progress shake
        camera.update(0.5, None);
        assert!(camera.is_shaking());

        // Update to finish shake
        camera.update(1.0, None);
        assert!(!camera.is_shaking());
        assert_eq!(camera.get_shake_offset(), Vector2::new(0.0, 0.0));

        // Test negative values (should be clamped)
        camera.shake(-5.0, -1.0);
        assert!(!camera.is_shaking());
    }

    #[test]
    fn test_camera2d_position_calculation() {
        let mut camera = Camera2D::new("Camera");
        let target = Vector2::new(100.0, 50.0);

        // Test basic position calculation
        let desired = camera.calculate_desired_position(target);
        assert_eq!(desired, target); // No offset, no shake

        // Test with offset
        camera.set_offset(Vector2::new(25.0, -10.0));
        let desired = camera.calculate_desired_position(target);
        assert_eq!(desired, Vector2::new(125.0, 40.0));

        // Test with limits
        camera.set_limit_left(0.0);
        camera.set_limit_right(100.0);
        camera.set_limit_top(0.0);
        camera.set_limit_bottom(50.0);

        let desired = camera.calculate_desired_position(target);
        assert_eq!(desired, Vector2::new(100.0, 40.0)); // Clamped to limits
    }

    #[test]
    fn test_camera2d_update_without_target() {
        let mut camera = Camera2D::new("Camera");
        let initial_pos = camera.get_position();

        // Update without target (should not move)
        camera.update(0.016, None);
        assert_eq!(camera.get_position(), initial_pos);
    }

    #[test]
    fn test_camera2d_update_with_target() {
        let mut camera = Camera2D::new("Camera");
        let target = Vector2::new(100.0, 50.0);

        // Update with target (no smoothing)
        camera.update(0.016, Some(target));
        assert_eq!(camera.get_position(), target);

        // Test with smoothing
        camera.set_smoothing_enabled(true);
        camera.set_smoothing_speed(1.0);
        camera.set_position(Vector2::new(0.0, 0.0));

        // Should move towards target but not reach it immediately
        camera.update(0.016, Some(target));
        let pos = camera.get_position();
        assert!(pos.x > 0.0 && pos.x < target.x);
        assert!(pos.y > 0.0 && pos.y < target.y);
    }

    #[test]
    fn test_camera2d_force_update_scroll() {
        let mut camera = Camera2D::new("Camera");
        let position = Vector2::new(200.0, 150.0);

        // Force position update
        camera.force_update_scroll(position);
        assert_eq!(camera.get_position(), position);
    }

    #[test]
    fn test_camera2d_reset_smoothing() {
        let mut camera = Camera2D::new("Camera");

        // Reset smoothing (placeholder test)
        camera.reset_smoothing();
        // This is a placeholder method, so just ensure it doesn't crash
    }

    #[test]
    fn test_camera2d_base_access() {
        let mut camera = Camera2D::new("Camera");

        // Test base access
        assert_eq!(camera.base().base().get_name(), "Camera");

        // Test mutable base access
        camera.base_mut().base_mut().set_name("NewCamera");
        assert_eq!(camera.get_name(), "NewCamera");
    }

    #[test]
    fn test_camera2d_equality() {
        let camera1 = Camera2D::new("Camera1");
        let camera2 = Camera2D::new("Camera2");
        let camera1_clone = camera1.clone();

        // Same camera should be equal
        assert_eq!(camera1, camera1_clone);

        // Different cameras should not be equal
        assert_ne!(camera1, camera2);
    }

    #[test]
    fn test_camera2d_display() {
        let mut camera = Camera2D::new("TestCamera");
        camera.set_current(true);
        camera.set_zoom(Vector2::new(2.0, 1.5));
        camera.set_position(Vector2::new(100.0, 50.0));

        let display_str = format!("{}", camera);
        assert!(display_str.contains("TestCamera"));
        assert!(display_str.contains("current: true"));
        assert!(display_str.contains("zoom: (2, 1.5)"));
        assert!(display_str.contains("pos: (100, 50)"));
    }

    #[test]
    fn test_camera2d_complex_scenario() {
        let mut camera = Camera2D::new("GameCamera");

        // Configure camera for game scenario
        camera.set_current(true);
        camera.set_zoom(Vector2::new(1.2, 1.2));
        camera.set_smoothing_enabled(true);
        camera.set_smoothing_speed(3.0);
        camera.set_limit_left(-500.0);
        camera.set_limit_right(500.0);
        camera.set_limit_top(-300.0);
        camera.set_limit_bottom(300.0);
        camera.set_offset(Vector2::new(0.0, -50.0)); // Look ahead

        // Simulate player movement
        let player_positions = vec![
            Vector2::new(0.0, 0.0),
            Vector2::new(100.0, 0.0),
            Vector2::new(200.0, 50.0),
            Vector2::new(600.0, 100.0), // Beyond limit
        ];

        for player_pos in player_positions {
            camera.update(0.016, Some(player_pos));

            // Camera should respect limits
            let cam_pos = camera.get_position();
            assert!(cam_pos.x >= -500.0 && cam_pos.x <= 500.0);
            assert!(cam_pos.y >= -300.0 && cam_pos.y <= 300.0);
        }

        // Test screen shake during action
        camera.shake(15.0, 0.3);
        assert!(camera.is_shaking());

        // Update through shake duration
        for _ in 0..20 {
            camera.update(0.016, Some(Vector2::new(0.0, 0.0)));
        }

        // Shake should be finished
        assert!(!camera.is_shaking());

        // Verify final configuration
        assert!(camera.is_current());
        assert_eq!(camera.get_zoom(), Vector2::new(1.2, 1.2));
        assert!(camera.is_smoothing_enabled());
        assert_eq!(camera.get_smoothing_speed(), 3.0);
    }
}
