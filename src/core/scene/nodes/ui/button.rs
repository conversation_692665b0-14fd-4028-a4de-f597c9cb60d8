//! Button implementation for interactive UI elements with comprehensive state management.
//!
//! This module provides the Button class that extends Control with interactive
//! button functionality including pressed/released states, text/icon support,
//! and signal emission. It maintains full compatibility with Godot's Button class
//! while providing efficient interactive UI element management.

use std::fmt;
use crate::core::math::Vector2;
use crate::core::variant::{Color, String as GodotString};
use crate::core::scene::nodes::Control;
use crate::core::signal::{Signal, SignalManager, SignalData};

/// ### Button interaction states for visual feedback.
///
/// Defines the current interaction state of the button.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum ButtonState {
    /// Button is in normal state
    Normal,
    /// But<PERSON> is being hovered over
    Hovered,
    /// But<PERSON> is being pressed
    Pressed,
    /// But<PERSON> is disabled and cannot be interacted with
    Disabled,
    /// Button is focused (keyboard navigation)
    Focused,
}

/// ### Button action modes for different interaction behaviors.
///
/// Defines how the button responds to user input.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq)]
#[allow(dead_code)] // Complete button action mode options for comprehensive UI API
pub enum ButtonActionMode {
    /// Button triggers on press down
    Press,
    /// Button triggers on release
    Release,
}

/// ### Interactive button for user interface with comprehensive state management.
///
/// Button extends Control with comprehensive interactive functionality,
/// providing pressed/released states, text/icon display, visual feedback,
/// and signal emission. It maintains full compatibility with Godot's Button
/// class while ensuring efficient interactive UI element management.
///
/// ## Core Features
///
/// - **Interactive States**: Normal, hovered, pressed, disabled, focused states
/// - **Text Display**: Button text with font and color customization
/// - **Icon Support**: Optional icon display alongside text
/// - **Visual Feedback**: State-based visual styling and animations
/// - **Signal Emission**: Button press and release event handling
/// - **Keyboard Support**: Focus and keyboard activation
/// - **Godot Compatibility**: API matching Godot's Button class
///
/// ## Button Properties
///
/// Button provides comprehensive interaction management:
/// - **Text**: Button label text content
/// - **Icon**: Optional icon texture resource
/// - **State**: Current interaction state (normal, hovered, pressed, etc.)
/// - **Action Mode**: When the button triggers (press or release)
/// - **Disabled**: Whether the button can be interacted with
/// - **Toggle Mode**: Whether the button acts as a toggle
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::ui::{Button, ButtonActionMode};
/// # use verturion::core::variant::{Color, String as GodotString};
/// # use verturion::core::math::Vector2;
/// // Create a button
/// let mut button = Button::new("PlayButton");
///
/// // Configure button properties
/// button.set_text(GodotString::from("Play Game"));
/// button.set_action_mode(ButtonActionMode::Release);
/// button.set_size(Vector2::new(120.0, 40.0));
///
/// // Handle button interaction
/// if button.is_pressed() {
///     println!("Button was pressed!");
/// }
///
/// assert_eq!(button.get_text().as_str(), "Play Game");
/// assert!(!button.is_disabled());
/// ```
#[derive(Debug, Clone)]
#[allow(dead_code)] // Comprehensive implementation - fields will be used when full button functionality is implemented
pub struct Button {
    /// Base Control functionality
    base: Control,
    /// Button text content
    text: GodotString,
    /// Icon texture resource path
    icon: Option<String>,
    /// Current button state
    state: ButtonState,
    /// Button action mode (press or release)
    action_mode: ButtonActionMode,
    /// Whether the button is disabled
    disabled: bool,
    /// Whether the button is in toggle mode
    toggle_mode: bool,
    /// Whether the button is currently toggled (if in toggle mode)
    button_pressed: bool,
    /// Whether the button is flat (no background)
    flat: bool,
    /// Text color for different states
    font_color: Color,
    /// Font size for button text
    font_size: i32,
    /// Whether to keep the button pressed visually
    keep_pressed_outside: bool,
    /// Signal emitted when button is pressed
    pressed_signal: Signal,
    /// Signal emitted when button is released
    released_signal: Signal,
    /// Signal emitted when button is toggled
    toggled_signal: Signal,
}

#[allow(dead_code)] // Comprehensive button implementation awaiting integration
impl Button {
    /// ### Creates a new Button with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this button node
    ///
    /// # Returns
    /// A new Button instance with default interactive properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::Button;
    /// let button = Button::new("MyButton");
    /// assert_eq!(button.get_name(), "MyButton");
    /// assert_eq!(button.get_text().as_str(), "");
    /// assert!(!button.is_disabled());
    /// assert!(!button.is_pressed());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let node_id = Control::new(name).base().get_id();
        Self {
            base: Control::new(name),
            text: GodotString::from(""),
            icon: None,
            state: ButtonState::Normal,
            action_mode: ButtonActionMode::Release,
            disabled: false,
            toggle_mode: false,
            button_pressed: false,
            flat: false,
            font_color: Color::WHITE,
            font_size: 16,
            keep_pressed_outside: false,
            pressed_signal: Signal::new("pressed", node_id),
            released_signal: Signal::new("released", node_id),
            toggled_signal: Signal::new("toggled", node_id),
        }
    }

    /// ### Gets the button text.
    ///
    /// # Returns
    /// The current button text content.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::Button;
    /// let button = Button::new("Button");
    /// assert_eq!(button.get_text().as_str(), "");
    /// ```
    #[inline]
    pub fn get_text(&self) -> &GodotString {
        &self.text
    }

    /// ### Sets the button text.
    ///
    /// # Parameters
    /// - `text`: The new button text content
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::Button;
    /// # use verturion::core::variant::String as GodotString;
    /// let mut button = Button::new("Button");
    /// button.set_text(GodotString::from("Click Me"));
    /// assert_eq!(button.get_text().as_str(), "Click Me");
    /// ```
    #[inline]
    pub fn set_text(&mut self, text: GodotString) {
        self.text = text;
    }

    /// ### Gets the button icon path.
    ///
    /// # Returns
    /// The current icon path, or None if no icon is set.
    #[inline]
    pub fn get_icon(&self) -> Option<&String> {
        self.icon.as_ref()
    }

    /// ### Sets the button icon.
    ///
    /// # Parameters
    /// - `icon`: The icon path, or None to clear the icon
    #[inline]
    pub fn set_icon(&mut self, icon: Option<String>) {
        self.icon = icon;
    }

    /// ### Gets the current button state.
    ///
    /// # Returns
    /// The current button interaction state.
    #[inline]
    pub fn get_state(&self) -> ButtonState {
        self.state
    }

    /// ### Sets the button state.
    ///
    /// # Parameters
    /// - `state`: The new button state
    #[inline]
    pub fn set_state(&mut self, state: ButtonState) {
        self.state = state;
    }

    /// ### Gets the button action mode.
    ///
    /// # Returns
    /// The current action mode (press or release).
    #[inline]
    pub fn get_action_mode(&self) -> ButtonActionMode {
        self.action_mode
    }

    /// ### Sets the button action mode.
    ///
    /// # Parameters
    /// - `mode`: The new action mode
    #[inline]
    pub fn set_action_mode(&mut self, mode: ButtonActionMode) {
        self.action_mode = mode;
    }

    /// ### Checks if the button is disabled.
    ///
    /// # Returns
    /// True if the button is disabled, false otherwise.
    #[inline]
    pub fn is_disabled(&self) -> bool {
        self.disabled
    }

    /// ### Sets the button disabled state.
    ///
    /// # Parameters
    /// - `disabled`: Whether the button should be disabled
    #[inline]
    pub fn set_disabled(&mut self, disabled: bool) {
        self.disabled = disabled;
        if disabled {
            self.state = ButtonState::Disabled;
        } else if self.state == ButtonState::Disabled {
            self.state = ButtonState::Normal;
        }
    }

    /// ### Checks if the button is in toggle mode.
    ///
    /// # Returns
    /// True if toggle mode is enabled, false otherwise.
    #[inline]
    pub fn is_toggle_mode(&self) -> bool {
        self.toggle_mode
    }

    /// ### Sets the toggle mode.
    ///
    /// # Parameters
    /// - `toggle`: Whether to enable toggle mode
    #[inline]
    pub fn set_toggle_mode(&mut self, toggle: bool) {
        self.toggle_mode = toggle;
        if !toggle {
            self.button_pressed = false;
        }
    }

    /// ### Checks if the button is currently pressed.
    ///
    /// # Returns
    /// True if the button is pressed (or toggled), false otherwise.
    #[inline]
    pub fn is_pressed(&self) -> bool {
        self.button_pressed || self.state == ButtonState::Pressed
    }

    /// ### Sets the button pressed state (for toggle mode).
    ///
    /// # Parameters
    /// - `pressed`: Whether the button should be pressed
    #[inline]
    pub fn set_pressed(&mut self, pressed: bool) {
        if self.toggle_mode {
            self.button_pressed = pressed;
        }
    }

    /// ### Checks if the button is flat (no background).
    ///
    /// # Returns
    /// True if the button is flat, false otherwise.
    #[inline]
    pub fn is_flat(&self) -> bool {
        self.flat
    }

    /// ### Sets the flat mode.
    ///
    /// # Parameters
    /// - `flat`: Whether the button should be flat
    #[inline]
    pub fn set_flat(&mut self, flat: bool) {
        self.flat = flat;
    }

    /// ### Gets the font color.
    ///
    /// # Returns
    /// The current font color.
    #[inline]
    pub fn get_font_color(&self) -> Color {
        self.font_color
    }

    /// ### Sets the font color.
    ///
    /// # Parameters
    /// - `color`: The new font color
    #[inline]
    pub fn set_font_color(&mut self, color: Color) {
        self.font_color = color;
    }

    /// ### Gets the font size.
    ///
    /// # Returns
    /// The current font size in pixels.
    #[inline]
    pub fn get_font_size(&self) -> i32 {
        self.font_size
    }

    /// ### Sets the font size.
    ///
    /// # Parameters
    /// - `size`: The new font size in pixels
    #[inline]
    pub fn set_font_size(&mut self, size: i32) {
        self.font_size = size.max(1);
    }

    /// ### Gets the pressed signal.
    ///
    /// # Returns
    /// A reference to the pressed signal.
    #[inline]
    pub fn get_pressed_signal(&self) -> &Signal {
        &self.pressed_signal
    }

    /// ### Gets the released signal.
    ///
    /// # Returns
    /// A reference to the released signal.
    #[inline]
    pub fn get_released_signal(&self) -> &Signal {
        &self.released_signal
    }

    /// ### Gets the toggled signal.
    ///
    /// # Returns
    /// A reference to the toggled signal.
    #[inline]
    pub fn get_toggled_signal(&self) -> &Signal {
        &self.toggled_signal
    }

    /// ### Simulates a button press and emits appropriate signals.
    ///
    /// This method can be used to programmatically trigger the button.
    /// It will emit the pressed signal and handle toggle mode appropriately.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager to emit signals through
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::Button;
    /// # use verturion::core::signal::SignalManager;
    /// let mut button = Button::new("TestButton");
    /// let mut manager = SignalManager::new();
    ///
    /// // Register button signals
    /// manager.register_signal(button.get_pressed_signal().clone());
    ///
    /// // Emit button press
    /// button.emit_pressed(&mut manager);
    /// ```
    #[inline]
    pub fn emit_pressed(&mut self, signal_manager: &mut SignalManager) {
        if !self.disabled {
            // Emit pressed signal
            signal_manager.emit(self.pressed_signal.id(), SignalData::empty());

            if self.toggle_mode {
                let old_pressed = self.button_pressed;
                self.button_pressed = !self.button_pressed;

                // Emit toggled signal with the new state
                let mut data = SignalData::empty();
                data.add_arg(crate::core::variant::Variant::from(self.button_pressed));
                signal_manager.emit(self.toggled_signal.id(), data);

                // If we're now released, emit released signal
                if old_pressed && !self.button_pressed {
                    signal_manager.emit(self.released_signal.id(), SignalData::empty());
                }
            } else {
                // For non-toggle buttons, always emit released after pressed
                signal_manager.emit(self.released_signal.id(), SignalData::empty());
            }
        }
    }

    /// ### Provides access to the base Control functionality.
    ///
    /// # Returns
    /// A reference to the underlying Control.
    #[inline]
    pub fn base(&self) -> &Control {
        &self.base
    }

    /// ### Provides mutable access to the base Control functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Control.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Control {
        &mut self.base
    }

    /// ### Gets the node name from the base Control.
    ///
    /// # Returns
    /// The name of this button node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }

    /// ### Gets the size from the base Control.
    ///
    /// # Returns
    /// The current size of the button.
    #[inline]
    pub fn get_size(&self) -> Vector2 {
        self.base.get_size()
    }

    /// ### Sets the size in the base Control.
    ///
    /// # Parameters
    /// - `size`: The new size for the button
    #[inline]
    pub fn set_size(&mut self, size: Vector2) {
        self.base.set_size(size);
    }
}

impl fmt::Display for Button {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Button({}, text: \"{}\", state: {:?}, disabled: {})",
               self.get_name(), self.text.as_str(), self.state, self.disabled)
    }
}

impl PartialEq for Button {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Button {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_button_creation() {
        let button = Button::new("TestButton");
        assert_eq!(button.get_name(), "TestButton");
        assert_eq!(button.get_text().as_str(), "");
        assert_eq!(button.get_state(), ButtonState::Normal);
        assert_eq!(button.get_action_mode(), ButtonActionMode::Release);
        assert!(!button.is_disabled());
        assert!(!button.is_toggle_mode());
        assert!(!button.is_pressed());
        assert!(!button.is_flat());
    }

    #[test]
    fn test_button_text_management() {
        let mut button = Button::new("Button");

        // Initially empty text
        assert_eq!(button.get_text().as_str(), "");

        // Set text
        button.set_text(GodotString::from("Click Me"));
        assert_eq!(button.get_text().as_str(), "Click Me");

        // Update text
        button.set_text(GodotString::from("Press Here"));
        assert_eq!(button.get_text().as_str(), "Press Here");

        // Clear text
        button.set_text(GodotString::from(""));
        assert_eq!(button.get_text().as_str(), "");
    }

    #[test]
    fn test_button_icon_management() {
        let mut button = Button::new("Button");

        // Initially no icon
        assert_eq!(button.get_icon(), None);

        // Set icon
        button.set_icon(Some("res://icons/play.png".to_string()));
        assert_eq!(button.get_icon(), Some(&"res://icons/play.png".to_string()));

        // Clear icon
        button.set_icon(None);
        assert_eq!(button.get_icon(), None);
    }

    #[test]
    fn test_button_state_management() {
        let mut button = Button::new("Button");

        // Initially normal state
        assert_eq!(button.get_state(), ButtonState::Normal);

        // Test all states
        button.set_state(ButtonState::Hovered);
        assert_eq!(button.get_state(), ButtonState::Hovered);

        button.set_state(ButtonState::Pressed);
        assert_eq!(button.get_state(), ButtonState::Pressed);

        button.set_state(ButtonState::Focused);
        assert_eq!(button.get_state(), ButtonState::Focused);

        button.set_state(ButtonState::Disabled);
        assert_eq!(button.get_state(), ButtonState::Disabled);

        button.set_state(ButtonState::Normal);
        assert_eq!(button.get_state(), ButtonState::Normal);
    }

    #[test]
    fn test_button_action_mode() {
        let mut button = Button::new("Button");

        // Initially release mode
        assert_eq!(button.get_action_mode(), ButtonActionMode::Release);

        // Set press mode
        button.set_action_mode(ButtonActionMode::Press);
        assert_eq!(button.get_action_mode(), ButtonActionMode::Press);

        // Back to release mode
        button.set_action_mode(ButtonActionMode::Release);
        assert_eq!(button.get_action_mode(), ButtonActionMode::Release);
    }

    #[test]
    fn test_button_disabled_state() {
        let mut button = Button::new("Button");

        // Initially enabled
        assert!(!button.is_disabled());
        assert_eq!(button.get_state(), ButtonState::Normal);

        // Disable button
        button.set_disabled(true);
        assert!(button.is_disabled());
        assert_eq!(button.get_state(), ButtonState::Disabled);

        // Re-enable button
        button.set_disabled(false);
        assert!(!button.is_disabled());
        assert_eq!(button.get_state(), ButtonState::Normal);
    }

    #[test]
    fn test_button_toggle_mode() {
        let mut button = Button::new("Button");

        // Initially not toggle mode
        assert!(!button.is_toggle_mode());
        assert!(!button.is_pressed());

        // Enable toggle mode
        button.set_toggle_mode(true);
        assert!(button.is_toggle_mode());

        // Test toggle functionality
        button.set_pressed(true);
        assert!(button.is_pressed());

        button.set_pressed(false);
        assert!(!button.is_pressed());

        // Disable toggle mode (should clear pressed state)
        button.set_pressed(true);
        button.set_toggle_mode(false);
        assert!(!button.is_toggle_mode());
        assert!(!button.is_pressed());
    }

    #[test]
    fn test_button_flat_mode() {
        let mut button = Button::new("Button");

        // Initially not flat
        assert!(!button.is_flat());

        // Enable flat mode
        button.set_flat(true);
        assert!(button.is_flat());

        // Disable flat mode
        button.set_flat(false);
        assert!(!button.is_flat());
    }

    #[test]
    fn test_button_font_properties() {
        let mut button = Button::new("Button");

        // Initially default font properties
        assert_eq!(button.get_font_color(), Color::WHITE);
        assert_eq!(button.get_font_size(), 16);

        // Set custom font color
        let blue_color = Color::new(0.0, 0.5, 1.0, 1.0);
        button.set_font_color(blue_color);
        assert_eq!(button.get_font_color(), blue_color);

        // Set font size
        button.set_font_size(20);
        assert_eq!(button.get_font_size(), 20);

        // Test minimum font size
        button.set_font_size(0);
        assert_eq!(button.get_font_size(), 1); // Should be clamped to 1
    }

    #[test]
    fn test_button_emit_pressed() {
        use crate::core::signal::SignalManager;

        let mut button = Button::new("Button");
        let mut signal_manager = SignalManager::new();

        // Register button signals
        signal_manager.register_signal(button.get_pressed_signal().clone());
        signal_manager.register_signal(button.get_released_signal().clone());
        signal_manager.register_signal(button.get_toggled_signal().clone());

        // Test normal button press
        button.emit_pressed(&mut signal_manager);
        // In a real implementation, this would emit a signal

        // Test toggle button press
        button.set_toggle_mode(true);
        assert!(!button.is_pressed());

        button.emit_pressed(&mut signal_manager);
        assert!(button.is_pressed());

        button.emit_pressed(&mut signal_manager);
        assert!(!button.is_pressed());

        // Test disabled button (should not respond)
        button.set_disabled(true);
        button.emit_pressed(&mut signal_manager);
        assert!(!button.is_pressed()); // Should remain unchanged
    }

    #[test]
    fn test_button_base_access() {
        let mut button = Button::new("Button");

        // Test base access
        assert_eq!(button.base().base().get_name(), "Button");

        // Test size management through base
        button.set_size(Vector2::new(120.0, 40.0));
        assert_eq!(button.get_size(), Vector2::new(120.0, 40.0));
    }

    #[test]
    fn test_button_equality() {
        let button1 = Button::new("Button1");
        let button2 = Button::new("Button2");
        let button1_clone = button1.clone();

        // Same button should be equal
        assert_eq!(button1, button1_clone);

        // Different buttons should not be equal
        assert_ne!(button1, button2);
    }

    #[test]
    fn test_button_display() {
        let mut button = Button::new("TestButton");
        button.set_text(GodotString::from("Click"));
        button.set_state(ButtonState::Hovered);
        button.set_disabled(false);

        let display_str = format!("{}", button);
        assert!(display_str.contains("TestButton"));
        assert!(display_str.contains("Click"));
        assert!(display_str.contains("Hovered"));
        assert!(display_str.contains("disabled: false"));
    }

    #[test]
    fn test_button_complex_configuration() {
        let mut button = Button::new("ComplexButton");

        // Configure all properties
        button.set_text(GodotString::from("Play Game"));
        button.set_icon(Some("res://icons/play.png".to_string()));
        button.set_action_mode(ButtonActionMode::Press);
        button.set_toggle_mode(true);
        button.set_flat(true);
        button.set_font_color(Color::new(0.9, 0.9, 0.1, 1.0));
        button.set_font_size(18);
        button.set_size(Vector2::new(150.0, 50.0));

        // Verify all properties
        assert_eq!(button.get_text().as_str(), "Play Game");
        assert_eq!(button.get_icon(), Some(&"res://icons/play.png".to_string()));
        assert_eq!(button.get_action_mode(), ButtonActionMode::Press);
        assert!(button.is_toggle_mode());
        assert!(button.is_flat());
        assert_eq!(button.get_font_color(), Color::new(0.9, 0.9, 0.1, 1.0));
        assert_eq!(button.get_font_size(), 18);
        assert_eq!(button.get_size(), Vector2::new(150.0, 50.0));
    }
}
