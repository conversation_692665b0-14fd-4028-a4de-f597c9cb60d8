//! TabContainer implementation for organizing UI content into tabs.
//!
//! This module provides the TabContainer class that extends Control with
//! comprehensive tab management functionality including tab creation,
//! switching, content organization, and visual tab rendering. It maintains
//! full compatibility with God<PERSON>'s TabContainer class while providing
//! efficient tab-based UI organization.

use crate::core::math::Vector2;
use crate::core::scene::nodes::Control;
use crate::core::signal::{Signal, SignalManager, SignalData};
use crate::core::variant::Variant;

/// ### Tab information for TabContainer.
///
/// Represents a single tab with its title, content, and state.
#[derive(Debug, Clone)]
pub struct Tab {
    /// Tab title displayed on the tab button
    pub title: String,
    /// Whether the tab is visible
    pub visible: bool,
    /// Whether the tab is disabled
    pub disabled: bool,
    /// Tab icon (optional)
    pub icon: Option<String>,
    /// Content control for this tab
    pub content: Option<Control>,
}

impl Tab {
    /// ### Creates a new tab with the specified title.
    pub fn new(title: &str) -> Self {
        Self {
            title: title.to_string(),
            visible: true,
            disabled: false,
            icon: None,
            content: None,
        }
    }
}

/// ### TabContainer for organizing UI content into tabs.
///
/// TabContainer extends Control with comprehensive tab management functionality,
/// providing tab creation, switching, content organization, and visual tab
/// rendering. It maintains full compatibility with Godot's TabContainer class
/// while ensuring efficient tab-based UI organization and navigation.
///
/// ## Core Features
///
/// - **Tab Management**: Create, remove, and organize tabs
/// - **Content Organization**: Associate content with each tab
/// - **Tab Switching**: Interactive tab navigation
/// - **Visual Rendering**: Tab buttons and content display
/// - **Signal Integration**: Tab change and interaction signals
/// - **Customizable Appearance**: Tab styling and layout options
/// - **Godot Compatibility**: API matching Godot's TabContainer
///
/// ## Tab Properties
///
/// TabContainer provides comprehensive tab control:
/// - **Current Tab**: Currently active tab index
/// - **Tab Count**: Number of tabs in the container
/// - **Tab Titles**: Text displayed on tab buttons
/// - **Tab Visibility**: Whether individual tabs are shown
/// - **Tab Icons**: Optional icons for tab buttons
/// - **Content Areas**: Associated content for each tab
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::ui::TabContainer;
/// # use verturion::core::signal::SignalManager;
/// // Create a tab container
/// let mut tab_container = TabContainer::new("MainTabs");
/// let mut signal_manager = SignalManager::new();
///
/// // Add tabs
/// tab_container.add_tab("Basic Controls");
/// tab_container.add_tab("Text Input");
/// tab_container.add_tab("Gamepad Input");
///
/// // Switch to a specific tab
/// tab_container.set_current_tab(1, &mut signal_manager);
///
/// assert_eq!(tab_container.get_tab_count(), 3);
/// assert_eq!(tab_container.get_current_tab(), 1);
/// assert_eq!(tab_container.get_tab_title(0), "Basic Controls");
/// ```
#[derive(Debug, Clone)]
#[allow(dead_code)] // Comprehensive implementation - fields will be used when full tab functionality is implemented
pub struct TabContainer {
    /// Base Control functionality
    base: Control,
    /// List of tabs
    tabs: Vec<Tab>,
    /// Currently active tab index
    current_tab: i32,
    /// Height of the tab bar
    tab_bar_height: f32,
    /// Whether tabs are shown at the top
    tabs_visible: bool,
    /// Tab alignment (left, center, right)
    tab_alignment: TabAlignment,
    /// Signal emitted when tab changes
    tab_changed_signal: Signal,
    /// Signal emitted when tab is selected
    tab_selected_signal: Signal,
}

/// ### Tab alignment options for TabContainer.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TabAlignment {
    /// Align tabs to the left
    Left,
    /// Center tabs
    Center,
    /// Align tabs to the right
    Right,
}

#[allow(dead_code)] // Comprehensive tab container implementation awaiting integration
impl TabContainer {
    /// ### Creates a new TabContainer with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this tab container control
    ///
    /// # Returns
    /// A new TabContainer instance with default tab properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let tab_container = TabContainer::new("MainTabs");
    /// assert_eq!(tab_container.get_name(), "MainTabs");
    /// assert_eq!(tab_container.get_tab_count(), 0);
    /// assert_eq!(tab_container.get_current_tab(), -1);
    /// assert!(tab_container.are_tabs_visible());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let control = Control::new(name);
        let node_id = control.base().get_id();
        Self {
            base: control,
            tabs: Vec::new(),
            current_tab: -1,
            tab_bar_height: 32.0,
            tabs_visible: true,
            tab_alignment: TabAlignment::Left,
            tab_changed_signal: Signal::new("tab_changed", node_id),
            tab_selected_signal: Signal::new("tab_selected", node_id),
        }
    }

    /// ### Gets the base Control node.
    ///
    /// # Returns
    /// Reference to the base Control functionality.
    #[inline]
    pub fn base(&self) -> &Control {
        &self.base
    }

    /// ### Gets the node name.
    ///
    /// # Returns
    /// The name of this tab container node.
    #[inline]
    pub fn get_name(&self) -> &str {
        self.base.get_name()
    }

    /// ### Adds a new tab with the specified title.
    ///
    /// # Parameters
    /// - `title`: The title for the new tab
    ///
    /// # Returns
    /// The index of the newly created tab.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// let index = tab_container.add_tab("New Tab");
    /// assert_eq!(index, 0);
    /// assert_eq!(tab_container.get_tab_count(), 1);
    /// assert_eq!(tab_container.get_tab_title(0), "New Tab");
    /// ```
    #[inline]
    pub fn add_tab(&mut self, title: &str) -> usize {
        let tab = Tab::new(title);
        self.tabs.push(tab);
        let index = self.tabs.len() - 1;
        
        // Set as current tab if it's the first tab
        if self.current_tab == -1 {
            self.current_tab = 0;
        }
        
        index
    }

    /// ### Removes a tab at the specified index.
    ///
    /// # Parameters
    /// - `index`: The index of the tab to remove
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// let mut signal_manager = SignalManager::new();
    /// tab_container.add_tab("Tab 1");
    /// tab_container.add_tab("Tab 2");
    /// tab_container.remove_tab(0, &mut signal_manager);
    /// assert_eq!(tab_container.get_tab_count(), 1);
    /// assert_eq!(tab_container.get_tab_title(0), "Tab 2");
    /// ```
    #[inline]
    pub fn remove_tab(&mut self, index: usize, signal_manager: &mut SignalManager) {
        if index < self.tabs.len() {
            self.tabs.remove(index);
            
            // Adjust current tab index
            if self.tabs.is_empty() {
                self.current_tab = -1;
            } else if self.current_tab >= self.tabs.len() as i32 {
                self.current_tab = (self.tabs.len() - 1) as i32;
                self.emit_tab_changed(signal_manager);
            }
        }
    }

    /// ### Gets the number of tabs.
    ///
    /// # Returns
    /// The total number of tabs in the container.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// assert_eq!(tab_container.get_tab_count(), 0);
    /// tab_container.add_tab("Tab 1");
    /// assert_eq!(tab_container.get_tab_count(), 1);
    /// ```
    #[inline]
    pub fn get_tab_count(&self) -> usize {
        self.tabs.len()
    }

    /// ### Gets the currently active tab index.
    ///
    /// # Returns
    /// The index of the current tab, or -1 if no tabs exist.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// assert_eq!(tab_container.get_current_tab(), -1);
    /// tab_container.add_tab("Tab 1");
    /// assert_eq!(tab_container.get_current_tab(), 0);
    /// ```
    #[inline]
    pub fn get_current_tab(&self) -> i32 {
        self.current_tab
    }

    /// ### Sets the currently active tab.
    ///
    /// # Parameters
    /// - `index`: The index of the tab to make active
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// # use verturion::core::signal::SignalManager;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// let mut signal_manager = SignalManager::new();
    /// tab_container.add_tab("Tab 1");
    /// tab_container.add_tab("Tab 2");
    /// tab_container.set_current_tab(1, &mut signal_manager);
    /// assert_eq!(tab_container.get_current_tab(), 1);
    /// ```
    #[inline]
    pub fn set_current_tab(&mut self, index: i32, signal_manager: &mut SignalManager) {
        if index >= 0 && (index as usize) < self.tabs.len() && index != self.current_tab {
            self.current_tab = index;
            self.emit_tab_changed(signal_manager);
        }
    }

    /// ### Gets the title of a tab at the specified index.
    ///
    /// # Parameters
    /// - `index`: The index of the tab
    ///
    /// # Returns
    /// The title of the tab, or empty string if index is invalid.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// tab_container.add_tab("My Tab");
    /// assert_eq!(tab_container.get_tab_title(0), "My Tab");
    /// assert_eq!(tab_container.get_tab_title(1), "");
    /// ```
    #[inline]
    pub fn get_tab_title(&self, index: usize) -> &str {
        self.tabs.get(index).map(|tab| tab.title.as_str()).unwrap_or("")
    }

    /// ### Sets the title of a tab at the specified index.
    ///
    /// # Parameters
    /// - `index`: The index of the tab
    /// - `title`: The new title for the tab
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// tab_container.add_tab("Old Title");
    /// tab_container.set_tab_title(0, "New Title");
    /// assert_eq!(tab_container.get_tab_title(0), "New Title");
    /// ```
    #[inline]
    pub fn set_tab_title(&mut self, index: usize, title: &str) {
        if let Some(tab) = self.tabs.get_mut(index) {
            tab.title = title.to_string();
        }
    }

    /// ### Checks if tabs are visible.
    ///
    /// # Returns
    /// True if the tab bar is visible, false otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let tab_container = TabContainer::new("Tabs");
    /// assert!(tab_container.are_tabs_visible());
    /// ```
    #[inline]
    pub fn are_tabs_visible(&self) -> bool {
        self.tabs_visible
    }

    /// ### Sets whether tabs are visible.
    ///
    /// # Parameters
    /// - `visible`: Whether to show the tab bar
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// tab_container.set_tabs_visible(false);
    /// assert!(!tab_container.are_tabs_visible());
    /// ```
    #[inline]
    pub fn set_tabs_visible(&mut self, visible: bool) {
        self.tabs_visible = visible;
    }

    /// ### Gets the tab bar height.
    ///
    /// # Returns
    /// The height of the tab bar in pixels.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let tab_container = TabContainer::new("Tabs");
    /// assert_eq!(tab_container.get_tab_bar_height(), 32.0);
    /// ```
    #[inline]
    pub fn get_tab_bar_height(&self) -> f32 {
        self.tab_bar_height
    }

    /// ### Sets the tab bar height.
    ///
    /// # Parameters
    /// - `height`: The new height for the tab bar
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let mut tab_container = TabContainer::new("Tabs");
    /// tab_container.set_tab_bar_height(40.0);
    /// assert_eq!(tab_container.get_tab_bar_height(), 40.0);
    /// ```
    #[inline]
    pub fn set_tab_bar_height(&mut self, height: f32) {
        self.tab_bar_height = height.max(0.0);
    }

    /// ### Gets the tab alignment.
    ///
    /// # Returns
    /// The current tab alignment setting.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::{TabContainer, TabAlignment};
    /// let tab_container = TabContainer::new("Tabs");
    /// assert_eq!(tab_container.get_tab_alignment(), TabAlignment::Left);
    /// ```
    #[inline]
    pub fn get_tab_alignment(&self) -> TabAlignment {
        self.tab_alignment
    }

    /// ### Sets the tab alignment.
    ///
    /// # Parameters
    /// - `alignment`: The new tab alignment
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::{TabContainer, TabAlignment};
    /// let mut tab_container = TabContainer::new("Tabs");
    /// tab_container.set_tab_alignment(TabAlignment::Center);
    /// assert_eq!(tab_container.get_tab_alignment(), TabAlignment::Center);
    /// ```
    #[inline]
    pub fn set_tab_alignment(&mut self, alignment: TabAlignment) {
        self.tab_alignment = alignment;
    }

    /// ### Gets the tab changed signal.
    ///
    /// # Returns
    /// Reference to the tab changed signal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let tab_container = TabContainer::new("Tabs");
    /// let signal = tab_container.get_tab_changed_signal();
    /// assert_eq!(signal.name(), "tab_changed");
    /// ```
    #[inline]
    pub fn get_tab_changed_signal(&self) -> &Signal {
        &self.tab_changed_signal
    }

    /// ### Gets the tab selected signal.
    ///
    /// # Returns
    /// Reference to the tab selected signal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::TabContainer;
    /// let tab_container = TabContainer::new("Tabs");
    /// let signal = tab_container.get_tab_selected_signal();
    /// assert_eq!(signal.name(), "tab_selected");
    /// ```
    #[inline]
    pub fn get_tab_selected_signal(&self) -> &Signal {
        &self.tab_selected_signal
    }

    /// ### Emits the tab changed signal.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    fn emit_tab_changed(&self, signal_manager: &mut SignalManager) {
        let signal_data = SignalData::new(vec![Variant::from(self.current_tab)]);
        signal_manager.emit_signal(&self.tab_changed_signal, signal_data);
    }

    /// ### Emits the tab selected signal.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    /// - `tab_index`: The index of the selected tab
    #[inline]
    fn emit_tab_selected(&self, signal_manager: &mut SignalManager, tab_index: i32) {
        let signal_data = SignalData::new(vec![Variant::from(tab_index)]);
        signal_manager.emit_signal(&self.tab_selected_signal, signal_data);
    }

    /// ### Handles tab click at the specified position.
    ///
    /// # Parameters
    /// - `position`: The click position relative to the tab container
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Returns
    /// True if a tab was clicked, false otherwise.
    pub fn handle_tab_click(&mut self, position: Vector2, signal_manager: &mut SignalManager) -> bool {
        if !self.tabs_visible || position.y > self.tab_bar_height {
            return false;
        }

        // Calculate which tab was clicked (simplified calculation)
        let tab_width = if self.tabs.is_empty() { 0.0 } else {
            self.base.get_size().x / self.tabs.len() as f32
        };
        let clicked_tab = (position.x / tab_width) as usize;

        if clicked_tab < self.tabs.len() {
            let old_tab = self.current_tab;
            self.current_tab = clicked_tab as i32;

            if old_tab != self.current_tab {
                self.emit_tab_changed(signal_manager);
            }
            self.emit_tab_selected(signal_manager, self.current_tab);
            return true;
        }

        false
    }
}
