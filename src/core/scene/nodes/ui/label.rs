//! Label implementation for displaying text with comprehensive formatting options.
//!
//! This module provides the Label class that extends Control with text rendering
//! capabilities including font management, color styling, alignment options, and
//! word wrapping. It maintains full compatibility with Godot's Label class while
//! providing efficient text rendering for UI development.

use std::fmt;
use crate::core::math::Vector2;
use crate::core::variant::{Color, String as GodotString};
use crate::core::scene::nodes::Control;

/// ### Text alignment options for label content.
///
/// Defines how text is aligned within the label's bounds.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
#[allow(dead_code)] // Complete text alignment options for comprehensive UI API
pub enum TextAlign {
    /// Align text to the left
    Left,
    /// Center text horizontally
    Center,
    /// Align text to the right
    Right,
    /// Justify text (stretch to fill width)
    Justify,
}

/// ### Vertical alignment options for label content.
///
/// Defines how text is aligned vertically within the label's bounds.
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq)]
#[allow(dead_code)] // Complete vertical alignment options for comprehensive UI API
pub enum VerticalAlign {
    /// Align text to the top
    Top,
    /// Center text vertically
    Center,
    /// Align text to the bottom
    Bottom,
}

/// ### Text overflow behavior when content exceeds label bounds.
///
/// Defines how text behaves when it doesn't fit within the label.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TextOverflow {
    /// Clip text that exceeds bounds
    Clip,
    /// Show ellipsis (...) for overflow
    Ellipsis,
    /// Allow text to overflow bounds
    Visible,
}

/// ### Text label for displaying formatted text with comprehensive styling options.
///
/// Label extends Control with comprehensive text rendering capabilities,
/// providing font management, color styling, alignment options, word wrapping,
/// and overflow handling. It maintains full compatibility with Godot's Label
/// class while ensuring efficient text rendering and layout management.
///
/// ## Core Features
///
/// - **Text Rendering**: Display text with full Unicode support
/// - **Font Management**: Custom font support with size and style options
/// - **Color Styling**: Text color, outline, and shadow effects
/// - **Alignment**: Horizontal and vertical text alignment
/// - **Word Wrapping**: Automatic text wrapping with overflow handling
/// - **Rich Text**: Basic markup support for styled text
/// - **Godot Compatibility**: API matching Godot's Label class
///
/// ## Text Properties
///
/// Label provides comprehensive text formatting:
/// - **Text**: The displayed text content
/// - **Font**: Font resource and size settings
/// - **Color**: Text color and transparency
/// - **Alignment**: Horizontal and vertical positioning
/// - **Wrapping**: Word wrap and overflow behavior
/// - **Effects**: Outline, shadow, and other text effects
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::ui::{Label, TextAlign, VerticalAlign};
/// # use verturion::core::variant::{Color, String as GodotString};
/// # use verturion::core::math::Vector2;
/// // Create a label
/// let mut label = Label::new("ScoreLabel");
///
/// // Configure text properties
/// label.set_text(GodotString::from("Score: 1000"));
/// label.set_text_align(TextAlign::Center);
/// label.set_vertical_align(VerticalAlign::Center);
/// label.set_font_color(Color::new(1.0, 1.0, 0.0, 1.0)); // Yellow text
///
/// // Enable word wrapping
/// label.set_autowrap_mode(true);
/// label.set_size(Vector2::new(200.0, 100.0));
///
/// assert_eq!(label.get_text().as_str(), "Score: 1000");
/// assert_eq!(label.get_text_align(), TextAlign::Center);
/// ```
#[derive(Debug, Clone)]
#[allow(dead_code)] // Comprehensive implementation - fields will be used when text rendering is implemented
pub struct Label {
    /// Base Control functionality
    base: Control,
    /// Text content to display
    text: GodotString,
    /// Font resource path or identifier
    font: Option<String>,
    /// Font size in pixels
    font_size: i32,
    /// Text color
    font_color: Color,
    /// Horizontal text alignment
    text_align: TextAlign,
    /// Vertical text alignment
    vertical_align: VerticalAlign,
    /// Whether to enable automatic word wrapping
    autowrap_mode: bool,
    /// Text overflow behavior
    text_overflow: TextOverflow,
    /// Whether to clip text to label bounds
    clip_contents: bool,
    /// Outline color (if enabled)
    outline_color: Color,
    /// Outline size in pixels
    outline_size: i32,
    /// Shadow color (if enabled)
    shadow_color: Color,
    /// Shadow offset
    shadow_offset: Vector2,
    /// Whether to use uppercase text
    uppercase: bool,
}

#[allow(dead_code)] // Comprehensive label implementation awaiting integration
impl Label {
    /// ### Creates a new Label with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this label node
    ///
    /// # Returns
    /// A new Label instance with default text properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::Label;
    /// let label = Label::new("MyLabel");
    /// assert_eq!(label.get_name(), "MyLabel");
    /// assert_eq!(label.get_text().as_str(), "");
    /// assert!(label.is_visible());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Control::new(name),
            text: GodotString::from(""),
            font: None,
            font_size: 16,
            font_color: Color::WHITE,
            text_align: TextAlign::Left,
            vertical_align: VerticalAlign::Top,
            autowrap_mode: false,
            text_overflow: TextOverflow::Clip,
            clip_contents: true,
            outline_color: Color::BLACK,
            outline_size: 0,
            shadow_color: Color::BLACK,
            shadow_offset: Vector2::ZERO,
            uppercase: false,
        }
    }

    /// ### Gets the text content.
    ///
    /// # Returns
    /// The current text content.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::Label;
    /// let label = Label::new("Label");
    /// assert_eq!(label.get_text().as_str(), "");
    /// ```
    #[inline]
    pub fn get_text(&self) -> &GodotString {
        &self.text
    }

    /// ### Sets the text content.
    ///
    /// # Parameters
    /// - `text`: The new text content
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ui::Label;
    /// # use verturion::core::variant::String as GodotString;
    /// let mut label = Label::new("Label");
    /// label.set_text(GodotString::from("Hello World"));
    /// assert_eq!(label.get_text().as_str(), "Hello World");
    /// ```
    #[inline]
    pub fn set_text(&mut self, text: GodotString) {
        self.text = text;
    }

    /// ### Gets the font resource path.
    ///
    /// # Returns
    /// The current font path, or None if using default font.
    #[inline]
    pub fn get_font(&self) -> Option<&String> {
        self.font.as_ref()
    }

    /// ### Sets the font resource path.
    ///
    /// # Parameters
    /// - `font`: The font path, or None to use default font
    #[inline]
    pub fn set_font(&mut self, font: Option<String>) {
        self.font = font;
    }

    /// ### Gets the font size.
    ///
    /// # Returns
    /// The current font size in pixels.
    #[inline]
    pub fn get_font_size(&self) -> i32 {
        self.font_size
    }

    /// ### Sets the font size.
    ///
    /// # Parameters
    /// - `size`: The new font size in pixels
    #[inline]
    pub fn set_font_size(&mut self, size: i32) {
        self.font_size = size.max(1); // Ensure positive size
    }

    /// ### Gets the font color.
    ///
    /// # Returns
    /// The current font color.
    #[inline]
    pub fn get_font_color(&self) -> Color {
        self.font_color
    }

    /// ### Sets the font color.
    ///
    /// # Parameters
    /// - `color`: The new font color
    #[inline]
    pub fn set_font_color(&mut self, color: Color) {
        self.font_color = color;
    }

    /// ### Gets the text alignment.
    ///
    /// # Returns
    /// The current horizontal text alignment.
    #[inline]
    pub fn get_text_align(&self) -> TextAlign {
        self.text_align
    }

    /// ### Sets the text alignment.
    ///
    /// # Parameters
    /// - `align`: The new horizontal text alignment
    #[inline]
    pub fn set_text_align(&mut self, align: TextAlign) {
        self.text_align = align;
    }

    /// ### Gets the vertical alignment.
    ///
    /// # Returns
    /// The current vertical text alignment.
    #[inline]
    pub fn get_vertical_align(&self) -> VerticalAlign {
        self.vertical_align
    }

    /// ### Sets the vertical alignment.
    ///
    /// # Parameters
    /// - `align`: The new vertical text alignment
    #[inline]
    pub fn set_vertical_align(&mut self, align: VerticalAlign) {
        self.vertical_align = align;
    }

    /// ### Checks if autowrap mode is enabled.
    ///
    /// # Returns
    /// True if automatic word wrapping is enabled, false otherwise.
    #[inline]
    pub fn get_autowrap_mode(&self) -> bool {
        self.autowrap_mode
    }

    /// ### Sets the autowrap mode.
    ///
    /// # Parameters
    /// - `enabled`: Whether to enable automatic word wrapping
    #[inline]
    pub fn set_autowrap_mode(&mut self, enabled: bool) {
        self.autowrap_mode = enabled;
    }

    /// ### Gets the text overflow behavior.
    ///
    /// # Returns
    /// The current text overflow mode.
    #[inline]
    pub fn get_text_overflow(&self) -> TextOverflow {
        self.text_overflow
    }

    /// ### Sets the text overflow behavior.
    ///
    /// # Parameters
    /// - `overflow`: The new text overflow mode
    #[inline]
    pub fn set_text_overflow(&mut self, overflow: TextOverflow) {
        self.text_overflow = overflow;
    }

    /// ### Provides access to the base Control functionality.
    ///
    /// # Returns
    /// A reference to the underlying Control.
    #[inline]
    pub fn base(&self) -> &Control {
        &self.base
    }

    /// ### Provides mutable access to the base Control functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Control.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Control {
        &mut self.base
    }

    /// ### Gets the node name from the base Control.
    ///
    /// # Returns
    /// The name of this label node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }

    /// ### Gets the size from the base Control.
    ///
    /// # Returns
    /// The current size of the label.
    #[inline]
    pub fn get_size(&self) -> Vector2 {
        self.base.get_size()
    }

    /// ### Sets the size in the base Control.
    ///
    /// # Parameters
    /// - `size`: The new size for the label
    #[inline]
    pub fn set_size(&mut self, size: Vector2) {
        self.base.set_size(size);
    }

    /// ### Checks if the label is visible.
    ///
    /// # Returns
    /// True if the label is visible, false otherwise.
    #[inline]
    pub fn is_visible(&self) -> bool {
        self.base.is_visible()
    }

    /// ### Sets the visibility of the label.
    ///
    /// # Parameters
    /// - `visible`: Whether the label should be visible
    #[inline]
    pub fn set_visible(&mut self, visible: bool) {
        self.base.set_visible(visible);
    }
}

impl fmt::Display for Label {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Label({}, text: \"{}\", font_size: {}, align: {:?})",
               self.get_name(), self.text.as_str(), self.font_size, self.text_align)
    }
}

impl PartialEq for Label {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Label {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_label_creation() {
        let label = Label::new("TestLabel");
        assert_eq!(label.get_name(), "TestLabel");
        assert_eq!(label.get_text().as_str(), "");
        assert_eq!(label.get_font_size(), 16);
        assert_eq!(label.get_font_color(), Color::WHITE);
        assert_eq!(label.get_text_align(), TextAlign::Left);
        assert_eq!(label.get_vertical_align(), VerticalAlign::Top);
        assert!(!label.get_autowrap_mode());
        assert!(label.is_visible());
    }

    #[test]
    fn test_label_text_management() {
        let mut label = Label::new("Label");

        // Initially empty text
        assert_eq!(label.get_text().as_str(), "");

        // Set text
        label.set_text(GodotString::from("Hello World"));
        assert_eq!(label.get_text().as_str(), "Hello World");

        // Update text
        label.set_text(GodotString::from("Updated Text"));
        assert_eq!(label.get_text().as_str(), "Updated Text");

        // Clear text
        label.set_text(GodotString::from(""));
        assert_eq!(label.get_text().as_str(), "");
    }

    #[test]
    fn test_label_font_management() {
        let mut label = Label::new("Label");

        // Initially no custom font
        assert_eq!(label.get_font(), None);
        assert_eq!(label.get_font_size(), 16);

        // Set custom font
        label.set_font(Some("res://fonts/custom.ttf".to_string()));
        assert_eq!(label.get_font(), Some(&"res://fonts/custom.ttf".to_string()));

        // Set font size
        label.set_font_size(24);
        assert_eq!(label.get_font_size(), 24);

        // Test minimum font size
        label.set_font_size(0);
        assert_eq!(label.get_font_size(), 1); // Should be clamped to 1

        // Clear custom font
        label.set_font(None);
        assert_eq!(label.get_font(), None);
    }

    #[test]
    fn test_label_color_management() {
        let mut label = Label::new("Label");

        // Initially white
        assert_eq!(label.get_font_color(), Color::WHITE);

        // Set custom color
        let blue_color = Color::new(0.0, 0.5, 1.0, 0.8);
        label.set_font_color(blue_color);
        assert_eq!(label.get_font_color(), blue_color);

        // Reset to white
        label.set_font_color(Color::WHITE);
        assert_eq!(label.get_font_color(), Color::WHITE);
    }

    #[test]
    fn test_label_text_alignment() {
        let mut label = Label::new("Label");

        // Initially left aligned
        assert_eq!(label.get_text_align(), TextAlign::Left);

        // Test all horizontal alignments
        label.set_text_align(TextAlign::Center);
        assert_eq!(label.get_text_align(), TextAlign::Center);

        label.set_text_align(TextAlign::Right);
        assert_eq!(label.get_text_align(), TextAlign::Right);

        label.set_text_align(TextAlign::Justify);
        assert_eq!(label.get_text_align(), TextAlign::Justify);

        label.set_text_align(TextAlign::Left);
        assert_eq!(label.get_text_align(), TextAlign::Left);
    }

    #[test]
    fn test_label_vertical_alignment() {
        let mut label = Label::new("Label");

        // Initially top aligned
        assert_eq!(label.get_vertical_align(), VerticalAlign::Top);

        // Test all vertical alignments
        label.set_vertical_align(VerticalAlign::Center);
        assert_eq!(label.get_vertical_align(), VerticalAlign::Center);

        label.set_vertical_align(VerticalAlign::Bottom);
        assert_eq!(label.get_vertical_align(), VerticalAlign::Bottom);

        label.set_vertical_align(VerticalAlign::Top);
        assert_eq!(label.get_vertical_align(), VerticalAlign::Top);
    }

    #[test]
    fn test_label_autowrap_mode() {
        let mut label = Label::new("Label");

        // Initially disabled
        assert!(!label.get_autowrap_mode());

        // Enable autowrap
        label.set_autowrap_mode(true);
        assert!(label.get_autowrap_mode());

        // Disable autowrap
        label.set_autowrap_mode(false);
        assert!(!label.get_autowrap_mode());
    }

    #[test]
    fn test_label_text_overflow() {
        let mut label = Label::new("Label");

        // Initially clip
        assert_eq!(label.get_text_overflow(), TextOverflow::Clip);

        // Test all overflow modes
        label.set_text_overflow(TextOverflow::Ellipsis);
        assert_eq!(label.get_text_overflow(), TextOverflow::Ellipsis);

        label.set_text_overflow(TextOverflow::Visible);
        assert_eq!(label.get_text_overflow(), TextOverflow::Visible);

        label.set_text_overflow(TextOverflow::Clip);
        assert_eq!(label.get_text_overflow(), TextOverflow::Clip);
    }

    #[test]
    fn test_label_base_access() {
        let mut label = Label::new("Label");

        // Test base access
        assert_eq!(label.base().base().get_name(), "Label");

        // Test size management through base
        label.set_size(Vector2::new(200.0, 100.0));
        assert_eq!(label.get_size(), Vector2::new(200.0, 100.0));

        // Test visibility through base
        assert!(label.is_visible());
        label.set_visible(false);
        assert!(!label.is_visible());
        label.set_visible(true);
        assert!(label.is_visible());
    }

    #[test]
    fn test_label_equality() {
        let label1 = Label::new("Label1");
        let label2 = Label::new("Label2");
        let label1_clone = label1.clone();

        // Same label should be equal
        assert_eq!(label1, label1_clone);

        // Different labels should not be equal
        assert_ne!(label1, label2);
    }

    #[test]
    fn test_label_display() {
        let mut label = Label::new("TestLabel");
        label.set_text(GodotString::from("Hello"));
        label.set_font_size(20);
        label.set_text_align(TextAlign::Center);

        let display_str = format!("{}", label);
        assert!(display_str.contains("TestLabel"));
        assert!(display_str.contains("Hello"));
        assert!(display_str.contains("font_size: 20"));
        assert!(display_str.contains("Center"));
    }

    #[test]
    fn test_label_complex_configuration() {
        let mut label = Label::new("ComplexLabel");

        // Configure all properties
        label.set_text(GodotString::from("Complex Text Example"));
        label.set_font(Some("res://fonts/arial.ttf".to_string()));
        label.set_font_size(18);
        label.set_font_color(Color::new(0.2, 0.8, 0.4, 1.0));
        label.set_text_align(TextAlign::Center);
        label.set_vertical_align(VerticalAlign::Center);
        label.set_autowrap_mode(true);
        label.set_text_overflow(TextOverflow::Ellipsis);
        label.set_size(Vector2::new(300.0, 150.0));

        // Verify all properties
        assert_eq!(label.get_text().as_str(), "Complex Text Example");
        assert_eq!(label.get_font(), Some(&"res://fonts/arial.ttf".to_string()));
        assert_eq!(label.get_font_size(), 18);
        assert_eq!(label.get_font_color(), Color::new(0.2, 0.8, 0.4, 1.0));
        assert_eq!(label.get_text_align(), TextAlign::Center);
        assert_eq!(label.get_vertical_align(), VerticalAlign::Center);
        assert!(label.get_autowrap_mode());
        assert_eq!(label.get_text_overflow(), TextOverflow::Ellipsis);
        assert_eq!(label.get_size(), Vector2::new(300.0, 150.0));
    }
}
