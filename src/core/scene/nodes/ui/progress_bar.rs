//! ProgressBar implementation for value display with customizable styling and progress indication.
//!
//! This module provides the ProgressBar class that extends Control with
//! comprehensive progress display functionality including value tracking,
//! fill modes, customizable styling, progress indication, and signal
//! emission. It maintains full compatibility with Godot's ProgressBar
//! class while providing efficient progress rendering and value management.

use std::fmt;
use crate::core::scene::Control;
use crate::core::signal::{Signal, SignalManager, SignalData};
use crate::core::variant::Variant;

/// ### Fill modes for ProgressBar display behavior.
///
/// Defines how the progress bar should fill when displaying values.
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
#[allow(dead_code)] // Complete fill mode options for comprehensive progress bar API
pub enum FillMode {
    /// Fill from left to right
    LeftToRight,
    /// Fill from right to left
    RightToLeft,
    /// Fill from top to bottom
    TopToBottom,
    /// Fill from bottom to top
    BottomToTop,
    /// Fill from center outward
    CenterExpand,
    /// Fill from edges inward
    EdgeContract,
}

/// ### ProgressBar for value display with customizable styling and progress indication.
///
/// ProgressBar extends Control with comprehensive progress display functionality,
/// providing value tracking, fill modes, customizable styling, progress
/// indication, and signal emission for value changes. It maintains full
/// compatibility with Godot's ProgressBar class while ensuring efficient
/// progress rendering and responsive value updates.
///
/// ## Core Features
///
/// - **Value Tracking**: Current, minimum, and maximum value management
/// - **Fill Modes**: Multiple fill directions and patterns
/// - **Progress Indication**: Visual progress representation
/// - **Customizable Styling**: Colors, textures, and appearance options
/// - **Signal Integration**: Value change and progress completion signals
/// - **Step Control**: Discrete value stepping and smooth transitions
/// - **Godot Compatibility**: API matching Godot's ProgressBar
///
/// ## Progress Properties
///
/// ProgressBar provides comprehensive progress control:
/// - **Value Range**: Minimum and maximum value bounds
/// - **Current Value**: Current progress value
/// - **Step Size**: Increment/decrement step amount
/// - **Fill Mode**: Direction and pattern of progress fill
/// - **Show Percentage**: Whether to display percentage text
/// - **Rounded Values**: Whether to round values to integers
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::ProgressBar;
/// # use verturion::core::signal::SignalManager;
/// // Create a progress bar
/// let mut progress_bar = ProgressBar::new("HealthBar");
/// let mut signal_manager = SignalManager::new();
///
/// // Configure progress range
/// progress_bar.set_min_value(0.0, &mut signal_manager);
/// progress_bar.set_max_value(100.0, &mut signal_manager);
/// progress_bar.set_step(1.0);
///
/// // Set initial value
/// progress_bar.set_value(75.0, &mut signal_manager);
///
/// // Register signals
/// signal_manager.register_signal(progress_bar.get_value_changed_signal().clone());
///
/// assert_eq!(progress_bar.get_value(), 75.0);
/// assert_eq!(progress_bar.get_percentage(), 0.75);
/// assert!(progress_bar.is_show_percentage());
/// ```
#[derive(Debug, Clone)]
pub struct ProgressBar {
    /// Base Control functionality
    base: Control,
    /// Current progress value
    value: f64,
    /// Minimum value
    min_value: f64,
    /// Maximum value
    max_value: f64,
    /// Step size for value changes
    step: f64,
    /// Fill mode for progress display
    fill_mode: FillMode,
    /// Whether to show percentage text
    show_percentage: bool,
    /// Whether to round values to integers
    rounded: bool,
    /// Whether the progress bar is editable
    editable: bool,
    /// Whether to allow values greater than max
    allow_greater: bool,
    /// Whether to allow values less than min
    allow_lesser: bool,
    /// Signal emitted when value changes
    value_changed_signal: Signal,
    /// Signal emitted when progress reaches maximum
    progress_complete_signal: Signal,
    /// Signal emitted when progress reaches minimum
    progress_empty_signal: Signal,
}

#[allow(dead_code)] // Comprehensive progress bar implementation awaiting integration
impl ProgressBar {
    /// ### Creates a new ProgressBar with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this progress bar control
    ///
    /// # Returns
    /// A new ProgressBar instance with default progress properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ProgressBar;
    /// let progress_bar = ProgressBar::new("LoadingBar");
    /// assert_eq!(progress_bar.get_name(), "LoadingBar");
    /// assert_eq!(progress_bar.get_value(), 0.0);
    /// assert_eq!(progress_bar.get_min_value(), 0.0);
    /// assert_eq!(progress_bar.get_max_value(), 100.0);
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let control = Control::new(name);
        let node_id = control.base().get_id();
        Self {
            base: control,
            value: 0.0,
            min_value: 0.0,
            max_value: 100.0,
            step: 1.0,
            fill_mode: FillMode::LeftToRight,
            show_percentage: true,
            rounded: false,
            editable: false,
            allow_greater: false,
            allow_lesser: false,
            value_changed_signal: Signal::new("value_changed", node_id),
            progress_complete_signal: Signal::new("progress_complete", node_id),
            progress_empty_signal: Signal::new("progress_empty", node_id),
        }
    }

    /// ### Gets the current progress value.
    ///
    /// # Returns
    /// The current progress value.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ProgressBar;
    /// # use verturion::core::signal::SignalManager;
    /// let mut progress_bar = ProgressBar::new("Bar");
    /// let mut manager = SignalManager::new();
    /// progress_bar.set_value(50.0, &mut manager);
    /// assert_eq!(progress_bar.get_value(), 50.0);
    /// ```
    #[inline]
    pub fn get_value(&self) -> f64 {
        self.value
    }

    /// ### Sets the progress value.
    ///
    /// # Parameters
    /// - `value`: The new progress value
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ProgressBar;
    /// # use verturion::core::signal::SignalManager;
    /// let mut progress_bar = ProgressBar::new("Bar");
    /// let mut manager = SignalManager::new();
    /// progress_bar.set_value(75.0, &mut manager);
    /// assert_eq!(progress_bar.get_value(), 75.0);
    /// ```
    #[inline]
    pub fn set_value(&mut self, value: f64, signal_manager: &mut SignalManager) {
        let old_value = self.value;

        // Apply value constraints
        let new_value = if self.allow_greater && self.allow_lesser {
            value
        } else if self.allow_greater {
            value.max(self.min_value)
        } else if self.allow_lesser {
            value.min(self.max_value)
        } else {
            value.max(self.min_value).min(self.max_value)
        };

        // Apply rounding if enabled
        let final_value = if self.rounded {
            new_value.round()
        } else {
            new_value
        };

        if (final_value - old_value).abs() > f64::EPSILON {
            self.value = final_value;

            // Emit value changed signal
            let mut data = SignalData::empty();
            data.add_arg(Variant::Float(self.value));
            signal_manager.emit(self.value_changed_signal.id(), data);

            // Check for completion signals
            if (self.value - self.max_value).abs() < f64::EPSILON && (old_value - self.max_value).abs() >= f64::EPSILON {
                let data = SignalData::empty();
                signal_manager.emit(self.progress_complete_signal.id(), data);
            }

            if (self.value - self.min_value).abs() < f64::EPSILON && (old_value - self.min_value).abs() >= f64::EPSILON {
                let data = SignalData::empty();
                signal_manager.emit(self.progress_empty_signal.id(), data);
            }
        }
    }

    /// ### Gets the minimum value.
    ///
    /// # Returns
    /// The minimum progress value.
    #[inline]
    pub fn get_min_value(&self) -> f64 {
        self.min_value
    }

    /// ### Sets the minimum value.
    ///
    /// # Parameters
    /// - `min_value`: The new minimum value
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn set_min_value(&mut self, min_value: f64, signal_manager: &mut SignalManager) {
        self.min_value = min_value;

        // Ensure max_value is not less than min_value
        if self.max_value < self.min_value {
            self.max_value = self.min_value;
        }

        // Clamp current value if necessary
        if self.value < self.min_value && !self.allow_lesser {
            self.set_value(self.min_value, signal_manager);
        }
    }

    /// ### Gets the maximum value.
    ///
    /// # Returns
    /// The maximum progress value.
    #[inline]
    pub fn get_max_value(&self) -> f64 {
        self.max_value
    }

    /// ### Sets the maximum value.
    ///
    /// # Parameters
    /// - `max_value`: The new maximum value
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn set_max_value(&mut self, max_value: f64, signal_manager: &mut SignalManager) {
        self.max_value = max_value;

        // Ensure min_value is not greater than max_value
        if self.min_value > self.max_value {
            self.min_value = self.max_value;
        }

        // Clamp current value if necessary
        if self.value > self.max_value && !self.allow_greater {
            self.set_value(self.max_value, signal_manager);
        }
    }

    /// ### Gets the step size.
    ///
    /// # Returns
    /// The step size for value changes.
    #[inline]
    pub fn get_step(&self) -> f64 {
        self.step
    }

    /// ### Sets the step size.
    ///
    /// # Parameters
    /// - `step`: The new step size
    #[inline]
    pub fn set_step(&mut self, step: f64) {
        self.step = step.max(0.0);
    }

    /// ### Gets the fill mode.
    ///
    /// # Returns
    /// The current fill mode.
    #[inline]
    pub fn get_fill_mode(&self) -> FillMode {
        self.fill_mode
    }

    /// ### Sets the fill mode.
    ///
    /// # Parameters
    /// - `mode`: The new fill mode
    #[inline]
    pub fn set_fill_mode(&mut self, mode: FillMode) {
        self.fill_mode = mode;
    }

    /// ### Checks if percentage text is shown.
    ///
    /// # Returns
    /// True if percentage text is displayed, false otherwise.
    #[inline]
    pub fn is_show_percentage(&self) -> bool {
        self.show_percentage
    }

    /// ### Sets whether to show percentage text.
    ///
    /// # Parameters
    /// - `show`: Whether to display percentage text
    #[inline]
    pub fn set_show_percentage(&mut self, show: bool) {
        self.show_percentage = show;
    }

    /// ### Checks if values are rounded.
    ///
    /// # Returns
    /// True if values are rounded to integers, false otherwise.
    #[inline]
    pub fn is_rounded(&self) -> bool {
        self.rounded
    }

    /// ### Sets whether to round values.
    ///
    /// # Parameters
    /// - `rounded`: Whether to round values to integers
    #[inline]
    pub fn set_rounded(&mut self, rounded: bool) {
        self.rounded = rounded;
    }

    /// ### Checks if the progress bar is editable.
    ///
    /// # Returns
    /// True if the progress bar can be edited by user input.
    #[inline]
    pub fn is_editable(&self) -> bool {
        self.editable
    }

    /// ### Sets whether the progress bar is editable.
    ///
    /// # Parameters
    /// - `editable`: Whether the progress bar can be edited
    #[inline]
    pub fn set_editable(&mut self, editable: bool) {
        self.editable = editable;
    }

    /// ### Checks if values greater than max are allowed.
    ///
    /// # Returns
    /// True if values can exceed the maximum.
    #[inline]
    pub fn is_allow_greater(&self) -> bool {
        self.allow_greater
    }

    /// ### Sets whether to allow values greater than max.
    ///
    /// # Parameters
    /// - `allow`: Whether to allow values greater than maximum
    #[inline]
    pub fn set_allow_greater(&mut self, allow: bool) {
        self.allow_greater = allow;
    }

    /// ### Checks if values less than min are allowed.
    ///
    /// # Returns
    /// True if values can be below the minimum.
    #[inline]
    pub fn is_allow_lesser(&self) -> bool {
        self.allow_lesser
    }

    /// ### Sets whether to allow values less than min.
    ///
    /// # Parameters
    /// - `allow`: Whether to allow values less than minimum
    #[inline]
    pub fn set_allow_lesser(&mut self, allow: bool) {
        self.allow_lesser = allow;
    }

    /// ### Gets the progress percentage (0.0 to 1.0).
    ///
    /// # Returns
    /// The progress as a percentage between 0.0 and 1.0.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ProgressBar;
    /// # use verturion::core::signal::SignalManager;
    /// let mut progress_bar = ProgressBar::new("Bar");
    /// let mut manager = SignalManager::new();
    /// progress_bar.set_value(25.0, &mut manager);
    /// assert_eq!(progress_bar.get_percentage(), 0.25);
    /// ```
    #[inline]
    pub fn get_percentage(&self) -> f64 {
        if (self.max_value - self.min_value).abs() < f64::EPSILON {
            0.0
        } else {
            ((self.value - self.min_value) / (self.max_value - self.min_value)).max(0.0).min(1.0)
        }
    }

    /// ### Sets the progress by percentage (0.0 to 1.0).
    ///
    /// # Parameters
    /// - `percentage`: The progress percentage (0.0 to 1.0)
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn set_percentage(&mut self, percentage: f64, signal_manager: &mut SignalManager) {
        let clamped_percentage = percentage.max(0.0).min(1.0);
        let new_value = self.min_value + (self.max_value - self.min_value) * clamped_percentage;
        self.set_value(new_value, signal_manager);
    }

    /// ### Increments the value by the step amount.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ProgressBar;
    /// # use verturion::core::signal::SignalManager;
    /// let mut progress_bar = ProgressBar::new("Bar");
    /// let mut manager = SignalManager::new();
    /// progress_bar.set_step(5.0);
    /// progress_bar.increment(&mut manager);
    /// assert_eq!(progress_bar.get_value(), 5.0);
    /// ```
    #[inline]
    pub fn increment(&mut self, signal_manager: &mut SignalManager) {
        self.set_value(self.value + self.step, signal_manager);
    }

    /// ### Decrements the value by the step amount.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::ProgressBar;
    /// # use verturion::core::signal::SignalManager;
    /// let mut progress_bar = ProgressBar::new("Bar");
    /// let mut manager = SignalManager::new();
    /// progress_bar.set_value(10.0, &mut manager);
    /// progress_bar.set_step(3.0);
    /// progress_bar.decrement(&mut manager);
    /// assert_eq!(progress_bar.get_value(), 7.0);
    /// ```
    #[inline]
    pub fn decrement(&mut self, signal_manager: &mut SignalManager) {
        self.set_value(self.value - self.step, signal_manager);
    }

    /// ### Checks if the progress is at minimum value.
    ///
    /// # Returns
    /// True if the current value equals the minimum value.
    #[inline]
    pub fn is_at_minimum(&self) -> bool {
        (self.value - self.min_value).abs() < f64::EPSILON
    }

    /// ### Checks if the progress is at maximum value.
    ///
    /// # Returns
    /// True if the current value equals the maximum value.
    #[inline]
    pub fn is_at_maximum(&self) -> bool {
        (self.value - self.max_value).abs() < f64::EPSILON
    }

    /// ### Gets the remaining progress to maximum.
    ///
    /// # Returns
    /// The amount remaining to reach maximum value.
    #[inline]
    pub fn get_remaining(&self) -> f64 {
        (self.max_value - self.value).max(0.0)
    }

    /// ### Gets the progress from minimum.
    ///
    /// # Returns
    /// The amount of progress from minimum value.
    #[inline]
    pub fn get_progress(&self) -> f64 {
        (self.value - self.min_value).max(0.0)
    }

    /// ### Gets the value changed signal.
    ///
    /// # Returns
    /// A reference to the value changed signal.
    #[inline]
    pub fn get_value_changed_signal(&self) -> &Signal {
        &self.value_changed_signal
    }

    /// ### Gets the progress complete signal.
    ///
    /// # Returns
    /// A reference to the progress complete signal.
    #[inline]
    pub fn get_progress_complete_signal(&self) -> &Signal {
        &self.progress_complete_signal
    }

    /// ### Gets the progress empty signal.
    ///
    /// # Returns
    /// A reference to the progress empty signal.
    #[inline]
    pub fn get_progress_empty_signal(&self) -> &Signal {
        &self.progress_empty_signal
    }

    /// ### Gets the formatted percentage text.
    ///
    /// # Returns
    /// A formatted string showing the percentage.
    #[inline]
    pub fn get_percentage_text(&self) -> String {
        if self.show_percentage {
            format!("{:.1}%", self.get_percentage() * 100.0)
        } else {
            String::new()
        }
    }

    /// ### Gets the formatted value text.
    ///
    /// # Returns
    /// A formatted string showing the current value.
    #[inline]
    pub fn get_value_text(&self) -> String {
        if self.rounded {
            format!("{:.0}", self.value)
        } else {
            format!("{:.1}", self.value)
        }
    }

    /// ### Gets the formatted range text.
    ///
    /// # Returns
    /// A formatted string showing value/max format.
    #[inline]
    pub fn get_range_text(&self) -> String {
        if self.rounded {
            format!("{:.0}/{:.0}", self.value, self.max_value)
        } else {
            format!("{:.1}/{:.1}", self.value, self.max_value)
        }
    }

    /// ### Animates the progress bar to a target value.
    ///
    /// This is a simplified animation that immediately sets the value.
    /// In a real implementation, this would use the AnimationPlayer.
    ///
    /// # Parameters
    /// - `target_value`: The target value to animate to
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn animate_to_value(&mut self, target_value: f64, signal_manager: &mut SignalManager) {
        // Simplified implementation - in reality would use AnimationPlayer
        self.set_value(target_value, signal_manager);
    }

    /// ### Resets the progress bar to minimum value.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn reset(&mut self, signal_manager: &mut SignalManager) {
        self.set_value(self.min_value, signal_manager);
    }

    /// ### Fills the progress bar to maximum value.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for signal emission
    #[inline]
    pub fn fill(&mut self, signal_manager: &mut SignalManager) {
        self.set_value(self.max_value, signal_manager);
    }

    /// ### Provides access to the base Control functionality.
    ///
    /// # Returns
    /// A reference to the underlying Control.
    #[inline]
    pub fn base(&self) -> &Control {
        &self.base
    }

    /// ### Provides mutable access to the base Control functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Control.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Control {
        &mut self.base
    }

    /// ### Gets the node name from the base Control.
    ///
    /// # Returns
    /// The name of this progress bar control.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }
}

impl fmt::Display for ProgressBar {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "ProgressBar({}, value: {:.1}/{:.1} ({:.1}%), mode: {:?})",
               self.get_name(),
               self.value,
               self.max_value,
               self.get_percentage() * 100.0,
               self.fill_mode)
    }
}

impl PartialEq for ProgressBar {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for ProgressBar {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::signal::SignalManager;

    #[test]
    fn test_progress_bar_creation() {
        let progress_bar = ProgressBar::new("TestProgress");
        assert_eq!(progress_bar.get_name(), "TestProgress");
        assert_eq!(progress_bar.get_value(), 0.0);
        assert_eq!(progress_bar.get_min_value(), 0.0);
        assert_eq!(progress_bar.get_max_value(), 100.0);
        assert_eq!(progress_bar.get_step(), 1.0);
        assert_eq!(progress_bar.get_fill_mode(), FillMode::LeftToRight);
        assert!(progress_bar.is_show_percentage());
        assert!(!progress_bar.is_rounded());
        assert!(!progress_bar.is_editable());
        assert!(!progress_bar.is_allow_greater());
        assert!(!progress_bar.is_allow_lesser());
        assert_eq!(progress_bar.get_percentage(), 0.0);
        assert!(progress_bar.is_at_minimum());
        assert!(!progress_bar.is_at_maximum());
    }

    #[test]
    fn test_progress_bar_value_operations() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        // Register signal
        signal_manager.register_signal(progress_bar.get_value_changed_signal().clone());

        // Test setting value
        progress_bar.set_value(50.0, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 50.0);
        assert_eq!(progress_bar.get_percentage(), 0.5);
        assert!(!progress_bar.is_at_minimum());
        assert!(!progress_bar.is_at_maximum());

        // Test value clamping
        progress_bar.set_value(150.0, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 100.0);
        assert!(progress_bar.is_at_maximum());

        progress_bar.set_value(-50.0, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 0.0);
        assert!(progress_bar.is_at_minimum());
    }

    #[test]
    fn test_progress_bar_range_operations() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        // Test setting min value
        progress_bar.set_min_value(10.0, &mut signal_manager);
        assert_eq!(progress_bar.get_min_value(), 10.0);
        assert_eq!(progress_bar.get_value(), 10.0); // Should clamp to new minimum

        // Test setting max value
        progress_bar.set_max_value(50.0, &mut signal_manager);
        assert_eq!(progress_bar.get_max_value(), 50.0);

        // Test value in new range
        progress_bar.set_value(30.0, &mut signal_manager);
        assert_eq!(progress_bar.get_percentage(), 0.5); // (30-10)/(50-10) = 0.5

        // Test invalid range (min > max)
        progress_bar.set_min_value(60.0, &mut signal_manager);
        assert_eq!(progress_bar.get_max_value(), 60.0); // Max should adjust to min
    }

    #[test]
    fn test_progress_bar_percentage_operations() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        // Test percentage setting
        progress_bar.set_percentage(0.75, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 75.0);
        assert_eq!(progress_bar.get_percentage(), 0.75);

        // Test percentage clamping
        progress_bar.set_percentage(1.5, &mut signal_manager);
        assert_eq!(progress_bar.get_percentage(), 1.0);

        progress_bar.set_percentage(-0.5, &mut signal_manager);
        assert_eq!(progress_bar.get_percentage(), 0.0);

        // Test with custom range
        progress_bar.set_min_value(20.0, &mut signal_manager);
        progress_bar.set_max_value(80.0, &mut signal_manager);
        progress_bar.set_percentage(0.5, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 50.0); // 20 + (80-20) * 0.5
    }

    #[test]
    fn test_progress_bar_step_operations() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        progress_bar.set_step(5.0);
        progress_bar.set_value(20.0, &mut signal_manager);

        // Test increment
        progress_bar.increment(&mut signal_manager);
        assert_eq!(progress_bar.get_value(), 25.0);

        // Test decrement
        progress_bar.decrement(&mut signal_manager);
        assert_eq!(progress_bar.get_value(), 20.0);

        // Test increment with clamping
        progress_bar.set_value(98.0, &mut signal_manager);
        progress_bar.increment(&mut signal_manager);
        assert_eq!(progress_bar.get_value(), 100.0); // Clamped to max

        // Test decrement with clamping
        progress_bar.set_value(2.0, &mut signal_manager);
        progress_bar.decrement(&mut signal_manager);
        assert_eq!(progress_bar.get_value(), 0.0); // Clamped to min
    }

    #[test]
    fn test_progress_bar_properties() {
        let mut progress_bar = ProgressBar::new("Progress");

        // Test step
        progress_bar.set_step(2.5);
        assert_eq!(progress_bar.get_step(), 2.5);

        // Test negative step clamping
        progress_bar.set_step(-1.0);
        assert_eq!(progress_bar.get_step(), 0.0);

        // Test fill mode
        progress_bar.set_fill_mode(FillMode::RightToLeft);
        assert_eq!(progress_bar.get_fill_mode(), FillMode::RightToLeft);

        // Test show percentage
        progress_bar.set_show_percentage(false);
        assert!(!progress_bar.is_show_percentage());

        // Test rounded
        progress_bar.set_rounded(true);
        assert!(progress_bar.is_rounded());

        // Test editable
        progress_bar.set_editable(true);
        assert!(progress_bar.is_editable());

        // Test allow greater
        progress_bar.set_allow_greater(true);
        assert!(progress_bar.is_allow_greater());

        // Test allow lesser
        progress_bar.set_allow_lesser(true);
        assert!(progress_bar.is_allow_lesser());
    }

    #[test]
    fn test_progress_bar_allow_greater_lesser() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        // Test allow greater
        progress_bar.set_allow_greater(true);
        progress_bar.set_value(150.0, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 150.0); // Should not clamp

        // Test allow lesser
        progress_bar.set_allow_lesser(true);
        progress_bar.set_value(-50.0, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), -50.0); // Should not clamp

        // Test both allowed
        progress_bar.set_allow_greater(true);
        progress_bar.set_allow_lesser(true);
        progress_bar.set_value(200.0, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 200.0);
        progress_bar.set_value(-100.0, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), -100.0);
    }

    #[test]
    fn test_progress_bar_rounded_values() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        progress_bar.set_rounded(true);

        // Test rounding
        progress_bar.set_value(45.7, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 46.0);

        progress_bar.set_value(45.3, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 45.0);
    }

    #[test]
    fn test_progress_bar_utility_methods() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        progress_bar.set_value(30.0, &mut signal_manager);

        // Test utility calculations
        assert_eq!(progress_bar.get_remaining(), 70.0);
        assert_eq!(progress_bar.get_progress(), 30.0);

        // Test reset and fill
        progress_bar.reset(&mut signal_manager);
        assert_eq!(progress_bar.get_value(), 0.0);
        assert!(progress_bar.is_at_minimum());

        progress_bar.fill(&mut signal_manager);
        assert_eq!(progress_bar.get_value(), 100.0);
        assert!(progress_bar.is_at_maximum());
    }

    #[test]
    fn test_progress_bar_text_formatting() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        progress_bar.set_value(75.5, &mut signal_manager);

        // Test percentage text
        assert_eq!(progress_bar.get_percentage_text(), "75.5%");

        progress_bar.set_show_percentage(false);
        assert_eq!(progress_bar.get_percentage_text(), "");

        // Test value text
        assert_eq!(progress_bar.get_value_text(), "75.5");

        progress_bar.set_rounded(true);
        progress_bar.set_value(75.5, &mut signal_manager);
        assert_eq!(progress_bar.get_value_text(), "76");

        // Test range text
        assert_eq!(progress_bar.get_range_text(), "76/100");

        progress_bar.set_rounded(false);
        progress_bar.set_value(75.5, &mut signal_manager);
        assert_eq!(progress_bar.get_range_text(), "75.5/100.0");
    }

    #[test]
    fn test_progress_bar_signals() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        // Register signals
        signal_manager.register_signal(progress_bar.get_value_changed_signal().clone());
        signal_manager.register_signal(progress_bar.get_progress_complete_signal().clone());
        signal_manager.register_signal(progress_bar.get_progress_empty_signal().clone());

        // Test value changed signal (implicitly tested by setting values)
        progress_bar.set_value(50.0, &mut signal_manager);

        // Test progress complete signal
        progress_bar.set_value(100.0, &mut signal_manager);

        // Test progress empty signal
        progress_bar.set_value(0.0, &mut signal_manager);

        // Signals should be emitted (tested by signal manager)
    }

    #[test]
    fn test_progress_bar_animation() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        // Test animate to value (simplified implementation)
        progress_bar.animate_to_value(80.0, &mut signal_manager);
        assert_eq!(progress_bar.get_value(), 80.0);
    }

    #[test]
    fn test_progress_bar_edge_cases() {
        let mut progress_bar = ProgressBar::new("Progress");
        let mut signal_manager = SignalManager::new();

        // Test zero range
        progress_bar.set_min_value(50.0, &mut signal_manager);
        progress_bar.set_max_value(50.0, &mut signal_manager);
        assert_eq!(progress_bar.get_percentage(), 0.0); // Should handle division by zero

        // Test very small differences
        progress_bar.set_min_value(0.0, &mut signal_manager);
        progress_bar.set_max_value(100.0, &mut signal_manager);
        progress_bar.set_value(50.0000001, &mut signal_manager);
        progress_bar.set_value(50.0, &mut signal_manager);
        // Should handle floating point precision
    }

    #[test]
    fn test_progress_bar_base_access() {
        let mut progress_bar = ProgressBar::new("BaseTest");

        // Test base access
        assert_eq!(progress_bar.base().base().get_name(), "BaseTest");

        // Test mutable base access
        progress_bar.base_mut().base_mut().set_name("NewName");
        assert_eq!(progress_bar.get_name(), "NewName");
    }

    #[test]
    fn test_progress_bar_equality() {
        let progress_bar1 = ProgressBar::new("Progress1");
        let progress_bar2 = ProgressBar::new("Progress2");
        let progress_bar1_clone = progress_bar1.clone();

        // Same progress bar should be equal
        assert_eq!(progress_bar1, progress_bar1_clone);

        // Different progress bars should not be equal
        assert_ne!(progress_bar1, progress_bar2);
    }

    #[test]
    fn test_progress_bar_display() {
        let mut progress_bar = ProgressBar::new("DisplayTest");
        let mut signal_manager = SignalManager::new();

        progress_bar.set_value(75.0, &mut signal_manager);
        progress_bar.set_fill_mode(FillMode::TopToBottom);

        let display_str = format!("{}", progress_bar);
        assert!(display_str.contains("DisplayTest"));
        assert!(display_str.contains("75.0/100.0"));
        assert!(display_str.contains("75.0%"));
        assert!(display_str.contains("TopToBottom"));
    }

    #[test]
    fn test_fill_modes() {
        // Test all fill modes for completeness
        assert_eq!(FillMode::LeftToRight, FillMode::LeftToRight);
        assert_ne!(FillMode::LeftToRight, FillMode::RightToLeft);
        assert_ne!(FillMode::TopToBottom, FillMode::BottomToTop);
        assert_ne!(FillMode::CenterExpand, FillMode::EdgeContract);
    }

    #[test]
    fn test_complex_progress_bar_scenario() {
        let mut health_bar = ProgressBar::new("HealthBar");
        let mut signal_manager = SignalManager::new();

        // Register all signals
        signal_manager.register_signal(health_bar.get_value_changed_signal().clone());
        signal_manager.register_signal(health_bar.get_progress_complete_signal().clone());
        signal_manager.register_signal(health_bar.get_progress_empty_signal().clone());

        // Configure health bar for game scenario
        health_bar.set_min_value(0.0, &mut signal_manager);
        health_bar.set_max_value(100.0, &mut signal_manager);
        health_bar.set_step(1.0);
        health_bar.set_fill_mode(FillMode::LeftToRight);
        health_bar.set_show_percentage(true);
        health_bar.set_rounded(true);

        // Start with full health
        health_bar.fill(&mut signal_manager);
        assert_eq!(health_bar.get_value(), 100.0);
        assert_eq!(health_bar.get_percentage_text(), "100.0%");
        assert_eq!(health_bar.get_range_text(), "100/100");

        // Simulate damage over time
        let damage_amounts = vec![15.0, 25.0, 10.0, 30.0, 20.0];

        for damage in damage_amounts {
            let new_health = health_bar.get_value() - damage;
            health_bar.set_value(new_health, &mut signal_manager);

            println!("Health: {} ({})", health_bar.get_range_text(), health_bar.get_percentage_text());

            // Check critical health
            if health_bar.get_percentage() <= 0.2 {
                println!("Critical health!");
            }

            // Check if dead
            if health_bar.is_at_minimum() {
                println!("Player defeated!");
                break;
            }
        }

        // Simulate healing
        if !health_bar.is_at_minimum() {
            health_bar.set_value(health_bar.get_value() + 50.0, &mut signal_manager);
            println!("Healed! Health: {}", health_bar.get_range_text());
        }

        // Test incremental changes
        for _ in 0..5 {
            health_bar.increment(&mut signal_manager);
        }

        // Verify final state
        assert!(health_bar.get_value() >= 0.0);
        assert!(health_bar.get_value() <= 100.0);
        assert!(health_bar.get_percentage() >= 0.0);
        assert!(health_bar.get_percentage() <= 1.0);
        assert_eq!(health_bar.get_remaining() + health_bar.get_progress(), 100.0);
    }
}
