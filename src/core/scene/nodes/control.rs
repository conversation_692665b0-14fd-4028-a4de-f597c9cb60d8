//! Control implementation for UI base class with anchors and margins.
//!
//! This module provides the Control class that extends the base Node with UI-specific
//! functionality including anchors, margins, and size handling for user interface
//! elements. It maintains compatibility with Godot's Control class.

use std::fmt;
use crate::core::math::{Vector2, Rect2};
use crate::core::scene::Node;

/// ### Anchor modes for Control positioning.
///
/// Defines how a Control node is anchored relative to its parent.
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum AnchorMode {
    /// Anchored to top-left corner
    TopLeft,
    /// Anchored to top-right corner
    TopRight,
    /// Anchored to bottom-left corner
    BottomLeft,
    /// Anchored to bottom-right corner
    BottomRight,
    /// Anchored to center
    Center,
    /// Fill the entire parent area
    Fill,
}

/// ### UI base class with anchors, margins, and size handling.
///
/// Control extends the base Node class with UI-specific functionality,
/// providing anchor-based positioning, margin handling, and size management
/// for user interface elements. It maintains compatibility with <PERSON><PERSON>'s
/// Control class while ensuring efficient UI layout calculations.
///
/// ## Core Features
///
/// - **Anchor System**: Flexible positioning relative to parent
/// - **Margin Handling**: Precise spacing control
/// - **Size Management**: Automatic and manual size handling
/// - **Godot Compatibility**: API matching Godot's Control class
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::control::{Control, AnchorMode};
/// # use verturion::core::math::Vector2;
/// // Create a UI control
/// let mut button = Control::new("Button");
///
/// // Set UI properties
/// button.set_anchor_mode(AnchorMode::Center);
/// button.set_size(Vector2::new(100.0, 50.0));
///
/// assert_eq!(button.get_size(), Vector2::new(100.0, 50.0));
/// ```
#[derive(Debug, Clone)]
#[allow(dead_code)] // Comprehensive UI control implementation awaiting integration
pub struct Control {
    /// Base node functionality
    base: Node,
    /// Position relative to anchor
    position: Vector2,
    /// Size of the control
    size: Vector2,
    /// Anchor mode for positioning
    anchor_mode: AnchorMode,
    /// Margins (left, top, right, bottom)
    margins: [f32; 4],
    /// Whether the control is visible
    visible: bool,
}

impl Control {
    /// ### Creates a new Control with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this control
    ///
    /// # Returns
    /// A new Control instance with default UI properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::control::Control;
    /// let control = Control::new("MyControl");
    /// assert_eq!(control.get_name(), "MyControl");
    /// assert!(control.is_visible());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Node::new(name),
            position: Vector2::ZERO,
            size: Vector2::new(100.0, 100.0),
            anchor_mode: AnchorMode::TopLeft,
            margins: [0.0, 0.0, 0.0, 0.0], // left, top, right, bottom
            visible: true,
        }
    }

    /// ### Gets the position of this control.
    ///
    /// # Returns
    /// The current position as a Vector2.
    #[inline]
    pub fn get_position(&self) -> Vector2 {
        self.position
    }

    /// ### Sets the position of this control.
    ///
    /// # Parameters
    /// - `position`: The new position as a Vector2
    #[inline]
    pub fn set_position(&mut self, position: Vector2) {
        self.position = position;
    }

    /// ### Gets the size of this control.
    ///
    /// # Returns
    /// The current size as a Vector2.
    #[inline]
    pub fn get_size(&self) -> Vector2 {
        self.size
    }

    /// ### Sets the size of this control.
    ///
    /// # Parameters
    /// - `size`: The new size as a Vector2
    #[inline]
    pub fn set_size(&mut self, size: Vector2) {
        self.size = size;
    }

    /// ### Gets the anchor mode.
    ///
    /// # Returns
    /// The current anchor mode.
    #[inline]
    pub fn get_anchor_mode(&self) -> AnchorMode {
        self.anchor_mode
    }

    /// ### Sets the anchor mode.
    ///
    /// # Parameters
    /// - `mode`: The new anchor mode
    #[inline]
    pub fn set_anchor_mode(&mut self, mode: AnchorMode) {
        self.anchor_mode = mode;
    }

    /// ### Gets the margins.
    ///
    /// # Returns
    /// Array of margins [left, top, right, bottom].
    #[inline]
    pub fn get_margins(&self) -> [f32; 4] {
        self.margins
    }

    /// ### Sets the margins.
    ///
    /// # Parameters
    /// - `margins`: Array of margins [left, top, right, bottom]
    #[inline]
    pub fn set_margins(&mut self, margins: [f32; 4]) {
        self.margins = margins;
    }

    /// ### Checks if the control is visible.
    ///
    /// # Returns
    /// True if the control is visible, false otherwise.
    #[inline]
    pub fn is_visible(&self) -> bool {
        self.visible
    }

    /// ### Sets the visibility of the control.
    ///
    /// # Parameters
    /// - `visible`: Whether the control should be visible
    #[inline]
    pub fn set_visible(&mut self, visible: bool) {
        self.visible = visible;
    }

    /// ### Gets the bounding rectangle of this control.
    ///
    /// # Returns
    /// A Rect2 representing the control's bounds.
    #[inline]
    pub fn get_rect(&self) -> Rect2 {
        Rect2::new(self.position.x, self.position.y, self.size.x, self.size.y)
    }

    /// ### Provides access to the base Node functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node.
    #[inline]
    pub fn base(&self) -> &Node {
        &self.base
    }

    /// ### Provides mutable access to the base Node functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node {
        &mut self.base
    }

    /// ### Gets the node name from the base Node.
    ///
    /// # Returns
    /// The name of this node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.get_name()
    }
}

impl fmt::Display for Control {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "Control({}, pos: {}, size: {}, visible: {})", 
               self.base.get_name(), self.position, self.size, self.visible)
    }
}

impl PartialEq for Control {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for Control {}
