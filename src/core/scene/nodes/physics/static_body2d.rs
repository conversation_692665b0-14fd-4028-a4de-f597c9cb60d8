//! StaticBody2D implementation for immovable physics bodies with collision detection.
//!
//! This module provides the StaticBody2D class that extends Node2D with
//! comprehensive physics collision functionality including collision detection,
//! physics material properties, collision signals, and static body behavior.
//! It maintains full compatibility with Godot's StaticBody2D class while
//! providing efficient collision handling and physics integration.

use std::fmt;
use std::collections::HashSet;
use crate::core::scene::nodes::Node2D;
use crate::core::signal::{Signal, SignalManager, SignalData};
use crate::core::variant::Variant;
use crate::core::math::Vector2;

/// ### Physics material properties for StaticBody2D collision behavior.
///
/// Defines how the static body interacts with other physics bodies.
#[derive(Debu<PERSON>, Clone, PartialEq)]
pub struct PhysicsMaterial {
    /// Friction coefficient (0.0 to 1.0)
    pub friction: f32,
    /// Bounce/restitution coefficient (0.0 to 1.0)
    pub bounce: f32,
    /// Whether the material absorbs energy
    pub absorb: bool,
}

#[allow(dead_code)] // Comprehensive physics material implementation awaiting integration
impl PhysicsMaterial {
    /// ### Creates a new PhysicsMaterial with default properties.
    ///
    /// # Returns
    /// A new PhysicsMaterial with standard physics properties.
    pub fn new() -> Self {
        Self {
            friction: 1.0,
            bounce: 0.0,
            absorb: false,
        }
    }

    /// ### Creates a bouncy physics material.
    ///
    /// # Returns
    /// A PhysicsMaterial with high bounce and low friction.
    pub fn bouncy() -> Self {
        Self {
            friction: 0.2,
            bounce: 0.8,
            absorb: false,
        }
    }

    /// ### Creates a slippery physics material.
    ///
    /// # Returns
    /// A PhysicsMaterial with very low friction.
    pub fn slippery() -> Self {
        Self {
            friction: 0.1,
            bounce: 0.0,
            absorb: false,
        }
    }

    /// ### Creates a rough physics material.
    ///
    /// # Returns
    /// A PhysicsMaterial with high friction.
    pub fn rough() -> Self {
        Self {
            friction: 1.5,
            bounce: 0.0,
            absorb: true,
        }
    }
}

impl Default for PhysicsMaterial {
    fn default() -> Self {
        Self::new()
    }
}

/// ### Collision layers for physics interaction filtering.
///
/// Defines which layers this body exists on and which layers it can collide with.
#[derive(Debug, Clone, PartialEq)]
pub struct CollisionLayers {
    /// Layers this body exists on (bitmask)
    pub layer: u32,
    /// Layers this body can collide with (bitmask)
    pub mask: u32,
}

#[allow(dead_code)] // Comprehensive collision layers implementation awaiting integration
impl CollisionLayers {
    /// ### Creates new collision layers.
    ///
    /// # Parameters
    /// - `layer`: The layer this body exists on
    /// - `mask`: The layers this body can collide with
    ///
    /// # Returns
    /// A new CollisionLayers configuration.
    pub fn new(layer: u32, mask: u32) -> Self {
        Self { layer, mask }
    }

    /// ### Creates collision layers for all interactions.
    ///
    /// # Returns
    /// CollisionLayers that interact with everything.
    pub fn all() -> Self {
        Self {
            layer: 0xFFFFFFFF,
            mask: 0xFFFFFFFF,
        }
    }

    /// ### Creates collision layers for no interactions.
    ///
    /// # Returns
    /// CollisionLayers that interact with nothing.
    pub fn none() -> Self {
        Self {
            layer: 0,
            mask: 0,
        }
    }

    /// ### Checks if this body can collide with another layer.
    ///
    /// # Parameters
    /// - `other_layer`: The layer to check collision with
    ///
    /// # Returns
    /// True if collision is possible, false otherwise.
    pub fn can_collide_with(&self, other_layer: u32) -> bool {
        (self.mask & other_layer) != 0
    }

    /// ### Sets a specific layer bit.
    ///
    /// # Parameters
    /// - `bit`: The bit position (0-31)
    /// - `enabled`: Whether to enable this layer
    pub fn set_layer_bit(&mut self, bit: u8, enabled: bool) {
        if bit < 32 {
            if enabled {
                self.layer |= 1 << bit;
            } else {
                self.layer &= !(1 << bit);
            }
        }
    }

    /// ### Sets a specific mask bit.
    ///
    /// # Parameters
    /// - `bit`: The bit position (0-31)
    /// - `enabled`: Whether to enable this mask
    pub fn set_mask_bit(&mut self, bit: u8, enabled: bool) {
        if bit < 32 {
            if enabled {
                self.mask |= 1 << bit;
            } else {
                self.mask &= !(1 << bit);
            }
        }
    }

    /// ### Checks if a layer bit is set.
    ///
    /// # Parameters
    /// - `bit`: The bit position to check
    ///
    /// # Returns
    /// True if the layer bit is set, false otherwise.
    pub fn get_layer_bit(&self, bit: u8) -> bool {
        if bit < 32 {
            (self.layer & (1 << bit)) != 0
        } else {
            false
        }
    }

    /// ### Checks if a mask bit is set.
    ///
    /// # Parameters
    /// - `bit`: The bit position to check
    ///
    /// # Returns
    /// True if the mask bit is set, false otherwise.
    pub fn get_mask_bit(&self, bit: u8) -> bool {
        if bit < 32 {
            (self.mask & (1 << bit)) != 0
        } else {
            false
        }
    }
}

impl Default for CollisionLayers {
    fn default() -> Self {
        Self::new(1, 1) // Default to layer 1, mask 1
    }
}

/// ### StaticBody2D for immovable physics bodies with collision detection.
///
/// StaticBody2D extends Node2D with comprehensive physics collision functionality,
/// providing collision detection, physics material properties, collision signals,
/// and static body behavior. It maintains full compatibility with Godot's
/// StaticBody2D class while ensuring efficient collision handling and responsive
/// physics interactions.
///
/// ## Core Features
///
/// - **Collision Detection**: Precise collision detection with other physics bodies
/// - **Physics Materials**: Friction, bounce, and absorption properties
/// - **Collision Layers**: Layer-based collision filtering and interaction
/// - **Signal Integration**: Collision enter, exit, and stay signals
/// - **Static Behavior**: Immovable body that affects other physics objects
/// - **Shape Management**: Support for multiple collision shapes
/// - **Godot Compatibility**: API matching Godot's StaticBody2D
///
/// ## Physics Properties
///
/// StaticBody2D provides comprehensive physics control:
/// - **Material Properties**: Friction, bounce, and energy absorption
/// - **Collision Layers**: Layer and mask-based collision filtering
/// - **Shape Collection**: Multiple collision shapes per body
/// - **Collision Monitoring**: Detection of collision events
/// - **Physics Integration**: Seamless integration with physics world
/// - **Performance Optimization**: Efficient static body handling
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::StaticBody2D;
/// # use verturion::core::signal::SignalManager;
/// // Create a static body for a platform
/// let mut platform = StaticBody2D::new("Platform");
/// let mut signal_manager = SignalManager::new();
///
/// // Configure physics material
/// platform.set_friction(0.8);
/// platform.set_bounce(0.1);
///
/// // Set collision layers
/// platform.set_collision_layer(1);
/// platform.set_collision_mask(2);
///
/// // Enable collision monitoring
/// platform.set_monitoring(true);
///
/// // Register collision signals
/// signal_manager.register_signal(platform.get_body_entered_signal().clone());
///
/// assert_eq!(platform.get_friction(), 0.8);
/// assert_eq!(platform.get_collision_layer(), 1);
/// assert!(platform.is_monitoring());
/// ```
#[derive(Debug, Clone)]
pub struct StaticBody2D {
    /// Base Node2D functionality
    base: Node2D,
    /// Physics material properties
    physics_material: PhysicsMaterial,
    /// Collision layer configuration
    collision_layers: CollisionLayers,
    /// Whether collision monitoring is enabled
    monitoring: bool,
    /// Whether the body can be detected by other bodies
    monitorable: bool,
    /// Set of currently colliding body IDs
    colliding_bodies: HashSet<u64>,
    /// Signal emitted when a body enters collision
    body_entered_signal: Signal,
    /// Signal emitted when a body exits collision
    body_exited_signal: Signal,
    /// Signal emitted when a body shape enters collision
    body_shape_entered_signal: Signal,
    /// Signal emitted when a body shape exits collision
    body_shape_exited_signal: Signal,
}

#[allow(dead_code)] // Comprehensive static body implementation awaiting integration
impl StaticBody2D {
    /// ### Creates a new StaticBody2D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this static body
    ///
    /// # Returns
    /// A new StaticBody2D instance with default physics properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::StaticBody2D;
    /// let static_body = StaticBody2D::new("Wall");
    /// assert_eq!(static_body.get_name(), "Wall");
    /// assert_eq!(static_body.get_friction(), 1.0);
    /// assert_eq!(static_body.get_bounce(), 0.0);
    /// assert!(static_body.is_monitoring());
    /// assert!(static_body.is_monitorable());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        let node2d = Node2D::new(name);
        let node_id = node2d.base().get_id();
        Self {
            base: node2d,
            physics_material: PhysicsMaterial::default(),
            collision_layers: CollisionLayers::default(),
            monitoring: true,
            monitorable: true,
            colliding_bodies: HashSet::new(),
            body_entered_signal: Signal::new("body_entered", node_id),
            body_exited_signal: Signal::new("body_exited", node_id),
            body_shape_entered_signal: Signal::new("body_shape_entered", node_id),
            body_shape_exited_signal: Signal::new("body_shape_exited", node_id),
        }
    }

    /// ### Gets the friction coefficient.
    ///
    /// # Returns
    /// The friction coefficient (0.0 to 1.0+).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::StaticBody2D;
    /// let static_body = StaticBody2D::new("Body");
    /// assert_eq!(static_body.get_friction(), 1.0);
    /// ```
    #[inline]
    pub fn get_friction(&self) -> f32 {
        self.physics_material.friction
    }

    /// ### Sets the friction coefficient.
    ///
    /// # Parameters
    /// - `friction`: The new friction coefficient (0.0 to 1.0+)
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::StaticBody2D;
    /// let mut static_body = StaticBody2D::new("Body");
    /// static_body.set_friction(0.5);
    /// assert_eq!(static_body.get_friction(), 0.5);
    /// ```
    #[inline]
    pub fn set_friction(&mut self, friction: f32) {
        self.physics_material.friction = friction.max(0.0);
    }

    /// ### Gets the bounce coefficient.
    ///
    /// # Returns
    /// The bounce/restitution coefficient (0.0 to 1.0).
    #[inline]
    pub fn get_bounce(&self) -> f32 {
        self.physics_material.bounce
    }

    /// ### Sets the bounce coefficient.
    ///
    /// # Parameters
    /// - `bounce`: The new bounce coefficient (0.0 to 1.0)
    #[inline]
    pub fn set_bounce(&mut self, bounce: f32) {
        self.physics_material.bounce = bounce.max(0.0).min(1.0);
    }

    /// ### Checks if the material absorbs energy.
    ///
    /// # Returns
    /// True if the material absorbs energy, false otherwise.
    #[inline]
    pub fn is_absorb(&self) -> bool {
        self.physics_material.absorb
    }

    /// ### Sets whether the material absorbs energy.
    ///
    /// # Parameters
    /// - `absorb`: Whether the material should absorb energy
    #[inline]
    pub fn set_absorb(&mut self, absorb: bool) {
        self.physics_material.absorb = absorb;
    }

    /// ### Gets the physics material.
    ///
    /// # Returns
    /// A reference to the physics material.
    #[inline]
    pub fn get_physics_material(&self) -> &PhysicsMaterial {
        &self.physics_material
    }

    /// ### Sets the physics material.
    ///
    /// # Parameters
    /// - `material`: The new physics material
    #[inline]
    pub fn set_physics_material(&mut self, material: PhysicsMaterial) {
        self.physics_material = material;
    }

    /// ### Gets the collision layer.
    ///
    /// # Returns
    /// The collision layer bitmask.
    #[inline]
    pub fn get_collision_layer(&self) -> u32 {
        self.collision_layers.layer
    }

    /// ### Sets the collision layer.
    ///
    /// # Parameters
    /// - `layer`: The new collision layer bitmask
    #[inline]
    pub fn set_collision_layer(&mut self, layer: u32) {
        self.collision_layers.layer = layer;
    }

    /// ### Gets the collision mask.
    ///
    /// # Returns
    /// The collision mask bitmask.
    #[inline]
    pub fn get_collision_mask(&self) -> u32 {
        self.collision_layers.mask
    }

    /// ### Sets the collision mask.
    ///
    /// # Parameters
    /// - `mask`: The new collision mask bitmask
    #[inline]
    pub fn set_collision_mask(&mut self, mask: u32) {
        self.collision_layers.mask = mask;
    }

    /// ### Sets a collision layer bit.
    ///
    /// # Parameters
    /// - `bit`: The bit position (0-31)
    /// - `enabled`: Whether to enable this layer bit
    #[inline]
    pub fn set_collision_layer_bit(&mut self, bit: u8, enabled: bool) {
        self.collision_layers.set_layer_bit(bit, enabled);
    }

    /// ### Gets a collision layer bit.
    ///
    /// # Parameters
    /// - `bit`: The bit position to check
    ///
    /// # Returns
    /// True if the layer bit is set, false otherwise.
    #[inline]
    pub fn get_collision_layer_bit(&self, bit: u8) -> bool {
        self.collision_layers.get_layer_bit(bit)
    }

    /// ### Sets a collision mask bit.
    ///
    /// # Parameters
    /// - `bit`: The bit position (0-31)
    /// - `enabled`: Whether to enable this mask bit
    #[inline]
    pub fn set_collision_mask_bit(&mut self, bit: u8, enabled: bool) {
        self.collision_layers.set_mask_bit(bit, enabled);
    }

    /// ### Gets a collision mask bit.
    ///
    /// # Parameters
    /// - `bit`: The bit position to check
    ///
    /// # Returns
    /// True if the mask bit is set, false otherwise.
    #[inline]
    pub fn get_collision_mask_bit(&self, bit: u8) -> bool {
        self.collision_layers.get_mask_bit(bit)
    }

    /// ### Checks if monitoring is enabled.
    ///
    /// # Returns
    /// True if collision monitoring is enabled, false otherwise.
    #[inline]
    pub fn is_monitoring(&self) -> bool {
        self.monitoring
    }

    /// ### Sets whether monitoring is enabled.
    ///
    /// # Parameters
    /// - `monitoring`: Whether to enable collision monitoring
    #[inline]
    pub fn set_monitoring(&mut self, monitoring: bool) {
        self.monitoring = monitoring;
    }

    /// ### Checks if the body is monitorable.
    ///
    /// # Returns
    /// True if the body can be detected by other bodies, false otherwise.
    #[inline]
    pub fn is_monitorable(&self) -> bool {
        self.monitorable
    }

    /// ### Sets whether the body is monitorable.
    ///
    /// # Parameters
    /// - `monitorable`: Whether the body can be detected by others
    #[inline]
    pub fn set_monitorable(&mut self, monitorable: bool) {
        self.monitorable = monitorable;
    }

    /// ### Gets the list of colliding bodies.
    ///
    /// # Returns
    /// A vector of body IDs currently colliding with this body.
    #[inline]
    pub fn get_colliding_bodies(&self) -> Vec<u64> {
        self.colliding_bodies.iter().copied().collect()
    }

    /// ### Checks if a specific body is colliding.
    ///
    /// # Parameters
    /// - `body_id`: The ID of the body to check
    ///
    /// # Returns
    /// True if the body is currently colliding, false otherwise.
    #[inline]
    pub fn is_colliding_with(&self, body_id: u64) -> bool {
        self.colliding_bodies.contains(&body_id)
    }

    /// ### Gets the number of colliding bodies.
    ///
    /// # Returns
    /// The number of bodies currently colliding with this body.
    #[inline]
    pub fn get_collision_count(&self) -> usize {
        self.colliding_bodies.len()
    }

    /// ### Checks if any collision is occurring.
    ///
    /// # Returns
    /// True if any body is currently colliding, false otherwise.
    #[inline]
    pub fn has_collisions(&self) -> bool {
        !self.colliding_bodies.is_empty()
    }

    /// ### Simulates a collision with another body.
    ///
    /// This method is used by the physics system to register collisions.
    ///
    /// # Parameters
    /// - `body_id`: The ID of the colliding body
    /// - `signal_manager`: The signal manager for collision signals
    ///
    /// # Returns
    /// True if this is a new collision, false if already colliding.
    #[inline]
    pub fn add_collision(&mut self, body_id: u64, signal_manager: &mut SignalManager) -> bool {
        if self.monitoring && self.colliding_bodies.insert(body_id) {
            // Emit body entered signal
            let mut data = SignalData::empty();
            data.add_arg(Variant::Int(body_id as i64));
            signal_manager.emit(self.body_entered_signal.id(), data);
            true
        } else {
            false
        }
    }

    /// ### Removes a collision with another body.
    ///
    /// This method is used by the physics system to unregister collisions.
    ///
    /// # Parameters
    /// - `body_id`: The ID of the body no longer colliding
    /// - `signal_manager`: The signal manager for collision signals
    ///
    /// # Returns
    /// True if the collision was removed, false if not colliding.
    #[inline]
    pub fn remove_collision(&mut self, body_id: u64, signal_manager: &mut SignalManager) -> bool {
        if self.monitoring && self.colliding_bodies.remove(&body_id) {
            // Emit body exited signal
            let mut data = SignalData::empty();
            data.add_arg(Variant::Int(body_id as i64));
            signal_manager.emit(self.body_exited_signal.id(), data);
            true
        } else {
            false
        }
    }

    /// ### Clears all collisions.
    ///
    /// # Parameters
    /// - `signal_manager`: The signal manager for collision signals
    #[inline]
    pub fn clear_collisions(&mut self, signal_manager: &mut SignalManager) {
        if self.monitoring {
            for body_id in self.colliding_bodies.drain() {
                let mut data = SignalData::empty();
                data.add_arg(Variant::Int(body_id as i64));
                signal_manager.emit(self.body_exited_signal.id(), data);
            }
        }
    }

    /// ### Checks if this body can collide with another layer.
    ///
    /// # Parameters
    /// - `other_layer`: The layer to check collision with
    ///
    /// # Returns
    /// True if collision is possible, false otherwise.
    #[inline]
    pub fn can_collide_with_layer(&self, other_layer: u32) -> bool {
        self.collision_layers.can_collide_with(other_layer)
    }

    /// ### Gets the body entered signal.
    ///
    /// # Returns
    /// A reference to the body entered signal.
    #[inline]
    pub fn get_body_entered_signal(&self) -> &Signal {
        &self.body_entered_signal
    }

    /// ### Gets the body exited signal.
    ///
    /// # Returns
    /// A reference to the body exited signal.
    #[inline]
    pub fn get_body_exited_signal(&self) -> &Signal {
        &self.body_exited_signal
    }

    /// ### Gets the body shape entered signal.
    ///
    /// # Returns
    /// A reference to the body shape entered signal.
    #[inline]
    pub fn get_body_shape_entered_signal(&self) -> &Signal {
        &self.body_shape_entered_signal
    }

    /// ### Gets the body shape exited signal.
    ///
    /// # Returns
    /// A reference to the body shape exited signal.
    #[inline]
    pub fn get_body_shape_exited_signal(&self) -> &Signal {
        &self.body_shape_exited_signal
    }

    /// ### Updates the static body physics state.
    ///
    /// This method can be used for physics processing if needed.
    ///
    /// # Parameters
    /// - `delta`: The time step in seconds
    /// - `signal_manager`: The signal manager for physics signals
    #[inline]
    pub fn update(&mut self, _delta: f32, _signal_manager: &mut SignalManager) {
        // Static bodies don't move, but this can be used for collision processing
        // In a real physics engine, this would handle collision detection
    }

    /// ### Provides access to the base Node2D functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node2D.
    #[inline]
    pub fn base(&self) -> &Node2D {
        &self.base
    }

    /// ### Provides mutable access to the base Node2D functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node2D.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node2D {
        &mut self.base
    }

    /// ### Gets the node name from the base Node2D.
    ///
    /// # Returns
    /// The name of this static body.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }

    /// ### Gets the position from the base Node2D.
    ///
    /// # Returns
    /// The position of this static body.
    #[inline]
    pub fn get_position(&self) -> Vector2 {
        self.base.get_position()
    }

    /// ### Sets the position in the base Node2D.
    ///
    /// # Parameters
    /// - `position`: The new position for this static body
    #[inline]
    pub fn set_position(&mut self, position: Vector2) {
        self.base.set_position(position);
    }

    /// ### Gets the rotation from the base Node2D.
    ///
    /// # Returns
    /// The rotation of this static body in radians.
    #[inline]
    pub fn get_rotation(&self) -> f32 {
        self.base.get_rotation()
    }

    /// ### Sets the rotation in the base Node2D.
    ///
    /// # Parameters
    /// - `rotation`: The new rotation for this static body in radians
    #[inline]
    pub fn set_rotation(&mut self, rotation: f32) {
        self.base.set_rotation(rotation);
    }

    /// ### Gets the scale from the base Node2D.
    ///
    /// # Returns
    /// The scale of this static body.
    #[inline]
    pub fn get_scale(&self) -> Vector2 {
        self.base.get_scale()
    }

    /// ### Sets the scale in the base Node2D.
    ///
    /// # Parameters
    /// - `scale`: The new scale for this static body
    #[inline]
    pub fn set_scale(&mut self, scale: Vector2) {
        self.base.set_scale(scale);
    }
}

impl fmt::Display for StaticBody2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "StaticBody2D({}, pos: {}, layer: {}, mask: {}, collisions: {})",
               self.get_name(),
               self.get_position(),
               self.collision_layers.layer,
               self.collision_layers.mask,
               self.colliding_bodies.len())
    }
}

impl PartialEq for StaticBody2D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for StaticBody2D {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::signal::SignalManager;

    #[test]
    fn test_static_body2d_creation() {
        let static_body = StaticBody2D::new("TestBody");
        assert_eq!(static_body.get_name(), "TestBody");
        assert_eq!(static_body.get_friction(), 1.0);
        assert_eq!(static_body.get_bounce(), 0.0);
        assert!(!static_body.is_absorb());
        assert_eq!(static_body.get_collision_layer(), 1);
        assert_eq!(static_body.get_collision_mask(), 1);
        assert!(static_body.is_monitoring());
        assert!(static_body.is_monitorable());
        assert_eq!(static_body.get_collision_count(), 0);
        assert!(!static_body.has_collisions());
        assert_eq!(static_body.get_position(), Vector2::new(0.0, 0.0));
        assert_eq!(static_body.get_rotation(), 0.0);
        assert_eq!(static_body.get_scale(), Vector2::new(1.0, 1.0));
    }

    #[test]
    fn test_physics_material_operations() {
        let mut static_body = StaticBody2D::new("Body");

        // Test friction
        static_body.set_friction(0.5);
        assert_eq!(static_body.get_friction(), 0.5);

        // Test negative friction clamping
        static_body.set_friction(-1.0);
        assert_eq!(static_body.get_friction(), 0.0);

        // Test bounce
        static_body.set_bounce(0.8);
        assert_eq!(static_body.get_bounce(), 0.8);

        // Test bounce clamping
        static_body.set_bounce(1.5);
        assert_eq!(static_body.get_bounce(), 1.0);

        static_body.set_bounce(-0.5);
        assert_eq!(static_body.get_bounce(), 0.0);

        // Test absorb
        static_body.set_absorb(true);
        assert!(static_body.is_absorb());

        static_body.set_absorb(false);
        assert!(!static_body.is_absorb());
    }

    #[test]
    fn test_physics_material_presets() {
        // Test default material
        let default_material = PhysicsMaterial::new();
        assert_eq!(default_material.friction, 1.0);
        assert_eq!(default_material.bounce, 0.0);
        assert!(!default_material.absorb);

        // Test bouncy material
        let bouncy_material = PhysicsMaterial::bouncy();
        assert_eq!(bouncy_material.friction, 0.2);
        assert_eq!(bouncy_material.bounce, 0.8);
        assert!(!bouncy_material.absorb);

        // Test slippery material
        let slippery_material = PhysicsMaterial::slippery();
        assert_eq!(slippery_material.friction, 0.1);
        assert_eq!(slippery_material.bounce, 0.0);
        assert!(!slippery_material.absorb);

        // Test rough material
        let rough_material = PhysicsMaterial::rough();
        assert_eq!(rough_material.friction, 1.5);
        assert_eq!(rough_material.bounce, 0.0);
        assert!(rough_material.absorb);

        // Test setting custom material
        let mut static_body = StaticBody2D::new("Body");
        static_body.set_physics_material(bouncy_material.clone());
        assert_eq!(static_body.get_physics_material(), &bouncy_material);
    }

    #[test]
    fn test_collision_layers_operations() {
        let mut static_body = StaticBody2D::new("Body");

        // Test layer setting
        static_body.set_collision_layer(5);
        assert_eq!(static_body.get_collision_layer(), 5);

        // Test mask setting
        static_body.set_collision_mask(10);
        assert_eq!(static_body.get_collision_mask(), 10);

        // Test layer bits - start fresh
        static_body.set_collision_layer(0);
        static_body.set_collision_layer_bit(0, true);
        assert!(static_body.get_collision_layer_bit(0));
        assert_eq!(static_body.get_collision_layer(), 1); // 2^0 = 1

        static_body.set_collision_layer_bit(0, false);
        assert!(!static_body.get_collision_layer_bit(0));
        assert_eq!(static_body.get_collision_layer(), 0);

        static_body.set_collision_layer_bit(3, true);
        assert!(static_body.get_collision_layer_bit(3));
        assert_eq!(static_body.get_collision_layer(), 8); // 2^3 = 8

        // Test mask bits
        static_body.set_collision_mask_bit(1, true);
        assert!(static_body.get_collision_mask_bit(1));

        static_body.set_collision_mask_bit(1, false);
        assert!(!static_body.get_collision_mask_bit(1));

        // Test invalid bit positions
        static_body.set_collision_layer_bit(32, true);
        static_body.set_collision_mask_bit(32, true);
        assert!(!static_body.get_collision_layer_bit(32));
        assert!(!static_body.get_collision_mask_bit(32));
    }

    #[test]
    fn test_collision_layers_utility() {
        // Test collision layer creation
        let layers = CollisionLayers::new(5, 10);
        assert_eq!(layers.layer, 5);
        assert_eq!(layers.mask, 10);

        // Test all layers
        let all_layers = CollisionLayers::all();
        assert_eq!(all_layers.layer, 0xFFFFFFFF);
        assert_eq!(all_layers.mask, 0xFFFFFFFF);

        // Test no layers
        let no_layers = CollisionLayers::none();
        assert_eq!(no_layers.layer, 0);
        assert_eq!(no_layers.mask, 0);

        // Test collision detection
        let mut layers = CollisionLayers::new(0, 5); // mask = 5 (bits 0 and 2)
        assert!(layers.can_collide_with(1)); // bit 0 set
        assert!(!layers.can_collide_with(2)); // bit 1 not in mask
        assert!(layers.can_collide_with(4)); // bit 2 set
        assert!(!layers.can_collide_with(8)); // bit 3 not in mask

        // Test bit manipulation
        layers.set_layer_bit(1, true);
        assert!(layers.get_layer_bit(1));
        assert_eq!(layers.layer, 2); // bit 1 set

        layers.set_mask_bit(3, true);
        assert!(layers.get_mask_bit(3));
        assert!(layers.can_collide_with(8)); // bit 3 now in mask
    }

    #[test]
    fn test_monitoring_properties() {
        let mut static_body = StaticBody2D::new("Body");

        // Test monitoring
        assert!(static_body.is_monitoring());
        static_body.set_monitoring(false);
        assert!(!static_body.is_monitoring());

        // Test monitorable
        assert!(static_body.is_monitorable());
        static_body.set_monitorable(false);
        assert!(!static_body.is_monitorable());
    }

    #[test]
    fn test_collision_management() {
        let mut static_body = StaticBody2D::new("Body");
        let mut signal_manager = SignalManager::new();

        // Register signals
        signal_manager.register_signal(static_body.get_body_entered_signal().clone());
        signal_manager.register_signal(static_body.get_body_exited_signal().clone());

        // Test adding collisions
        assert!(static_body.add_collision(100, &mut signal_manager));
        assert!(static_body.is_colliding_with(100));
        assert_eq!(static_body.get_collision_count(), 1);
        assert!(static_body.has_collisions());

        // Test adding duplicate collision
        assert!(!static_body.add_collision(100, &mut signal_manager));
        assert_eq!(static_body.get_collision_count(), 1);

        // Test adding multiple collisions
        assert!(static_body.add_collision(200, &mut signal_manager));
        assert!(static_body.add_collision(300, &mut signal_manager));
        assert_eq!(static_body.get_collision_count(), 3);

        let colliding_bodies = static_body.get_colliding_bodies();
        assert!(colliding_bodies.contains(&100));
        assert!(colliding_bodies.contains(&200));
        assert!(colliding_bodies.contains(&300));

        // Test removing collisions
        assert!(static_body.remove_collision(200, &mut signal_manager));
        assert!(!static_body.is_colliding_with(200));
        assert_eq!(static_body.get_collision_count(), 2);

        // Test removing non-existent collision
        assert!(!static_body.remove_collision(400, &mut signal_manager));
        assert_eq!(static_body.get_collision_count(), 2);

        // Test clearing all collisions
        static_body.clear_collisions(&mut signal_manager);
        assert_eq!(static_body.get_collision_count(), 0);
        assert!(!static_body.has_collisions());
    }

    #[test]
    fn test_collision_monitoring_disabled() {
        let mut static_body = StaticBody2D::new("Body");
        let mut signal_manager = SignalManager::new();

        // Disable monitoring
        static_body.set_monitoring(false);

        // Test that collisions are not tracked when monitoring is disabled
        assert!(!static_body.add_collision(100, &mut signal_manager));
        assert!(!static_body.is_colliding_with(100));
        assert_eq!(static_body.get_collision_count(), 0);

        // Test that removal doesn't work when monitoring is disabled
        assert!(!static_body.remove_collision(100, &mut signal_manager));
    }

    #[test]
    fn test_collision_layer_compatibility() {
        let static_body = StaticBody2D::new("Body");

        // Test layer collision compatibility
        assert!(static_body.can_collide_with_layer(1)); // Default mask is 1
        assert!(!static_body.can_collide_with_layer(2)); // Not in mask
        assert!(!static_body.can_collide_with_layer(4)); // Not in mask
    }

    #[test]
    fn test_transform_operations() {
        let mut static_body = StaticBody2D::new("Body");

        // Test position
        let new_position = Vector2::new(10.0, 20.0);
        static_body.set_position(new_position);
        assert_eq!(static_body.get_position(), new_position);

        // Test rotation
        static_body.set_rotation(1.5);
        assert_eq!(static_body.get_rotation(), 1.5);

        // Test scale
        let new_scale = Vector2::new(2.0, 3.0);
        static_body.set_scale(new_scale);
        assert_eq!(static_body.get_scale(), new_scale);
    }

    #[test]
    fn test_signals() {
        let static_body = StaticBody2D::new("Body");

        // Test signal access
        assert_eq!(static_body.get_body_entered_signal().name(), "body_entered");
        assert_eq!(static_body.get_body_exited_signal().name(), "body_exited");
        assert_eq!(static_body.get_body_shape_entered_signal().name(), "body_shape_entered");
        assert_eq!(static_body.get_body_shape_exited_signal().name(), "body_shape_exited");
    }

    #[test]
    fn test_update_method() {
        let mut static_body = StaticBody2D::new("Body");
        let mut signal_manager = SignalManager::new();

        // Test update (should not crash or change state)
        let old_position = static_body.get_position();
        static_body.update(0.016, &mut signal_manager);
        assert_eq!(static_body.get_position(), old_position);
    }

    #[test]
    fn test_base_access() {
        let mut static_body = StaticBody2D::new("BaseTest");

        // Test base access
        assert_eq!(static_body.base().base().get_name(), "BaseTest");

        // Test mutable base access
        static_body.base_mut().base_mut().set_name("NewName");
        assert_eq!(static_body.get_name(), "NewName");
    }

    #[test]
    fn test_equality() {
        let static_body1 = StaticBody2D::new("Body1");
        let static_body2 = StaticBody2D::new("Body2");
        let static_body1_clone = static_body1.clone();

        // Same static body should be equal
        assert_eq!(static_body1, static_body1_clone);

        // Different static bodies should not be equal
        assert_ne!(static_body1, static_body2);
    }

    #[test]
    fn test_display() {
        let mut static_body = StaticBody2D::new("DisplayTest");
        let mut signal_manager = SignalManager::new();

        static_body.set_position(Vector2::new(5.0, 10.0));
        static_body.set_collision_layer(3);
        static_body.set_collision_mask(7);
        static_body.add_collision(100, &mut signal_manager);

        let display_str = format!("{}", static_body);
        assert!(display_str.contains("DisplayTest"));
        assert!(display_str.contains("(5, 10)"));
        assert!(display_str.contains("layer: 3"));
        assert!(display_str.contains("mask: 7"));
        assert!(display_str.contains("collisions: 1"));
    }

    #[test]
    fn test_complex_static_body_scenario() {
        let mut platform = StaticBody2D::new("Platform");
        let mut wall = StaticBody2D::new("Wall");
        let mut signal_manager = SignalManager::new();

        // Register all signals
        signal_manager.register_signal(platform.get_body_entered_signal().clone());
        signal_manager.register_signal(platform.get_body_exited_signal().clone());
        signal_manager.register_signal(wall.get_body_entered_signal().clone());
        signal_manager.register_signal(wall.get_body_exited_signal().clone());

        // Configure platform as a bouncy surface
        platform.set_physics_material(PhysicsMaterial::bouncy());
        platform.set_position(Vector2::new(0.0, 100.0));
        platform.set_collision_layer(1);
        platform.set_collision_mask(2); // Can collide with player layer

        // Configure wall as a rough surface
        wall.set_physics_material(PhysicsMaterial::rough());
        wall.set_position(Vector2::new(50.0, 0.0));
        wall.set_collision_layer(4);
        wall.set_collision_mask(2); // Can collide with player layer

        // Simulate player (ID 1000) on layer 2
        let player_id = 1000;
        let player_layer = 2;

        // Test platform collision
        assert!(platform.can_collide_with_layer(player_layer));
        assert!(platform.add_collision(player_id, &mut signal_manager));
        assert!(platform.is_colliding_with(player_id));

        // Test wall collision
        assert!(wall.can_collide_with_layer(player_layer));
        assert!(wall.add_collision(player_id, &mut signal_manager));
        assert!(wall.is_colliding_with(player_id));

        // Verify both bodies are tracking the player
        assert_eq!(platform.get_collision_count(), 1);
        assert_eq!(wall.get_collision_count(), 1);

        // Test physics material properties
        assert_eq!(platform.get_friction(), 0.2);
        assert_eq!(platform.get_bounce(), 0.8);
        assert_eq!(wall.get_friction(), 1.5);
        assert!(wall.is_absorb());

        // Simulate player leaving platform but staying on wall
        assert!(platform.remove_collision(player_id, &mut signal_manager));
        assert!(!platform.is_colliding_with(player_id));
        assert!(wall.is_colliding_with(player_id));

        // Test collision layer filtering
        let _enemy_id = 2000;
        let enemy_layer = 8; // Different layer

        // Platform and wall should not collide with enemy layer
        assert!(!platform.can_collide_with_layer(enemy_layer));
        assert!(!wall.can_collide_with_layer(enemy_layer));

        // Even if we try to add collision, it should be filtered out
        // (In a real physics engine, this would be handled by the collision detection system)

        // Test disabling monitoring
        wall.set_monitoring(false);
        assert!(!wall.add_collision(3000, &mut signal_manager));

        // Test clearing all collisions
        wall.set_monitoring(true);
        wall.add_collision(3000, &mut signal_manager);
        wall.add_collision(4000, &mut signal_manager);
        assert_eq!(wall.get_collision_count(), 3); // player + 2 new

        wall.clear_collisions(&mut signal_manager);
        assert_eq!(wall.get_collision_count(), 0);

        // Test transform operations
        platform.set_rotation(0.5);
        platform.set_scale(Vector2::new(2.0, 1.0));

        // Verify final state
        assert_eq!(platform.get_rotation(), 0.5);
        assert_eq!(platform.get_scale(), Vector2::new(2.0, 1.0));
        assert_eq!(platform.get_position(), Vector2::new(0.0, 100.0));
        assert!(!platform.has_collisions());
        assert!(!wall.has_collisions());

        // Test update method
        platform.update(0.016, &mut signal_manager);
        wall.update(0.016, &mut signal_manager);

        // Static bodies should not move during update
        assert_eq!(platform.get_position(), Vector2::new(0.0, 100.0));
        assert_eq!(wall.get_position(), Vector2::new(50.0, 0.0));

        println!("Platform: {}", platform);
        println!("Wall: {}", wall);
    }
}
