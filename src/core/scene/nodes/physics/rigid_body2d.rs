//! RigidBody2D implementation for 2D physics bodies with comprehensive dynamics.
//!
//! This module provides the RigidBody2D class that extends Node2D with physics
//! body functionality including mass, velocity, forces, and collision response.
//! It maintains full compatibility with Godot's RigidBody2D class while providing
//! efficient physics simulation for game development.

use std::fmt;
use crate::core::math::Vector2;
use crate::core::scene::nodes::Node2D;

/// ### RigidBody2D modes for different physics behaviors.
///
/// Defines how the rigid body behaves in the physics simulation.
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum RigidBodyMode {
    /// Dynamic body affected by forces and collisions
    Dynamic,
    /// Static body that doesn't move
    Static,
    /// Kinematic body controlled by code
    Kinematic,
    /// Character body for character controllers
    Character,
}

/// ### Continuous Collision Detection modes for fast-moving bodies.
///
/// Defines the level of continuous collision detection.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq)]
#[allow(dead_code)] // Complete CCD mode options for comprehensive physics API
pub enum CCDMode {
    /// No continuous collision detection
    Disabled,
    /// Cast ray for continuous collision detection
    CastRay,
    /// Cast shape for continuous collision detection
    CastShape,
}

/// ### 2D physics body with comprehensive dynamics and collision response.
///
/// RigidBody2D extends Node2D with comprehensive physics body functionality,
/// providing mass, velocity, forces, torque, and collision response for realistic
/// physics simulation. It maintains full compatibility with Godot's RigidBody2D
/// class while ensuring efficient physics integration and dynamics.
///
/// ## Core Features
///
/// - **Physics Dynamics**: Mass, velocity, acceleration, and forces
/// - **Collision Response**: Automatic collision detection and response
/// - **Body Modes**: Dynamic, static, kinematic, and character modes
/// - **Force Application**: Apply forces, impulses, and torque
/// - **Material Properties**: Friction, bounce, and density
/// - **Continuous Collision**: CCD for fast-moving objects
/// - **Godot Compatibility**: API matching Godot's RigidBody2D class
///
/// ## Physics Properties
///
/// RigidBody2D provides comprehensive physics simulation:
/// - **Mass**: Body mass affecting acceleration and momentum
/// - **Velocity**: Linear and angular velocity
/// - **Forces**: Applied forces and torque
/// - **Damping**: Linear and angular damping
/// - **Material**: Friction, bounce, and absorption properties
/// - **Constraints**: Lock rotation and position axes
///
/// # Examples
/// ```
/// # use verturion::core::scene::nodes::physics::{RigidBody2D, RigidBodyMode};
/// # use verturion::core::math::Vector2;
/// // Create a physics body
/// let mut body = RigidBody2D::new("PhysicsBody");
///
/// // Configure physics properties
/// body.set_mode(RigidBodyMode::Dynamic);
/// body.set_mass(10.0);
/// body.set_linear_velocity(Vector2::new(100.0, 0.0));
/// body.set_gravity_scale(1.5);
///
/// // Apply forces
/// body.apply_central_force(Vector2::new(0.0, -500.0));
/// body.apply_torque(50.0);
///
/// assert_eq!(body.get_mass(), 10.0);
/// assert_eq!(body.get_mode(), RigidBodyMode::Dynamic);
/// ```
#[derive(Debug, Clone)]
#[allow(dead_code)] // Comprehensive implementation - fields will be used when physics system is implemented
pub struct RigidBody2D {
    /// Base Node2D functionality
    base: Node2D,
    /// Physics body mode
    mode: RigidBodyMode,
    /// Body mass in kilograms
    mass: f32,
    /// Linear velocity in pixels per second
    linear_velocity: Vector2,
    /// Angular velocity in radians per second
    angular_velocity: f32,
    /// Applied forces accumulator
    applied_force: Vector2,
    /// Applied torque accumulator
    applied_torque: f32,
    /// Linear damping factor
    linear_damp: f32,
    /// Angular damping factor
    angular_damp: f32,
    /// Gravity scale multiplier
    gravity_scale: f32,
    /// Whether the body can sleep when at rest
    can_sleep: bool,
    /// Whether the body is currently sleeping
    sleeping: bool,
    /// Continuous collision detection mode
    ccd_mode: CCDMode,
    /// Custom center of mass offset
    center_of_mass_mode: bool,
    /// Center of mass offset
    center_of_mass: Vector2,
    /// Inertia override
    inertia: Option<f32>,
    /// Whether to lock rotation
    lock_rotation: bool,
    /// Whether to freeze position on X axis
    freeze_x: bool,
    /// Whether to freeze position on Y axis
    freeze_y: bool,
}

#[allow(dead_code)] // Comprehensive 2D rigid body implementation awaiting integration
impl RigidBody2D {
    /// ### Creates a new RigidBody2D with the specified name.
    ///
    /// # Parameters
    /// - `name`: The name for this rigid body node
    ///
    /// # Returns
    /// A new RigidBody2D instance with default physics properties.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::scene::nodes::physics::RigidBody2D;
    /// let body = RigidBody2D::new("PhysicsBody");
    /// assert_eq!(body.get_name(), "PhysicsBody");
    /// assert_eq!(body.get_mass(), 1.0);
    /// assert!(!body.is_sleeping());
    /// ```
    #[inline]
    pub fn new(name: &str) -> Self {
        Self {
            base: Node2D::new(name),
            mode: RigidBodyMode::Dynamic,
            mass: 1.0,
            linear_velocity: Vector2::ZERO,
            angular_velocity: 0.0,
            applied_force: Vector2::ZERO,
            applied_torque: 0.0,
            linear_damp: 0.0,
            angular_damp: 0.0,
            gravity_scale: 1.0,
            can_sleep: true,
            sleeping: false,
            ccd_mode: CCDMode::Disabled,
            center_of_mass_mode: false,
            center_of_mass: Vector2::ZERO,
            inertia: None,
            lock_rotation: false,
            freeze_x: false,
            freeze_y: false,
        }
    }

    /// ### Gets the rigid body mode.
    ///
    /// # Returns
    /// The current body mode.
    #[inline]
    pub fn get_mode(&self) -> RigidBodyMode {
        self.mode
    }

    /// ### Sets the rigid body mode.
    ///
    /// # Parameters
    /// - `mode`: The new body mode
    #[inline]
    pub fn set_mode(&mut self, mode: RigidBodyMode) {
        self.mode = mode;
        // Reset velocities for static bodies
        if mode == RigidBodyMode::Static {
            self.linear_velocity = Vector2::ZERO;
            self.angular_velocity = 0.0;
        }
    }

    /// ### Gets the body mass.
    ///
    /// # Returns
    /// The current mass in kilograms.
    #[inline]
    pub fn get_mass(&self) -> f32 {
        self.mass
    }

    /// ### Sets the body mass.
    ///
    /// # Parameters
    /// - `mass`: The new mass in kilograms
    #[inline]
    pub fn set_mass(&mut self, mass: f32) {
        self.mass = mass.max(0.001); // Ensure positive mass
    }

    /// ### Gets the linear velocity.
    ///
    /// # Returns
    /// The current linear velocity.
    #[inline]
    pub fn get_linear_velocity(&self) -> Vector2 {
        self.linear_velocity
    }

    /// ### Sets the linear velocity.
    ///
    /// # Parameters
    /// - `velocity`: The new linear velocity
    #[inline]
    pub fn set_linear_velocity(&mut self, velocity: Vector2) {
        if self.mode != RigidBodyMode::Static {
            self.linear_velocity = velocity;
        }
    }

    /// ### Gets the angular velocity.
    ///
    /// # Returns
    /// The current angular velocity in radians per second.
    #[inline]
    pub fn get_angular_velocity(&self) -> f32 {
        self.angular_velocity
    }

    /// ### Sets the angular velocity.
    ///
    /// # Parameters
    /// - `velocity`: The new angular velocity in radians per second
    #[inline]
    pub fn set_angular_velocity(&mut self, velocity: f32) {
        if self.mode != RigidBodyMode::Static && !self.lock_rotation {
            self.angular_velocity = velocity;
        }
    }

    /// ### Gets the gravity scale.
    ///
    /// # Returns
    /// The current gravity scale multiplier.
    #[inline]
    pub fn get_gravity_scale(&self) -> f32 {
        self.gravity_scale
    }

    /// ### Sets the gravity scale.
    ///
    /// # Parameters
    /// - `scale`: The new gravity scale multiplier
    #[inline]
    pub fn set_gravity_scale(&mut self, scale: f32) {
        self.gravity_scale = scale;
    }

    /// ### Gets the linear damping.
    ///
    /// # Returns
    /// The current linear damping factor.
    #[inline]
    pub fn get_linear_damp(&self) -> f32 {
        self.linear_damp
    }

    /// ### Sets the linear damping.
    ///
    /// # Parameters
    /// - `damp`: The new linear damping factor
    #[inline]
    pub fn set_linear_damp(&mut self, damp: f32) {
        self.linear_damp = damp.max(0.0);
    }

    /// ### Gets the angular damping.
    ///
    /// # Returns
    /// The current angular damping factor.
    #[inline]
    pub fn get_angular_damp(&self) -> f32 {
        self.angular_damp
    }

    /// ### Sets the angular damping.
    ///
    /// # Parameters
    /// - `damp`: The new angular damping factor
    #[inline]
    pub fn set_angular_damp(&mut self, damp: f32) {
        self.angular_damp = damp.max(0.0);
    }

    /// ### Checks if the body can sleep.
    ///
    /// # Returns
    /// True if the body can sleep when at rest, false otherwise.
    #[inline]
    pub fn is_able_to_sleep(&self) -> bool {
        self.can_sleep
    }

    /// ### Sets whether the body can sleep.
    ///
    /// # Parameters
    /// - `able_to_sleep`: Whether the body can sleep when at rest
    #[inline]
    pub fn set_can_sleep(&mut self, able_to_sleep: bool) {
        self.can_sleep = able_to_sleep;
        if !able_to_sleep {
            self.sleeping = false;
        }
    }

    /// ### Checks if the body is currently sleeping.
    ///
    /// # Returns
    /// True if the body is sleeping, false otherwise.
    #[inline]
    pub fn is_sleeping(&self) -> bool {
        self.sleeping
    }

    /// ### Sets the sleeping state.
    ///
    /// # Parameters
    /// - `sleeping`: Whether the body should be sleeping
    #[inline]
    pub fn set_sleeping(&mut self, sleeping: bool) {
        if self.can_sleep {
            self.sleeping = sleeping;
            if sleeping {
                self.linear_velocity = Vector2::ZERO;
                self.angular_velocity = 0.0;
            }
        }
    }

    /// ### Applies a central force to the body.
    ///
    /// # Parameters
    /// - `force`: The force vector to apply
    #[inline]
    pub fn apply_central_force(&mut self, force: Vector2) {
        if self.mode == RigidBodyMode::Dynamic {
            self.applied_force = self.applied_force + force;
            self.sleeping = false;
        }
    }

    /// ### Applies a force at a specific point.
    ///
    /// # Parameters
    /// - `force`: The force vector to apply
    /// - `position`: The position to apply the force at
    #[inline]
    pub fn apply_force(&mut self, force: Vector2, position: Vector2) {
        if self.mode == RigidBodyMode::Dynamic {
            self.applied_force = self.applied_force + force;
            // Calculate torque from force and position offset
            let center = self.base.get_position() + self.center_of_mass;
            let offset = position - center;
            let torque = offset.x * force.y - offset.y * force.x;
            self.applied_torque += torque;
            self.sleeping = false;
        }
    }

    /// ### Applies torque to the body.
    ///
    /// # Parameters
    /// - `torque`: The torque to apply
    #[inline]
    pub fn apply_torque(&mut self, torque: f32) {
        if self.mode == RigidBodyMode::Dynamic && !self.lock_rotation {
            self.applied_torque += torque;
            self.sleeping = false;
        }
    }

    /// ### Applies a central impulse to the body.
    ///
    /// # Parameters
    /// - `impulse`: The impulse vector to apply
    #[inline]
    pub fn apply_central_impulse(&mut self, impulse: Vector2) {
        if self.mode == RigidBodyMode::Dynamic {
            self.linear_velocity = self.linear_velocity + impulse / self.mass;
            self.sleeping = false;
        }
    }

    /// ### Applies a torque impulse to the body.
    ///
    /// # Parameters
    /// - `impulse`: The torque impulse to apply
    #[inline]
    pub fn apply_torque_impulse(&mut self, impulse: f32) {
        if self.mode == RigidBodyMode::Dynamic && !self.lock_rotation {
            let inertia = self.inertia.unwrap_or(self.mass * 10.0); // Simple inertia calculation
            self.angular_velocity += impulse / inertia;
            self.sleeping = false;
        }
    }

    /// ### Provides access to the base Node2D functionality.
    ///
    /// # Returns
    /// A reference to the underlying Node2D.
    #[inline]
    pub fn base(&self) -> &Node2D {
        &self.base
    }

    /// ### Provides mutable access to the base Node2D functionality.
    ///
    /// # Returns
    /// A mutable reference to the underlying Node2D.
    #[inline]
    pub fn base_mut(&mut self) -> &mut Node2D {
        &mut self.base
    }

    /// ### Gets the node name from the base Node2D.
    ///
    /// # Returns
    /// The name of this rigid body node.
    #[inline]
    pub fn get_name(&self) -> String {
        self.base.base().get_name()
    }
}

impl fmt::Display for RigidBody2D {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "RigidBody2D({}, mode: {:?}, mass: {:.2}, vel: {:.1}, sleeping: {})",
               self.get_name(), self.mode, self.mass,
               self.linear_velocity.length(), self.sleeping)
    }
}

impl PartialEq for RigidBody2D {
    fn eq(&self, other: &Self) -> bool {
        self.base == other.base
    }
}

impl Eq for RigidBody2D {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rigid_body2d_creation() {
        let body = RigidBody2D::new("TestBody");
        assert_eq!(body.get_name(), "TestBody");
        assert_eq!(body.get_mode(), RigidBodyMode::Dynamic);
        assert_eq!(body.get_mass(), 1.0);
        assert_eq!(body.get_linear_velocity(), Vector2::ZERO);
        assert_eq!(body.get_angular_velocity(), 0.0);
        assert_eq!(body.get_gravity_scale(), 1.0);
        assert!(body.is_able_to_sleep());
        assert!(!body.is_sleeping());
    }

    #[test]
    fn test_rigid_body2d_mode_management() {
        let mut body = RigidBody2D::new("Body");

        // Initially dynamic
        assert_eq!(body.get_mode(), RigidBodyMode::Dynamic);

        // Test all modes
        body.set_mode(RigidBodyMode::Static);
        assert_eq!(body.get_mode(), RigidBodyMode::Static);
        assert_eq!(body.get_linear_velocity(), Vector2::ZERO); // Should reset velocities
        assert_eq!(body.get_angular_velocity(), 0.0);

        body.set_mode(RigidBodyMode::Kinematic);
        assert_eq!(body.get_mode(), RigidBodyMode::Kinematic);

        body.set_mode(RigidBodyMode::Character);
        assert_eq!(body.get_mode(), RigidBodyMode::Character);

        body.set_mode(RigidBodyMode::Dynamic);
        assert_eq!(body.get_mode(), RigidBodyMode::Dynamic);
    }

    #[test]
    fn test_rigid_body2d_mass_management() {
        let mut body = RigidBody2D::new("Body");

        // Initially 1.0 kg
        assert_eq!(body.get_mass(), 1.0);

        // Set custom mass
        body.set_mass(5.0);
        assert_eq!(body.get_mass(), 5.0);

        // Test minimum mass (should be clamped)
        body.set_mass(0.0);
        assert_eq!(body.get_mass(), 0.001);

        body.set_mass(-1.0);
        assert_eq!(body.get_mass(), 0.001);
    }

    #[test]
    fn test_rigid_body2d_velocity_management() {
        let mut body = RigidBody2D::new("Body");

        // Initially zero velocity
        assert_eq!(body.get_linear_velocity(), Vector2::ZERO);
        assert_eq!(body.get_angular_velocity(), 0.0);

        // Set velocities
        body.set_linear_velocity(Vector2::new(100.0, -50.0));
        body.set_angular_velocity(2.5);

        assert_eq!(body.get_linear_velocity(), Vector2::new(100.0, -50.0));
        assert_eq!(body.get_angular_velocity(), 2.5);

        // Test static body (should not allow velocity changes)
        body.set_mode(RigidBodyMode::Static);
        body.set_linear_velocity(Vector2::new(200.0, 100.0));
        body.set_angular_velocity(5.0);

        assert_eq!(body.get_linear_velocity(), Vector2::ZERO);
        assert_eq!(body.get_angular_velocity(), 0.0);
    }

    #[test]
    fn test_rigid_body2d_gravity_scale() {
        let mut body = RigidBody2D::new("Body");

        // Initially 1.0
        assert_eq!(body.get_gravity_scale(), 1.0);

        // Set custom gravity scale
        body.set_gravity_scale(2.5);
        assert_eq!(body.get_gravity_scale(), 2.5);

        // Test negative gravity (anti-gravity)
        body.set_gravity_scale(-0.5);
        assert_eq!(body.get_gravity_scale(), -0.5);

        // Test zero gravity
        body.set_gravity_scale(0.0);
        assert_eq!(body.get_gravity_scale(), 0.0);
    }

    #[test]
    fn test_rigid_body2d_damping() {
        let mut body = RigidBody2D::new("Body");

        // Initially zero damping
        assert_eq!(body.get_linear_damp(), 0.0);
        assert_eq!(body.get_angular_damp(), 0.0);

        // Set custom damping
        body.set_linear_damp(0.5);
        body.set_angular_damp(1.2);

        assert_eq!(body.get_linear_damp(), 0.5);
        assert_eq!(body.get_angular_damp(), 1.2);

        // Test negative damping (should be clamped to 0)
        body.set_linear_damp(-1.0);
        body.set_angular_damp(-0.5);

        assert_eq!(body.get_linear_damp(), 0.0);
        assert_eq!(body.get_angular_damp(), 0.0);
    }

    #[test]
    fn test_rigid_body2d_sleep_management() {
        let mut body = RigidBody2D::new("Body");

        // Initially can sleep but not sleeping
        assert!(body.is_able_to_sleep());
        assert!(!body.is_sleeping());

        // Put body to sleep
        body.set_sleeping(true);
        assert!(body.is_sleeping());
        assert_eq!(body.get_linear_velocity(), Vector2::ZERO); // Should reset velocities
        assert_eq!(body.get_angular_velocity(), 0.0);

        // Wake up body
        body.set_sleeping(false);
        assert!(!body.is_sleeping());

        // Disable sleeping ability
        body.set_can_sleep(false);
        assert!(!body.is_able_to_sleep());
        assert!(!body.is_sleeping()); // Should wake up

        // Try to sleep when can't sleep (should not work)
        body.set_sleeping(true);
        assert!(!body.is_sleeping());

        // Re-enable sleeping
        body.set_can_sleep(true);
        assert!(body.is_able_to_sleep());
    }

    #[test]
    fn test_rigid_body2d_force_application() {
        let mut body = RigidBody2D::new("Body");
        body.set_sleeping(true); // Start sleeping

        // Apply central force
        body.apply_central_force(Vector2::new(100.0, -200.0));
        assert!(!body.is_sleeping()); // Should wake up

        // Test static body (should not respond to forces)
        body.set_mode(RigidBodyMode::Static);
        body.apply_central_force(Vector2::new(500.0, 0.0));
        // Static bodies don't accumulate forces, so we can't test the applied_force directly

        // Back to dynamic
        body.set_mode(RigidBodyMode::Dynamic);
        body.apply_central_force(Vector2::new(50.0, 100.0));
        assert!(!body.is_sleeping());
    }

    #[test]
    fn test_rigid_body2d_torque_application() {
        let mut body = RigidBody2D::new("Body");
        body.set_sleeping(true); // Start sleeping

        // Apply torque
        body.apply_torque(10.0);
        assert!(!body.is_sleeping()); // Should wake up

        // Test static body (should not respond to torque)
        body.set_mode(RigidBodyMode::Static);
        body.apply_torque(50.0);
        // Static bodies don't accumulate torque

        // Back to dynamic
        body.set_mode(RigidBodyMode::Dynamic);
        body.apply_torque(-5.0);
        assert!(!body.is_sleeping());
    }

    #[test]
    fn test_rigid_body2d_impulse_application() {
        let mut body = RigidBody2D::new("Body");
        body.set_mass(2.0);
        body.set_sleeping(true); // Start sleeping

        // Apply central impulse
        body.apply_central_impulse(Vector2::new(20.0, 0.0));
        assert!(!body.is_sleeping()); // Should wake up
        assert_eq!(body.get_linear_velocity(), Vector2::new(10.0, 0.0)); // impulse / mass

        // Apply torque impulse
        body.apply_torque_impulse(40.0);
        assert!(body.get_angular_velocity() > 0.0); // Should have angular velocity

        // Test static body (should not respond to impulses)
        body.set_mode(RigidBodyMode::Static);
        let old_velocity = body.get_linear_velocity();
        body.apply_central_impulse(Vector2::new(100.0, 0.0));
        assert_eq!(body.get_linear_velocity(), old_velocity); // Should not change
    }

    #[test]
    fn test_rigid_body2d_force_at_position() {
        let mut body = RigidBody2D::new("Body");
        body.base_mut().set_position(Vector2::new(100.0, 100.0));

        // Apply force at offset position (should generate torque)
        let force_position = Vector2::new(110.0, 100.0); // 10 units to the right
        body.apply_force(Vector2::new(0.0, 100.0), force_position); // Upward force

        assert!(!body.is_sleeping()); // Should wake up
        // The force should generate torque due to the offset
    }

    #[test]
    fn test_rigid_body2d_base_access() {
        let mut body = RigidBody2D::new("Body");

        // Test base access
        assert_eq!(body.base().base().get_name(), "Body");

        // Test mutable base access
        body.base_mut().set_position(Vector2::new(300.0, 400.0));
        assert_eq!(body.base().get_position(), Vector2::new(300.0, 400.0));
    }

    #[test]
    fn test_rigid_body2d_equality() {
        let body1 = RigidBody2D::new("Body1");
        let body2 = RigidBody2D::new("Body2");
        let body1_clone = body1.clone();

        // Same body should be equal
        assert_eq!(body1, body1_clone);

        // Different bodies should not be equal
        assert_ne!(body1, body2);
    }

    #[test]
    fn test_rigid_body2d_display() {
        let mut body = RigidBody2D::new("TestBody");
        body.set_mode(RigidBodyMode::Dynamic);
        body.set_mass(5.0);
        body.set_linear_velocity(Vector2::new(30.0, 40.0)); // Length = 50.0
        body.set_sleeping(false);

        let display_str = format!("{}", body);
        assert!(display_str.contains("TestBody"));
        assert!(display_str.contains("Dynamic"));
        assert!(display_str.contains("mass: 5.00"));
        assert!(display_str.contains("vel: 50.0"));
        assert!(display_str.contains("sleeping: false"));
    }

    #[test]
    fn test_rigid_body2d_complex_configuration() {
        let mut body = RigidBody2D::new("ComplexBody");

        // Configure all properties
        body.set_mode(RigidBodyMode::Dynamic);
        body.set_mass(8.0);
        body.set_linear_velocity(Vector2::new(150.0, -100.0));
        body.set_angular_velocity(3.14);
        body.set_gravity_scale(1.5);
        body.set_linear_damp(0.3);
        body.set_angular_damp(0.8);
        body.set_can_sleep(true);
        body.base_mut().set_position(Vector2::new(500.0, 600.0));

        // Apply some forces
        body.apply_central_force(Vector2::new(200.0, 0.0));
        body.apply_torque(25.0);

        // Verify all properties
        assert_eq!(body.get_mode(), RigidBodyMode::Dynamic);
        assert_eq!(body.get_mass(), 8.0);
        assert_eq!(body.get_linear_velocity(), Vector2::new(150.0, -100.0));
        assert_eq!(body.get_angular_velocity(), 3.14);
        assert_eq!(body.get_gravity_scale(), 1.5);
        assert_eq!(body.get_linear_damp(), 0.3);
        assert_eq!(body.get_angular_damp(), 0.8);
        assert!(body.is_able_to_sleep());
        assert!(!body.is_sleeping()); // Should be awake due to applied forces
        assert_eq!(body.base().get_position(), Vector2::new(500.0, 600.0));
    }
}
