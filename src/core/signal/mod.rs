//! Comprehensive signal system for Godot-compatible node communication.
//!
//! This module provides a complete signal/slot implementation that matches Godot's
//! signal system, enabling type-safe node communication, event handling, and
//! decoupled architecture patterns. It supports signal emission, connection
//! management, and automatic cleanup.

pub mod signal;
pub mod signal_manager;
pub mod callable;

// Re-export main types - Complete API for external users
#[allow(unused_imports)]
pub use signal::{Signal, SignalId, SignalData};
#[allow(unused_imports)]
pub use signal_manager::{SignalManager, ConnectionFlags};
#[allow(unused_imports)]
pub use callable::Callable;
