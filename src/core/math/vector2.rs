//! Comprehensive 2D vector implementation with mathematical operations.
//!
//! This module provides a complete 2D vector implementation with all standard
//! mathematical operations, interpolation methods, transformations, and utility functions
//! commonly used in computer graphics, physics simulations, and geometric calculations.

use std::fmt;
use super::fast_inv_sqrt::fast_inv_sqrt;

/// ### A 2D vector using floating-point coordinates.
///
/// This struct represents a point or direction in 2D space with `x` and `y` components.
/// It provides comprehensive mathematical operations including arithmetic, dot/cross products,
/// normalization, interpolation, transformations, and various utility functions for
/// geometric calculations and vector analysis.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct Vector2 {
    /// The vector's X component (horizontal coordinate).
    pub x: f32,
    /// The vector's Y component (vertical coordinate).
    pub y: f32,
}

/// ### Enumeration representing the coordinate axes in 2D space.
///
/// Used for indexing vector components and specifying which axis to operate on
/// in various vector operations and transformations.
///
/// - **X**: Horizontal Axis in 2D space (Index 0).
/// - **Y**: Vertical Axis in 2D space (Index 1).
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Eq)]
#[allow(dead_code)] // Complete API for vector component access
pub enum Axis {
    X = 0,
    Y = 1,
}

impl Vector2 {
    /// ### Zero vector constant -> Vector2(0.0, 0.0).
    ///
    /// Represents the additive identity for vector operations and the origin point in 2D space.
    /// This vector has zero magnitude and no defined direction.
    pub const ZERO: Vector2 = Vector2 { x: 0.0, y: 0.0 };

    /// ### Unit vector constant -> Vector2(1.0, 1.0).
    ///
    /// A vector with both components set to 1.0, commonly used for uniform scaling
    /// and as a reference vector. Has magnitude √2 ≈ 1.414.
    pub const ONE: Vector2 = Vector2 { x: 1.0, y: 1.0 };

    /// ### Infinity vector constant -> Vector2(∞, ∞).
    ///
    /// A vector with both components set to positive infinity. Useful for representing
    /// unbounded values and in certain mathematical operations requiring infinite limits.
    pub const INF: Vector2 = Vector2 { x: f32::INFINITY, y: f32::INFINITY };

    /// ### Left-pointing unit vector constant -> Vector2(-1.0, 0.0).
    ///
    /// Points in the negative `x` direction with unit magnitude. Represents the standard
    /// leftward direction in a right-handed coordinate system.
    pub const LEFT: Vector2 = Vector2 { x: -1.0, y: 0.0 };

    /// ### Right-pointing unit vector constant -> Vector2(1.0, 0.0).
    ///
    /// Points in the positive `x` direction with unit magnitude. Represents the standard
    /// rightward direction and is the basis vector for the X-axis.
    pub const RIGHT: Vector2 = Vector2 { x: 1.0, y: 0.0 };

    /// ### Upward-pointing unit vector constant -> Vector2(0.0, -1.0).
    ///
    /// Points in the negative `y` direction with unit magnitude. In screen coordinates
    /// where `y` increases downward, this represents the upward direction.
    pub const UP: Vector2 = Vector2 { x: 0.0, y: -1.0 };

    /// ### Downward-pointing unit vector constant -> Vector2(0.0, 1.0).
    ///
    /// Points in the positive `y` direction with unit magnitude. In screen coordinates
    /// where `y` increases downward, this represents the downward direction.
    pub const DOWN: Vector2 = Vector2 { x: 0.0, y: 1.0 };

    /// ### Creates a new Vector2 with the specified `x` and `y` components.
    ///
    /// This is the primary constructor for creating vectors with explicit coordinate values.
    /// Both components can be any finite floating-point value, including negative numbers,
    /// zero, and positive numbers.
    ///
    /// # Arguments
    /// * `x` - The horizontal component (X-coordinate)
    /// * `y` - The vertical component (Y-coordinate)
    ///
    /// # Returns
    /// A new Vector2 instance with the specified components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(3.0, 4.0);
    /// assert_eq!(v.x, 3.0);
    /// assert_eq!(v.y, 4.0);
    ///
    /// // Negative components are allowed
    /// let v2 = Vector2::new(-1.5, 2.7);
    /// assert_eq!(v2.x, -1.5);
    /// assert_eq!(v2.y, 2.7);
    /// ```
    #[inline]
    pub const fn new(x: f32, y: f32) -> Self {
        Vector2 { x, y }
    }

    /// ### Calculates the length (magnitude) of the vector.
    ///
    /// Uses the Euclidean distance formula: **√(x² + y²)**
    ///
    /// This method uses the default fast inverse square root for optimal accuracy.
    ///
    /// # Returns
    /// The length of the vector as a positive floating-point number.
    /// Returns 0.0 for the zero vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(3.0, 4.0);
    /// assert!((v.length() - 5.0).abs() < 0.001); // 3-4-5 triangle
    ///
    /// let zero = Vector2::ZERO;
    /// assert_eq!(zero.length(), 0.0);
    /// ```
    #[inline]
    pub fn length(self) -> f32 {
        let length_squared = self.x * self.x + self.y * self.y;
        if length_squared == 0.0 {
            0.0
        } else {
            1.0 / fast_inv_sqrt(length_squared)
        }
    }

    /// ### Calculates the squared length of the vector.
    ///
    /// This is more efficient than `length()` as it avoids the square root calculation.
    /// Useful for distance comparisons where the actual distance value isn't needed.
    ///
    /// # Returns
    /// The squared length of the vector **(x² + y²)**.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(3.0, 4.0);
    /// assert_eq!(v.length_squared(), 25.0); // 3² + 4² = 9 + 16 = 25
    /// ```
    #[inline]
    pub fn length_squared(self) -> f32 {
        self.x * self.x + self.y * self.y
    }

    /// ### Returns a normalized version of the vector (unit vector).
    ///
    /// A normalized vector has the same direction but a length of 1.0.
    /// Uses the default Quake III fast inverse square root for optimal accuracy.
    ///
    /// # Returns
    /// A new Vector2 with the same direction but unit length.
    /// Returns the zero vector if the original vector has zero length.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(3.0, 4.0);
    /// let normalized = v.normalized();
    /// assert!((normalized.length() - 1.0).abs() < 0.001);
    ///
    /// let zero = Vector2::ZERO;
    /// assert_eq!(zero.normalized(), Vector2::ZERO);
    /// ```
    #[inline]
    pub fn normalized(self) -> Vector2 {
        let length_squared = self.x * self.x + self.y * self.y;
        if length_squared == 0.0 {
            Vector2::ZERO
        } else {
            let inv_length = fast_inv_sqrt(length_squared);
            Vector2::new(self.x * inv_length, self.y * inv_length)
        }
    }

    /// ### Returns the distance to another vector.
    ///
    /// Calculates the Euclidean distance between two points in 2D space.
    /// This is equivalent to (other - self).length().
    ///
    /// # Arguments
    /// * `other` - The other vector to calculate distance to
    ///
    /// # Returns
    /// The distance between the vectors as a floating-point number.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let pos1 = Vector2::new(0.0, 0.0);
    /// let pos2 = Vector2::new(3.0, 4.0);
    /// assert!((pos1.distance_to(pos2) - 5.0).abs() < 0.001);
    ///
    /// // Distance is symmetric
    /// assert_eq!(pos1.distance_to(pos2), pos2.distance_to(pos1));
    /// ```
    #[inline]
    pub fn distance_to(self, other: Vector2) -> f32 {
        (other - self).length()
    }

    /// ### Returns the squared distance to another vector.
    ///
    /// More efficient than distance_to() as it avoids the square root calculation.
    /// Useful for distance comparisons and nearest neighbor searches.
    ///
    /// # Arguments
    /// * `other` - The other vector to calculate squared distance to
    ///
    /// # Returns
    /// The squared distance between the vectors.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let pos1 = Vector2::new(0.0, 0.0);
    /// let pos2 = Vector2::new(3.0, 4.0);
    /// assert_eq!(pos1.distance_squared_to(pos2), 25.0);
    /// ```
    #[inline]
    pub fn distance_squared_to(self, other: Vector2) -> f32 {
        (other - self).length_squared()
    }

    /// ### Calculates the dot product with another vector.
    ///
    /// The dot product is a scalar value that represents the cosine of the angle
    /// between two vectors multiplied by their magnitudes.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the dot product with
    ///
    /// # Returns
    /// The dot product as a floating-point number.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v1 = Vector2::new(1.0, 0.0);
    /// let v2 = Vector2::new(0.0, 1.0);
    /// assert_eq!(v1.dot(v2), 0.0); // Perpendicular vectors
    ///
    /// let v3 = Vector2::new(3.0, 4.0);
    /// let v4 = Vector2::new(1.0, 0.0);
    /// assert_eq!(v3.dot(v4), 3.0);
    /// ```
    #[inline]
    pub fn dot(self, other: Vector2) -> f32 {
        self.x * other.x + self.y * other.y
    }

    /// ### Calculates the cross product with another vector.
    ///
    /// In 2D, the cross product returns a scalar representing the Z component
    /// of the 3D cross product if the vectors were extended to 3D space.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the cross product with
    ///
    /// # Returns
    /// The cross product as a floating-point number.
    /// Positive values indicate counter-clockwise rotation from self to other.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v1 = Vector2::new(1.0, 0.0);
    /// let v2 = Vector2::new(0.0, 1.0);
    /// assert_eq!(v1.cross(v2), 1.0); // 90° counter-clockwise
    /// ```
    #[inline]
    pub fn cross(self, other: Vector2) -> f32 {
        self.x * other.y - self.y * other.x
    }
    /// ### Returns the angle of the vector in radians.
    ///
    /// The angle is measured from the positive `x` axis in a counter-clockwise direction.
    /// Returns values in the range `(-π, π)`.
    ///
    /// # Returns
    /// The angle of the vector in radians, or 0.0 for the zero vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let right = Vector2::new(1.0, 0.0);
    /// assert!((right.angle() - 0.0).abs() < 0.001);
    ///
    /// let up = Vector2::new(0.0, 1.0);
    /// assert!((up.angle() - std::f32::consts::FRAC_PI_2).abs() < 0.001);
    /// ```
    #[inline]
    pub fn angle(self) -> f32 {
        self.y.atan2(self.x)
    }

    /// ### Returns the angle between this vector and another vector in radians.
    ///
    /// The returned angle is always positive and in the range `(0, π)`.
    /// Returns 0.0 if either vector is zero.
    ///
    /// # Arguments
    /// * `other` - The other vector to measure the angle to
    ///
    /// # Returns
    /// The angle between the vectors in radians.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v1 = Vector2::new(1.0, 0.0);
    /// let v2 = Vector2::new(0.0, 1.0);
    /// assert!((v1.angle_to(v2) - std::f32::consts::FRAC_PI_2).abs() < 0.001);
    /// ```
    #[inline]
    pub fn angle_to(self, other: Vector2) -> f32 {
        let self_len_sq = self.length_squared();
        let other_len_sq = other.length_squared();

        if self_len_sq == 0.0 || other_len_sq == 0.0 {
            return 0.0;
        }

        let dot = self.dot(other);
        let len_product = (self_len_sq * other_len_sq).sqrt();
        let cos_angle = (dot / len_product).clamp(-1.0, 1.0);
        cos_angle.acos()
    }

    /// ### Returns a copy of the vector rotated by the given angle in radians.
    ///
    /// Positive angles rotate counter-clockwise, negative angles rotate clockwise.
    ///
    /// # Arguments
    /// * `angle` - The angle to rotate by in radians
    ///
    /// # Returns
    /// A new Vector2 rotated by the specified angle.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(1.0, 0.0);
    /// let rotated = v.rotated(std::f32::consts::FRAC_PI_2);
    /// assert!((rotated.x - 0.0).abs() < 0.001);
    /// assert!((rotated.y - 1.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn rotated(self, angle: f32) -> Vector2 {
        let cos_a = angle.cos();
        let sin_a = angle.sin();
        Vector2::new(
            self.x * cos_a - self.y * sin_a,
            self.x * sin_a + self.y * cos_a,
        )
    }

    /// ### Returns a perpendicular vector (rotated 90 degrees counter-clockwise).
    ///
    /// This is equivalent to rotating the vector by π/2 radians or swapping
    /// components and negating the new x component: `(-y, x)`.
    ///
    /// # Returns
    /// A new Vector2 perpendicular to this vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(3.0, 4.0);
    /// let perp = v.orthogonal();
    /// assert_eq!(perp, Vector2::new(-4.0, 3.0));
    /// assert!((v.dot(perp)).abs() < 0.001); // Perpendicular vectors have zero dot product
    /// ```
    #[inline]
    pub fn orthogonal(self) -> Vector2 {
        Vector2::new(-self.y, self.x)
    }

    /// ### Projects this vector onto another vector.
    ///
    /// Returns the component of this vector that lies in the direction of the target vector.
    /// If the target vector is zero, returns the zero vector.
    ///
    /// # Arguments
    /// * `onto` - The vector to project onto
    ///
    /// # Returns
    /// The projection of this vector onto the target vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(3.0, 4.0);
    /// let onto = Vector2::new(1.0, 0.0);
    /// let projected = v.project(onto);
    /// assert_eq!(projected, Vector2::new(3.0, 0.0));
    /// ```
    #[inline]
    pub fn project(self, onto: Vector2) -> Vector2 {
        let onto_len_sq = onto.length_squared();
        if onto_len_sq == 0.0 {
            return Vector2::ZERO;
        }

        let dot = self.dot(onto);
        let scale = dot / onto_len_sq;
        Vector2::new(onto.x * scale, onto.y * scale)
    }

    /// ### Reflects this vector off a surface with the given normal vector.
    ///
    /// The normal should be a unit vector pointing away from the surface.
    /// If the normal is not normalized, the result may be incorrect.
    ///
    /// # Arguments
    /// * `normal` - The surface normal vector (should be normalized)
    ///
    /// # Returns
    /// The reflected vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let incident = Vector2::new(1.0, -1.0);
    /// let normal = Vector2::new(0.0, 1.0);
    /// let reflected = incident.reflect(normal);
    /// assert!((reflected - Vector2::new(1.0, 1.0)).length() < 0.001);
    /// ```
    #[inline]
    pub fn reflect(self, normal: Vector2) -> Vector2 {
        let dot = self.dot(normal);
        Vector2::new(
            self.x - 2.0 * dot * normal.x,
            self.y - 2.0 * dot * normal.y,
        )
    }
    /// ### Linear interpolation between this vector and another vector.
    ///
    /// Returns a vector that is linearly interpolated between this vector and the target vector
    /// based on the weight parameter. When weight is 0.0, returns this vector. When weight is 1.0,
    /// returns the target vector.
    ///
    /// # Arguments
    /// * `to` - The target vector to interpolate towards
    /// * `weight` - The interpolation weight (typically between 0.0 and 1.0)
    ///
    /// # Returns
    /// The interpolated vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let from = Vector2::new(0.0, 0.0);
    /// let to = Vector2::new(10.0, 20.0);
    /// let mid = from.lerp(to, 0.5);
    /// assert_eq!(mid, Vector2::new(5.0, 10.0));
    /// ```
    #[inline]
    pub fn lerp(self, to: Vector2, weight: f32) -> Vector2 {
        Vector2::new(
            self.x + (to.x - self.x) * weight,
            self.y + (to.y - self.y) * weight,
        )
    }

    /// ### Spherical linear interpolation between this vector and another vector.
    ///
    /// Performs spherical linear interpolation, which maintains constant angular velocity
    /// and is useful for rotating vectors smoothly. If either vector is zero or the vectors
    /// are parallel, falls back to linear interpolation.
    ///
    /// # Arguments
    /// * `to` - The target vector to interpolate towards
    /// * `weight` - The interpolation weight (typically between 0.0 and 1.0)
    ///
    /// # Returns
    /// The spherically interpolated vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let from = Vector2::new(1.0, 0.0);
    /// let to = Vector2::new(0.0, 1.0);
    /// let mid = from.slerp(to, 0.5);
    /// // Result should be approximately (0.707, 0.707) - normalized diagonal
    /// assert!((mid.length() - 1.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn slerp(self, to: Vector2, weight: f32) -> Vector2 {
        let from_len_sq = self.length_squared();
        let to_len_sq = to.length_squared();

        // Handle zero vectors or very small vectors
        if from_len_sq < 1e-10 || to_len_sq < 1e-10 {
            return self.lerp(to, weight);
        }

        let from_len = from_len_sq.sqrt();
        let to_len = to_len_sq.sqrt();

        let from_normalized = Vector2::new(self.x / from_len, self.y / from_len);
        let to_normalized = Vector2::new(to.x / to_len, to.y / to_len);

        let dot = from_normalized.dot(to_normalized).clamp(-1.0, 1.0);

        // If vectors are nearly parallel, use linear interpolation
        if (dot.abs() - 1.0).abs() < 1e-6 {
            return self.lerp(to, weight);
        }

        let angle = dot.acos();
        let sin_angle = angle.sin();

        if sin_angle.abs() < 1e-6 {
            return self.lerp(to, weight);
        }

        let from_weight = ((1.0 - weight) * angle).sin() / sin_angle;
        let to_weight = (weight * angle).sin() / sin_angle;

        let result_normalized = Vector2::new(
            from_normalized.x * from_weight + to_normalized.x * to_weight,
            from_normalized.y * from_weight + to_normalized.y * to_weight,
        );

        // Interpolate the length as well
        let result_len = from_len + (to_len - from_len) * weight;
        Vector2::new(result_normalized.x * result_len, result_normalized.y * result_len)
    }

    /// ### Move this vector toward a target vector by a maximum distance.
    ///
    /// Moves this vector toward the target vector by at most the specified delta distance.
    /// If the distance to the target is less than delta, returns the target vector.
    ///
    /// # Arguments
    /// * `to` - The target vector to move towards
    /// * `delta` - The maximum distance to move
    ///
    /// # Returns
    /// The vector moved toward the target.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let from = Vector2::new(0.0, 0.0);
    /// let to = Vector2::new(10.0, 0.0);
    /// let moved = from.move_toward(to, 3.0);
    /// assert!((moved - Vector2::new(3.0, 0.0)).length() < 0.001);
    ///
    /// // If delta is larger than distance, returns target
    /// let moved_far = from.move_toward(to, 15.0);
    /// assert_eq!(moved_far, to);
    /// ```
    #[inline]
    pub fn move_toward(self, to: Vector2, delta: f32) -> Vector2 {
        let diff = Vector2::new(to.x - self.x, to.y - self.y);
        let distance = diff.length();

        if distance <= delta || distance == 0.0 {
            to
        } else {
            let direction = Vector2::new(diff.x / distance, diff.y / distance);
            Vector2::new(
                self.x + direction.x * delta,
                self.y + direction.y * delta,
            )
        }
    }

    /// ### Returns a vector with the absolute values of the components.
    ///
    /// Creates a new vector where both `x` and `y` components are their absolute values.
    /// This is useful for distance calculations and ensuring positive coordinates.
    ///
    /// # Returns
    /// A new Vector2 with absolute values of both components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(-3.0, -4.0);
    /// let abs_v = v.abs();
    /// assert_eq!(abs_v, Vector2::new(3.0, 4.0));
    /// ```
    #[inline]
    pub fn abs(self) -> Vector2 {
        Vector2::new(self.x.abs(), self.y.abs())
    }

    /// ### Clamps the vector components between minimum and maximum vectors.
    ///
    /// Each component is clamped independently between the corresponding components
    /// of the min and max vectors.
    ///
    /// # Arguments
    /// * `min` - The minimum vector (component-wise lower bounds)
    /// * `max` - The maximum vector (component-wise upper bounds)
    ///
    /// # Returns
    /// A new Vector2 with components clamped between min and max.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(-5.0, 15.0);
    /// let min = Vector2::new(-2.0, 0.0);
    /// let max = Vector2::new(10.0, 10.0);
    /// let clamped = v.clamp(min, max);
    /// assert_eq!(clamped, Vector2::new(-2.0, 10.0));
    /// ```
    #[inline]
    pub fn clamp(self, min: Vector2, max: Vector2) -> Vector2 {
        Vector2::new(
            self.x.clamp(min.x, max.x),
            self.y.clamp(min.y, max.y),
        )
    }

    /// ### Returns the component-wise minimum of this vector and another vector.
    ///
    /// Creates a new vector where each component is the minimum of the corresponding
    /// components from both vectors.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// A new Vector2 with the minimum components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v1 = Vector2::new(1.0, 5.0);
    /// let v2 = Vector2::new(3.0, 2.0);
    /// let min_v = v1.min(v2);
    /// assert_eq!(min_v, Vector2::new(1.0, 2.0));
    /// ```
    #[inline]
    pub fn min(self, other: Vector2) -> Vector2 {
        Vector2::new(
            self.x.min(other.x),
            self.y.min(other.y),
        )
    }

    /// ### Returns the component-wise maximum of this vector and another vector.
    ///
    /// Creates a new vector where each component is the maximum of the corresponding
    /// components from both vectors.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// A new Vector2 with the maximum components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v1 = Vector2::new(1.0, 5.0);
    /// let v2 = Vector2::new(3.0, 2.0);
    /// let max_v = v1.max(v2);
    /// assert_eq!(max_v, Vector2::new(3.0, 5.0));
    /// ```
    #[inline]
    pub fn max(self, other: Vector2) -> Vector2 {
        Vector2::new(
            self.x.max(other.x),
            self.y.max(other.y),
        )
    }

    /// ### Snaps the vector components to a grid defined by the step vector.
    ///
    /// Each component is rounded to the nearest multiple of the corresponding
    /// component in the step vector.
    ///
    /// # Arguments
    /// * `step` - The step vector defining the grid spacing
    ///
    /// # Returns
    /// A new Vector2 with components snapped to the grid.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v = Vector2::new(3.7, 8.2);
    /// let step = Vector2::new(2.0, 5.0);
    /// let snapped = v.snapped(step);
    /// assert_eq!(snapped, Vector2::new(4.0, 10.0));
    /// ```
    #[inline]
    pub fn snapped(self, step: Vector2) -> Vector2 {
        Vector2::new(
            if step.x != 0.0 { (self.x / step.x).round() * step.x } else { self.x },
            if step.y != 0.0 { (self.y / step.y).round() * step.y } else { self.y },
        )
    }
    /// ### Checks if this vector is approximately equal to another vector.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// Two vectors are considered approximately equal if the distance between them
    /// is less than the epsilon threshold.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// `true` if the vectors are approximately equal, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let v1 = Vector2::new(1.0, 2.0);
    /// let v2 = Vector2::new(1.0000001, 2.0000001);
    /// assert!(v1.is_equal_approx(v2));
    ///
    /// let v3 = Vector2::new(1.1, 2.0);
    /// assert!(!v1.is_equal_approx(v3));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Vector2) -> bool {
        const EPSILON: f32 = 1e-5;
        (self.x - other.x).abs() < EPSILON && (self.y - other.y).abs() < EPSILON
    }

    /// ### Checks if the vector is normalized (has a length of approximately 1.0).
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// A vector is considered normalized if its length is within epsilon of 1.0.
    ///
    /// # Returns
    /// `true` if the vector is approximately normalized, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let normalized = Vector2::new(1.0, 0.0);
    /// assert!(normalized.is_normalized());
    ///
    /// let not_normalized = Vector2::new(2.0, 0.0);
    /// assert!(!not_normalized.is_normalized());
    ///
    /// let almost_normalized = Vector2::new(0.7071, 0.7071); // Approximately sqrt(2)/2
    /// assert!(almost_normalized.is_normalized());
    /// ```
    #[inline]
    pub fn is_normalized(self) -> bool {
        const EPSILON: f32 = 1e-4;
        (self.length_squared() - 1.0).abs() < EPSILON
    }

    /// ### Checks if all components of the vector are finite numbers.
    ///
    /// Returns `false` if any component is infinite or NaN.
    ///
    /// # Returns
    /// `true` if both x and y components are finite, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let finite = Vector2::new(1.0, 2.0);
    /// assert!(finite.is_finite());
    ///
    /// let infinite = Vector2::new(f32::INFINITY, 2.0);
    /// assert!(!infinite.is_finite());
    ///
    /// let nan = Vector2::new(f32::NAN, 2.0);
    /// assert!(!nan.is_finite());
    /// ```
    #[inline]
    pub fn is_finite(self) -> bool {
        self.x.is_finite() && self.y.is_finite()
    }

    /// ### Checks if the vector is approximately zero.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// A vector is considered approximately zero if both components are within
    /// epsilon of zero.
    ///
    /// # Returns
    /// `true` if the vector is approximately zero, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2;
    /// let zero = Vector2::ZERO;
    /// assert!(zero.is_zero_approx());
    ///
    /// let almost_zero = Vector2::new(1e-6, -1e-6);
    /// assert!(almost_zero.is_zero_approx());
    ///
    /// let not_zero = Vector2::new(0.1, 0.0);
    /// assert!(!not_zero.is_zero_approx());
    /// ```
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        const EPSILON: f32 = 1e-5;
        self.x.abs() < EPSILON && self.y.abs() < EPSILON
    }

    // ============================================================================
    // CONVERSION METHODS
    // ============================================================================

    /// ### Converts this floating-point vector to an integer Vector2i.
    ///
    /// Each component is converted using Rust's default float-to-integer conversion,
    /// which truncates towards zero. This is useful for converting screen coordinates
    /// to pixel positions or continuous positions to grid coordinates.
    ///
    /// # Returns
    /// A new Vector2i with components converted to integers.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector2, Vector2i};
    /// let float_vec = Vector2::new(3.7, -2.9);
    /// let int_vec = float_vec.to_vector2i();
    /// assert_eq!(int_vec, Vector2i::new(3, -2)); // Truncated towards zero
    ///
    /// // Useful for screen coordinate conversion
    /// let screen_pos = Vector2::new(123.8, 456.2);
    /// let pixel_pos = screen_pos.to_vector2i();
    /// assert_eq!(pixel_pos, Vector2i::new(123, 456));
    /// ```
    #[inline]
    pub fn to_vector2i(self) -> super::Vector2i {
        super::Vector2i::new(self.x as i32, self.y as i32)
    }

    /// ### Converts this 2D vector to a 3D Vector3 by adding a z component.
    ///
    /// This is useful for extruding 2D shapes into 3D space, converting UI coordinates
    /// to 3D world coordinates, or adding depth information to 2D positions.
    ///
    /// # Arguments
    /// * `z` - The z component to add to create the 3D vector
    ///
    /// # Returns
    /// A new Vector3 with x and y from this vector and the specified z component.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector2, Vector3};
    /// let pos_2d = Vector2::new(10.0, 20.0);
    /// let pos_3d = pos_2d.to_vector3(5.0);
    /// assert_eq!(pos_3d, Vector3::new(10.0, 20.0, 5.0));
    ///
    /// // Useful for UI to world coordinate conversion
    /// let ui_pos = Vector2::new(100.0, 200.0);
    /// let world_pos = ui_pos.to_vector3(0.0); // Place on XY plane
    /// assert_eq!(world_pos, Vector3::new(100.0, 200.0, 0.0));
    /// ```
    #[inline]
    pub fn to_vector3(self, z: f32) -> super::Vector3 {
        super::Vector3::new(self.x, self.y, z)
    }

    /// ### Converts this 2D vector to a 4D Vector4 by adding z and w components.
    ///
    /// This is useful for creating homogeneous coordinates, color values with alpha,
    /// or extending 2D data to 4D space for advanced mathematical operations.
    ///
    /// # Arguments
    /// * `z` - The z component to add
    /// * `w` - The w component to add
    ///
    /// # Returns
    /// A new Vector4 with x and y from this vector and the specified z and w components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector2, Vector4};
    /// let pos_2d = Vector2::new(10.0, 20.0);
    /// let homogeneous = pos_2d.to_vector4(0.0, 1.0);
    /// assert_eq!(homogeneous, Vector4::new(10.0, 20.0, 0.0, 1.0));
    ///
    /// // Useful for color creation (RG + BA)
    /// let rg = Vector2::new(0.8, 0.4);
    /// let rgba = rg.to_vector4(0.2, 1.0); // Add blue and alpha
    /// assert_eq!(rgba, Vector4::new(0.8, 0.4, 0.2, 1.0));
    /// ```
    #[inline]
    pub fn to_vector4(self, z: f32, w: f32) -> super::Vector4 {
        super::Vector4::new(self.x, self.y, z, w)
    }
}

impl fmt::Display for Vector2 {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {})", self.x, self.y)
    }
}

// ============================================================================
// ARITHMETIC OPERATORS
// ============================================================================

impl std::ops::Add for Vector2 {
    type Output = Self;

    #[inline]
    fn add(self, other: Self) -> Self {
        Self::new(self.x + other.x, self.y + other.y)
    }
}

impl std::ops::Sub for Vector2 {
    type Output = Self;

    #[inline]
    fn sub(self, other: Self) -> Self {
        Self::new(self.x - other.x, self.y - other.y)
    }
}

impl std::ops::Mul<f32> for Vector2 {
    type Output = Self;

    #[inline]
    fn mul(self, scalar: f32) -> Self {
        Self::new(self.x * scalar, self.y * scalar)
    }
}

impl std::ops::Mul<Vector2> for f32 {
    type Output = Vector2;

    #[inline]
    fn mul(self, vector: Vector2) -> Vector2 {
        Vector2::new(self * vector.x, self * vector.y)
    }
}

impl std::ops::Div<f32> for Vector2 {
    type Output = Self;

    #[inline]
    fn div(self, scalar: f32) -> Self {
        Self::new(self.x / scalar, self.y / scalar)
    }
}

impl std::ops::Neg for Vector2 {
    type Output = Self;

    #[inline]
    fn neg(self) -> Self {
        Self::new(-self.x, -self.y)
    }
}

impl std::ops::AddAssign for Vector2 {
    #[inline]
    fn add_assign(&mut self, other: Self) {
        self.x += other.x;
        self.y += other.y;
    }
}

impl std::ops::SubAssign for Vector2 {
    #[inline]
    fn sub_assign(&mut self, other: Self) {
        self.x -= other.x;
        self.y -= other.y;
    }
}

impl std::ops::MulAssign<f32> for Vector2 {
    #[inline]
    fn mul_assign(&mut self, scalar: f32) {
        self.x *= scalar;
        self.y *= scalar;
    }
}

impl std::ops::DivAssign<f32> for Vector2 {
    #[inline]
    fn div_assign(&mut self, scalar: f32) {
        self.x /= scalar;
        self.y /= scalar;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vector2_creation() {
        let v = Vector2::new(3.0, 4.0);
        assert_eq!(v.x, 3.0);
        assert_eq!(v.y, 4.0);
    }

    #[test]
    fn test_vector2_constants() {
        assert_eq!(Vector2::ZERO, Vector2::new(0.0, 0.0));
        assert_eq!(Vector2::ONE, Vector2::new(1.0, 1.0));
        assert_eq!(Vector2::LEFT, Vector2::new(-1.0, 0.0));
        assert_eq!(Vector2::RIGHT, Vector2::new(1.0, 0.0));
        assert_eq!(Vector2::UP, Vector2::new(0.0, -1.0));
        assert_eq!(Vector2::DOWN, Vector2::new(0.0, 1.0));
    }

    #[test]
    fn test_vector2_length() {
        let v = Vector2::new(3.0, 4.0);
        assert!((v.length() - 5.0).abs() < 0.001);
        assert_eq!(v.length_squared(), 25.0);

        let zero = Vector2::ZERO;
        assert_eq!(zero.length(), 0.0);
        assert_eq!(zero.length_squared(), 0.0);
    }

    #[test]
    fn test_vector2_normalization() {
        let v = Vector2::new(3.0, 4.0);
        let normalized = v.normalized();
        assert!((normalized.length() - 1.0).abs() < 0.001);
        assert!(normalized.is_normalized());

        // Zero vector normalization should return zero
        let zero_normalized = Vector2::ZERO.normalized();
        assert_eq!(zero_normalized, Vector2::ZERO);
    }

    #[test]
    fn test_vector2_dot_product() {
        let v1 = Vector2::new(1.0, 2.0);
        let v2 = Vector2::new(3.0, 4.0);
        assert_eq!(v1.dot(v2), 11.0); // 1*3 + 2*4 = 11

        // Perpendicular vectors
        let right = Vector2::RIGHT;
        let up = Vector2::UP;
        assert_eq!(right.dot(up), 0.0);
    }

    #[test]
    fn test_vector2_cross_product() {
        let v1 = Vector2::new(1.0, 0.0);
        let v2 = Vector2::new(0.0, 1.0);
        assert_eq!(v1.cross(v2), 1.0);

        let v3 = Vector2::new(2.0, 1.0);
        let v4 = Vector2::new(1.0, 3.0);
        assert_eq!(v3.cross(v4), 5.0); // 2*3 - 1*1 = 5
    }

    #[test]
    fn test_vector2_angle() {
        let right = Vector2::RIGHT;
        assert!((right.angle() - 0.0).abs() < 0.001);

        let up = Vector2::UP;
        assert!((up.angle() - (-std::f32::consts::FRAC_PI_2)).abs() < 0.001);

        let v1 = Vector2::new(1.0, 0.0);
        let v2 = Vector2::new(0.0, 1.0);
        assert!((v1.angle_to(v2) - std::f32::consts::FRAC_PI_2).abs() < 0.001);
    }

    #[test]
    fn test_vector2_rotation() {
        let v = Vector2::new(1.0, 0.0);
        let rotated = v.rotated(std::f32::consts::FRAC_PI_2);
        assert!((rotated.x - 0.0).abs() < 0.001);
        assert!((rotated.y - 1.0).abs() < 0.001);
    }

    #[test]
    fn test_vector2_projection() {
        let v1 = Vector2::new(3.0, 4.0);
        let v2 = Vector2::new(1.0, 0.0);
        let projected = v1.project(v2);
        assert_eq!(projected, Vector2::new(3.0, 0.0));
    }

    #[test]
    fn test_vector2_reflection() {
        let v = Vector2::new(1.0, -1.0);
        let normal = Vector2::new(0.0, 1.0);
        let reflected = v.reflect(normal);
        assert_eq!(reflected, Vector2::new(1.0, 1.0));
    }

    #[test]
    fn test_vector2_interpolation() {
        let from = Vector2::new(0.0, 0.0);
        let to = Vector2::new(10.0, 20.0);

        let lerped = from.lerp(to, 0.5);
        assert_eq!(lerped, Vector2::new(5.0, 10.0));

        let moved = from.move_toward(to, 5.0);
        assert!((moved.length() - 5.0).abs() < 0.001);
    }

    #[test]
    fn test_vector2_utility_functions() {
        let v = Vector2::new(-3.0, -4.0);
        assert_eq!(v.abs(), Vector2::new(3.0, 4.0));

        let v1 = Vector2::new(1.0, 5.0);
        let v2 = Vector2::new(3.0, 2.0);
        assert_eq!(v1.min(v2), Vector2::new(1.0, 2.0));
        assert_eq!(v1.max(v2), Vector2::new(3.0, 5.0));

        let clamped = Vector2::new(-5.0, 15.0).clamp(Vector2::new(-2.0, 0.0), Vector2::new(10.0, 10.0));
        assert_eq!(clamped, Vector2::new(-2.0, 10.0));

        let snapped = Vector2::new(3.7, 8.2).snapped(Vector2::new(2.0, 5.0));
        assert_eq!(snapped, Vector2::new(4.0, 10.0));
    }

    #[test]
    fn test_vector2_boolean_checks() {
        let v1 = Vector2::new(1.0, 2.0);
        let v2 = Vector2::new(1.0000001, 2.0000001);
        assert!(v1.is_equal_approx(v2));

        let normalized = Vector2::new(1.0, 0.0);
        assert!(normalized.is_normalized());

        let finite = Vector2::new(1.0, 2.0);
        assert!(finite.is_finite());

        let infinite = Vector2::new(f32::INFINITY, 2.0);
        assert!(!infinite.is_finite());

        assert!(Vector2::ZERO.is_zero_approx());
        assert!(!Vector2::ONE.is_zero_approx());
    }

    #[test]
    fn test_vector2_conversion_methods() {
        let float_vec = Vector2::new(3.7, -2.9);
        let int_vec = float_vec.to_vector2i();
        assert_eq!(int_vec, super::super::Vector2i::new(3, -2));

        let vec_3d = float_vec.to_vector3(5.0);
        assert_eq!(vec_3d, super::super::Vector3::new(3.7, -2.9, 5.0));

        let vec_4d = float_vec.to_vector4(5.0, 1.0);
        assert_eq!(vec_4d, super::super::Vector4::new(3.7, -2.9, 5.0, 1.0));
    }

    #[test]
    fn test_vector2_edge_cases() {
        // Very small values
        let small = Vector2::new(1e-10, 1e-10);
        assert!(small.is_zero_approx());

        // Very large values
        let large = Vector2::new(1e10, 1e10);
        assert!(large.is_finite());

        // NaN handling
        let nan_vec = Vector2::new(f32::NAN, 1.0);
        assert!(!nan_vec.is_finite());
        assert!(!nan_vec.is_normalized());
    }
}