// Vector modules
pub mod vector2;
pub mod vector2i;
pub mod vector3;
pub mod vector3i;
pub mod vector4;
pub mod vector4i;

// Fast inverse square root module
pub mod fast_inv_sqrt;

// Transformation modules
pub mod transform2d;
pub mod quaternion;
pub mod basis;
pub mod plane;
pub mod aabb;
pub mod projection;

// Rectangle modules
pub mod rect2;
pub mod rect2i;

// Re-export vector types - Complete API for external users
#[allow(unused_imports)]
pub use vector2::Vector2;
#[allow(unused_imports)]
pub use vector2i::Vector2i;
#[allow(unused_imports)]
pub use vector3::Vector3;
#[allow(unused_imports)]
pub use vector3i::Vector3i;
#[allow(unused_imports)]
pub use vector4::Vector4;
#[allow(unused_imports)]
pub use vector4i::Vector4i;

// Re-export transformation types - Complete API for external users
#[allow(unused_imports)]
pub use transform2d::Transform2D;
#[allow(unused_imports)]
pub use quaternion::Quaternion;
#[allow(unused_imports)]
pub use basis::Basis;
#[allow(unused_imports)]
pub use plane::Plane;
#[allow(unused_imports)]
pub use aabb::AABB;
#[allow(unused_imports)]
pub use projection::Projection;

// Re-export rectangle types - Complete API for external users
#[allow(unused_imports)]
pub use rect2::Rect2;
#[allow(unused_imports)]
pub use rect2i::Rect2i;
