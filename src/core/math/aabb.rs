//! Comprehensive 3D Axis-Aligned Bounding Box implementation for collision detection and spatial queries.
//!
//! This module provides a complete AABB implementation representing 3D axis-aligned bounding boxes
//! used for collision detection, spatial partitioning, frustum culling, and geometric queries
//! commonly used in 3D graphics, physics engines, and game development.

use std::fmt;
use super::{Vector3, Plane};

/// ### A 3D Axis-Aligned Bounding Box for collision detection and spatial queries.
///
/// AABB represents a 3D rectangular box aligned with the coordinate axes, defined by
/// a position (minimum corner) and size (width, height, depth). This structure is
/// fundamental for 3D collision detection, spatial partitioning, and geometric queries.
///
/// ## Mathematical Representation
///
/// An AABB is defined by:
/// - **Position**: (x, y, z) - minimum corner coordinates
/// - **Size**: (width, height, depth) - dimensions along each axis
/// - **Bounds**: [min_x, max_x] × [min_y, max_y] × [min_z, max_z]
///
/// ## Use Cases
///
/// AABB is ideal for:
/// - **Collision Detection**: Fast broad-phase collision detection
/// - **Spatial Partitioning**: Octrees, BSP trees, spatial hashing
/// - **Frustum Culling**: Camera view frustum intersection testing
/// - **Physics**: Bounding volume hierarchies, collision shapes
/// - **Level Design**: Room boundaries, trigger volumes, object bounds
/// - **Optimization**: Fast rejection tests, spatial queries
///
/// # Examples
/// ```
/// # use verturion::core::math::{AABB, Vector3};
/// // Create a bounding box at origin with size 10x20x30
/// let bbox = AABB::new(0.0, 0.0, 0.0, 10.0, 20.0, 30.0);
///
/// // Create from center and size
/// let center = Vector3::new(5.0, 10.0, 15.0);
/// let size = Vector3::new(10.0, 20.0, 30.0);
/// let centered_bbox = AABB::from_center_size(center, size);
///
/// // Test point containment
/// let point = Vector3::new(3.0, 5.0, 8.0);
/// let contains = bbox.contains_point(point);
///
/// // Test AABB intersection
/// let other = AABB::new(5.0, 5.0, 5.0, 10.0, 10.0, 10.0);
/// let intersects = bbox.intersects(other);
/// ```
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct AABB {
    /// The position of the minimum corner (bottom-left-back)
    pub position: Vector3,
    /// The size (width, height, depth) of the bounding box
    pub size: Vector3,
}

impl AABB {
    /// ### Creates a new AABB with the specified position and size.
    ///
    /// # Parameters
    /// - `x`: X coordinate of the minimum corner
    /// - `y`: Y coordinate of the minimum corner
    /// - `z`: Z coordinate of the minimum corner
    /// - `width`: Width (size along X axis)
    /// - `height`: Height (size along Y axis)
    /// - `depth`: Depth (size along Z axis)
    ///
    /// # Returns
    /// A new AABB with the given position and size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::AABB;
    /// let bbox = AABB::new(10.0, 20.0, 30.0, 100.0, 200.0, 300.0);
    /// ```
    #[inline]
    pub const fn new(x: f32, y: f32, z: f32, width: f32, height: f32, depth: f32) -> Self {
        Self {
            position: Vector3::new(x, y, z),
            size: Vector3::new(width, height, depth),
        }
    }

    /// ### Creates an AABB from position and size vectors.
    ///
    /// # Parameters
    /// - `position`: The minimum corner position
    /// - `size`: The size (width, height, depth)
    ///
    /// # Returns
    /// A new AABB with the given position and size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let pos = Vector3::new(10.0, 20.0, 30.0);
    /// let size = Vector3::new(100.0, 200.0, 300.0);
    /// let bbox = AABB::from_position_size(pos, size);
    /// ```
    #[inline]
    pub const fn from_position_size(position: Vector3, size: Vector3) -> Self {
        Self { position, size }
    }

    /// ### Creates an AABB from center point and size.
    ///
    /// # Parameters
    /// - `center`: The center point of the bounding box
    /// - `size`: The size (width, height, depth)
    ///
    /// # Returns
    /// A new AABB centered at the given point with the specified size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let center = Vector3::new(50.0, 100.0, 150.0);
    /// let size = Vector3::new(20.0, 40.0, 60.0);
    /// let bbox = AABB::from_center_size(center, size);
    /// ```
    #[inline]
    pub fn from_center_size(center: Vector3, size: Vector3) -> Self {
        let half_size = size * 0.5;
        Self {
            position: center - half_size,
            size,
        }
    }

    /// ### Creates an AABB from two corner points.
    ///
    /// Automatically determines the correct position and size from any two
    /// corner points, handling cases where points are not min and max corners.
    ///
    /// # Parameters
    /// - `point1`: First corner point
    /// - `point2`: Second corner point (diagonal to first)
    ///
    /// # Returns
    /// A new AABB encompassing both points.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::from_corners(
    ///     Vector3::new(100.0, 200.0, 300.0),  // max corner
    ///     Vector3::new(10.0, 20.0, 30.0)      // min corner
    /// );
    /// ```
    #[inline]
    pub fn from_corners(point1: Vector3, point2: Vector3) -> Self {
        let min_corner = Vector3::new(
            point1.x.min(point2.x),
            point1.y.min(point2.y),
            point1.z.min(point2.z),
        );
        let max_corner = Vector3::new(
            point1.x.max(point2.x),
            point1.y.max(point2.y),
            point1.z.max(point2.z),
        );
        Self {
            position: min_corner,
            size: max_corner - min_corner,
        }
    }

    /// ### Returns the center point of the AABB.
    ///
    /// # Returns
    /// The center point of the bounding box.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 20.0, 30.0, 20.0, 40.0, 60.0);
    /// let center = bbox.get_center();
    /// assert_eq!(center, Vector3::new(20.0, 40.0, 60.0));
    /// ```
    #[inline]
    pub fn get_center(self) -> Vector3 {
        self.position + self.size * 0.5
    }

    /// ### Returns the maximum corner point (position + size).
    ///
    /// # Returns
    /// The maximum corner coordinates.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 20.0, 30.0, 20.0, 40.0, 60.0);
    /// let max_corner = bbox.get_end();
    /// assert_eq!(max_corner, Vector3::new(30.0, 60.0, 90.0));
    /// ```
    #[inline]
    pub fn get_end(self) -> Vector3 {
        self.position + self.size
    }

    /// ### Returns the volume of the AABB.
    ///
    /// # Returns
    /// The volume (width × height × depth) of the bounding box.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::AABB;
    /// let bbox = AABB::new(0.0, 0.0, 0.0, 10.0, 20.0, 30.0);
    /// let volume = bbox.get_volume();
    /// assert_eq!(volume, 6000.0); // 10 × 20 × 30
    /// ```
    #[inline]
    pub fn get_volume(self) -> f32 {
        self.size.x * self.size.y * self.size.z
    }

    /// ### Returns the surface area of the AABB.
    ///
    /// # Returns
    /// The total surface area of all six faces.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::AABB;
    /// let bbox = AABB::new(0.0, 0.0, 0.0, 10.0, 20.0, 30.0);
    /// let area = bbox.get_surface_area();
    /// assert_eq!(area, 2200.0); // 2 × (10×20 + 20×30 + 30×10)
    /// ```
    #[inline]
    pub fn get_surface_area(self) -> f32 {
        let w = self.size.x;
        let h = self.size.y;
        let d = self.size.z;
        2.0 * (w * h + h * d + d * w)
    }

    /// ### Checks if a point is contained within the AABB.
    ///
    /// # Parameters
    /// - `point`: The point to test
    ///
    /// # Returns
    /// `true` if the point is inside or on the boundary of the AABB.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 20.0, 30.0, 20.0, 40.0, 60.0);
    /// let point = Vector3::new(15.0, 25.0, 35.0);
    /// assert!(bbox.contains_point(point));
    /// ```
    #[inline]
    pub fn contains_point(self, point: Vector3) -> bool {
        let end = self.get_end();
        point.x >= self.position.x && point.x <= end.x
            && point.y >= self.position.y && point.y <= end.y
            && point.z >= self.position.z && point.z <= end.z
    }

    /// ### Checks if this AABB completely contains another AABB.
    ///
    /// # Parameters
    /// - `other`: The other AABB to test
    ///
    /// # Returns
    /// `true` if the other AABB is completely contained within this one.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let large = AABB::new(0.0, 0.0, 0.0, 100.0, 100.0, 100.0);
    /// let small = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
    /// assert!(large.contains_aabb(small));
    /// ```
    #[inline]
    pub fn contains_aabb(self, other: Self) -> bool {
        let self_end = self.get_end();
        let other_end = other.get_end();

        other.position.x >= self.position.x && other_end.x <= self_end.x
            && other.position.y >= self.position.y && other_end.y <= self_end.y
            && other.position.z >= self.position.z && other_end.z <= self_end.z
    }

    /// ### Checks if this AABB intersects with another AABB.
    ///
    /// # Parameters
    /// - `other`: The other AABB to test intersection with
    ///
    /// # Returns
    /// `true` if the AABBs intersect or touch.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::AABB;
    /// let bbox1 = AABB::new(0.0, 0.0, 0.0, 10.0, 10.0, 10.0);
    /// let bbox2 = AABB::new(5.0, 5.0, 5.0, 10.0, 10.0, 10.0);
    /// assert!(bbox1.intersects(bbox2));
    /// ```
    #[inline]
    pub fn intersects(self, other: Self) -> bool {
        let self_end = self.get_end();
        let other_end = other.get_end();

        !(self_end.x < other.position.x || self.position.x > other_end.x
            || self_end.y < other.position.y || self.position.y > other_end.y
            || self_end.z < other.position.z || self.position.z > other_end.z)
    }

    /// ### Computes the intersection of this AABB with another AABB.
    ///
    /// # Parameters
    /// - `other`: The other AABB to intersect with
    ///
    /// # Returns
    /// The intersection AABB, or None if they don't intersect.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox1 = AABB::new(0.0, 0.0, 0.0, 10.0, 10.0, 10.0);
    /// let bbox2 = AABB::new(5.0, 5.0, 5.0, 10.0, 10.0, 10.0);
    /// let intersection = bbox1.intersection(bbox2).unwrap();
    /// assert_eq!(intersection.position, Vector3::new(5.0, 5.0, 5.0));
    /// assert_eq!(intersection.size, Vector3::new(5.0, 5.0, 5.0));
    /// ```
    #[inline]
    pub fn intersection(self, other: Self) -> Option<Self> {
        if !self.intersects(other) {
            return None;
        }

        let self_end = self.get_end();
        let other_end = other.get_end();

        let min_corner = Vector3::new(
            self.position.x.max(other.position.x),
            self.position.y.max(other.position.y),
            self.position.z.max(other.position.z),
        );
        let max_corner = Vector3::new(
            self_end.x.min(other_end.x),
            self_end.y.min(other_end.y),
            self_end.z.min(other_end.z),
        );

        Some(Self::from_corners(min_corner, max_corner))
    }

    /// ### Computes the union of this AABB with another AABB.
    ///
    /// Creates the smallest AABB that contains both AABBs.
    ///
    /// # Parameters
    /// - `other`: The other AABB to union with
    ///
    /// # Returns
    /// The union AABB containing both AABBs.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox1 = AABB::new(0.0, 0.0, 0.0, 10.0, 10.0, 10.0);
    /// let bbox2 = AABB::new(5.0, 5.0, 5.0, 10.0, 10.0, 10.0);
    /// let union = bbox1.union(bbox2);
    /// assert_eq!(union.position, Vector3::new(0.0, 0.0, 0.0));
    /// assert_eq!(union.size, Vector3::new(15.0, 15.0, 15.0));
    /// ```
    #[inline]
    pub fn union(self, other: Self) -> Self {
        let self_end = self.get_end();
        let other_end = other.get_end();

        let min_corner = Vector3::new(
            self.position.x.min(other.position.x),
            self.position.y.min(other.position.y),
            self.position.z.min(other.position.z),
        );
        let max_corner = Vector3::new(
            self_end.x.max(other_end.x),
            self_end.y.max(other_end.y),
            self_end.z.max(other_end.z),
        );

        Self::from_corners(min_corner, max_corner)
    }

    /// ### Expands the AABB to include a point.
    ///
    /// # Parameters
    /// - `point`: The point to include
    ///
    /// # Returns
    /// A new AABB that includes the original AABB and the point.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
    /// let expanded = bbox.expand_to_point(Vector3::new(5.0, 35.0, 15.0));
    /// assert_eq!(expanded.position, Vector3::new(5.0, 10.0, 10.0));
    /// ```
    #[inline]
    pub fn expand_to_point(self, point: Vector3) -> Self {
        let end = self.get_end();
        let min_corner = Vector3::new(
            self.position.x.min(point.x),
            self.position.y.min(point.y),
            self.position.z.min(point.z),
        );
        let max_corner = Vector3::new(
            end.x.max(point.x),
            end.y.max(point.y),
            end.z.max(point.z),
        );
        Self::from_corners(min_corner, max_corner)
    }

    /// ### Expands the AABB by the specified amount in all directions.
    ///
    /// # Parameters
    /// - `amount`: The amount to expand by (can be negative to shrink)
    ///
    /// # Returns
    /// A new AABB expanded by the given amount.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
    /// let expanded = bbox.expand(5.0);
    /// assert_eq!(expanded.position, Vector3::new(5.0, 5.0, 5.0));
    /// assert_eq!(expanded.size, Vector3::new(30.0, 30.0, 30.0));
    /// ```
    #[inline]
    pub fn expand(self, amount: f32) -> Self {
        Self {
            position: self.position - Vector3::new(amount, amount, amount),
            size: self.size + Vector3::new(amount * 2.0, amount * 2.0, amount * 2.0),
        }
    }

    /// ### Expands the AABB by different amounts along each axis.
    ///
    /// # Parameters
    /// - `x`: Amount to expand along X axis
    /// - `y`: Amount to expand along Y axis
    /// - `z`: Amount to expand along Z axis
    ///
    /// # Returns
    /// A new AABB expanded by the given amounts.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
    /// let expanded = bbox.expand_individual(2.0, 3.0, 4.0);
    /// assert_eq!(expanded.position, Vector3::new(8.0, 7.0, 6.0));
    /// assert_eq!(expanded.size, Vector3::new(24.0, 26.0, 28.0));
    /// ```
    #[inline]
    pub fn expand_individual(self, x: f32, y: f32, z: f32) -> Self {
        Self {
            position: self.position - Vector3::new(x, y, z),
            size: self.size + Vector3::new(x * 2.0, y * 2.0, z * 2.0),
        }
    }

    /// ### Returns the closest point on the AABB to a given point.
    ///
    /// # Parameters
    /// - `point`: The point to find the closest point to
    ///
    /// # Returns
    /// The closest point on the AABB surface or interior.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
    /// let point = Vector3::new(5.0, 15.0, 25.0);
    /// let closest = bbox.get_closest_point(point);
    /// assert_eq!(closest, Vector3::new(10.0, 15.0, 25.0));
    /// ```
    #[inline]
    pub fn get_closest_point(self, point: Vector3) -> Vector3 {
        let end = self.get_end();
        Vector3::new(
            point.x.clamp(self.position.x, end.x),
            point.y.clamp(self.position.y, end.y),
            point.z.clamp(self.position.z, end.z),
        )
    }

    /// ### Calculates the distance from a point to the AABB.
    ///
    /// # Parameters
    /// - `point`: The point to calculate distance to
    ///
    /// # Returns
    /// The distance from the point to the closest point on the AABB (0 if inside).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
    /// let point = Vector3::new(5.0, 15.0, 15.0);
    /// let distance = bbox.distance_to_point(point);
    /// assert!((distance - 5.0).abs() < 0.001); // Distance to X face
    /// ```
    #[inline]
    pub fn distance_to_point(self, point: Vector3) -> f32 {
        let closest = self.get_closest_point(point);
        point.distance_to(closest)
    }

    /// ### Checks if a ray intersects with the AABB.
    ///
    /// # Parameters
    /// - `ray_origin`: The origin point of the ray
    /// - `ray_direction`: The direction vector of the ray (should be normalized)
    ///
    /// # Returns
    /// The intersection distance along the ray, or None if no intersection.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
    /// let ray_origin = Vector3::new(0.0, 15.0, 15.0);
    /// let ray_direction = Vector3::RIGHT;
    /// let distance = bbox.intersect_ray(ray_origin, ray_direction);
    /// assert_eq!(distance, Some(10.0));
    /// ```
    #[inline]
    pub fn intersect_ray(self, ray_origin: Vector3, ray_direction: Vector3) -> Option<f32> {
        let end = self.get_end();

        // Calculate intersection distances for each axis
        let inv_dir = Vector3::new(
            if ray_direction.x.abs() < f32::EPSILON { f32::INFINITY } else { 1.0 / ray_direction.x },
            if ray_direction.y.abs() < f32::EPSILON { f32::INFINITY } else { 1.0 / ray_direction.y },
            if ray_direction.z.abs() < f32::EPSILON { f32::INFINITY } else { 1.0 / ray_direction.z },
        );

        let t1 = (self.position.x - ray_origin.x) * inv_dir.x;
        let t2 = (end.x - ray_origin.x) * inv_dir.x;
        let t3 = (self.position.y - ray_origin.y) * inv_dir.y;
        let t4 = (end.y - ray_origin.y) * inv_dir.y;
        let t5 = (self.position.z - ray_origin.z) * inv_dir.z;
        let t6 = (end.z - ray_origin.z) * inv_dir.z;

        let tmin = t1.min(t2).max(t3.min(t4)).max(t5.min(t6));
        let tmax = t1.max(t2).min(t3.max(t4)).min(t5.max(t6));

        // Ray intersects if tmax >= 0 and tmin <= tmax
        if tmax >= 0.0 && tmin <= tmax {
            Some(if tmin >= 0.0 { tmin } else { tmax })
        } else {
            None
        }
    }

    /// ### Checks if the AABB intersects with a plane.
    ///
    /// # Parameters
    /// - `plane`: The plane to test intersection with
    ///
    /// # Returns
    /// `true` if the AABB intersects or touches the plane.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Plane, Vector3};
    /// let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
    /// let plane = Plane::new(Vector3::UP, -15.0); // Horizontal plane at y=15
    /// assert!(bbox.intersects_plane(plane));
    /// ```
    #[inline]
    pub fn intersects_plane(self, plane: Plane) -> bool {
        // Calculate the projection interval radius
        let radius = (self.size.x * plane.normal.x.abs()
                    + self.size.y * plane.normal.y.abs()
                    + self.size.z * plane.normal.z.abs()) * 0.5;

        // Calculate distance from center to plane
        let center = self.get_center();
        let distance = plane.distance_to(center).abs();

        distance <= radius
    }

    /// ### Returns the absolute AABB (ensures positive size).
    ///
    /// Adjusts position and size to ensure the size is positive.
    ///
    /// # Returns
    /// An AABB with positive size.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{AABB, Vector3};
    /// let bbox = AABB::new(10.0, 10.0, 10.0, -5.0, -3.0, -2.0);
    /// let abs_bbox = bbox.abs();
    /// assert_eq!(abs_bbox.position, Vector3::new(5.0, 7.0, 8.0));
    /// assert_eq!(abs_bbox.size, Vector3::new(5.0, 3.0, 2.0));
    /// ```
    #[inline]
    pub fn abs(self) -> Self {
        let mut result = self;
        if result.size.x < 0.0 {
            result.position.x += result.size.x;
            result.size.x = -result.size.x;
        }
        if result.size.y < 0.0 {
            result.position.y += result.size.y;
            result.size.y = -result.size.y;
        }
        if result.size.z < 0.0 {
            result.position.z += result.size.z;
            result.size.z = -result.size.z;
        }
        result
    }

    /// ### Checks if the AABB has a valid (positive) size.
    ///
    /// # Returns
    /// `true` if all size components are positive.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::AABB;
    /// let valid = AABB::new(0.0, 0.0, 0.0, 10.0, 20.0, 30.0);
    /// assert!(valid.has_valid_size());
    ///
    /// let invalid = AABB::new(0.0, 0.0, 0.0, -10.0, 20.0, 30.0);
    /// assert!(!invalid.has_valid_size());
    /// ```
    #[inline]
    pub fn has_valid_size(self) -> bool {
        self.size.x > 0.0 && self.size.y > 0.0 && self.size.z > 0.0
    }

    /// ### Checks if the AABB is approximately equal to another.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    ///
    /// # Parameters
    /// - `other`: The other AABB to compare with
    ///
    /// # Returns
    /// `true` if the AABBs are approximately equal.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::AABB;
    /// let bbox1 = AABB::new(10.0, 20.0, 30.0, 100.0, 200.0, 300.0);
    /// let bbox2 = AABB::new(10.0, 20.0, 30.0, 100.0, 200.0, 300.0);
    /// assert!(bbox1.is_equal_approx(bbox2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Self) -> bool {
        self.position.is_equal_approx(other.position) && self.size.is_equal_approx(other.size)
    }
}

impl Default for AABB {
    /// Returns an AABB at the origin with zero size.
    #[inline]
    fn default() -> Self {
        Self::new(0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
    }
}

impl fmt::Display for AABB {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "AABB(pos: {}, size: {})", self.position, self.size)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_aabb_creation() {
        let bbox = AABB::new(10.0, 20.0, 30.0, 100.0, 200.0, 300.0);
        assert_eq!(bbox.position, Vector3::new(10.0, 20.0, 30.0));
        assert_eq!(bbox.size, Vector3::new(100.0, 200.0, 300.0));

        let from_vectors = AABB::from_position_size(
            Vector3::new(10.0, 20.0, 30.0),
            Vector3::new(100.0, 200.0, 300.0)
        );
        assert_eq!(from_vectors, bbox);

        let default = AABB::default();
        assert_eq!(default.position, Vector3::ZERO);
        assert_eq!(default.size, Vector3::ZERO);
    }

    #[test]
    fn test_aabb_from_center_size() {
        let center = Vector3::new(50.0, 100.0, 150.0);
        let size = Vector3::new(20.0, 40.0, 60.0);
        let bbox = AABB::from_center_size(center, size);

        assert_eq!(bbox.position, Vector3::new(40.0, 80.0, 120.0));
        assert_eq!(bbox.size, size);
        assert_eq!(bbox.get_center(), center);
    }

    #[test]
    fn test_aabb_from_corners() {
        let corner1 = Vector3::new(100.0, 200.0, 300.0);
        let corner2 = Vector3::new(10.0, 20.0, 30.0);
        let bbox = AABB::from_corners(corner1, corner2);

        assert_eq!(bbox.position, Vector3::new(10.0, 20.0, 30.0));
        assert_eq!(bbox.size, Vector3::new(90.0, 180.0, 270.0));
        assert_eq!(bbox.get_end(), Vector3::new(100.0, 200.0, 300.0));
    }

    #[test]
    fn test_aabb_properties() {
        let bbox = AABB::new(10.0, 20.0, 30.0, 20.0, 40.0, 60.0);

        assert_eq!(bbox.get_center(), Vector3::new(20.0, 40.0, 60.0));
        assert_eq!(bbox.get_end(), Vector3::new(30.0, 60.0, 90.0));
        assert_eq!(bbox.get_volume(), 48000.0); // 20 × 40 × 60
        assert_eq!(bbox.get_surface_area(), 8800.0); // 2 × (20×40 + 40×60 + 60×20) = 2 × (800 + 2400 + 1200) = 2 × 4400 = 8800
    }

    #[test]
    fn test_aabb_contains_point() {
        let bbox = AABB::new(10.0, 20.0, 30.0, 20.0, 40.0, 60.0);

        // Points inside
        assert!(bbox.contains_point(Vector3::new(15.0, 25.0, 35.0)));
        assert!(bbox.contains_point(Vector3::new(20.0, 40.0, 60.0))); // Center

        // Points on boundary
        assert!(bbox.contains_point(Vector3::new(10.0, 20.0, 30.0))); // Min corner
        assert!(bbox.contains_point(Vector3::new(30.0, 60.0, 90.0))); // Max corner

        // Points outside
        assert!(!bbox.contains_point(Vector3::new(5.0, 25.0, 35.0)));
        assert!(!bbox.contains_point(Vector3::new(35.0, 25.0, 35.0)));
        assert!(!bbox.contains_point(Vector3::new(15.0, 15.0, 35.0)));
        assert!(!bbox.contains_point(Vector3::new(15.0, 65.0, 35.0)));
        assert!(!bbox.contains_point(Vector3::new(15.0, 25.0, 25.0)));
        assert!(!bbox.contains_point(Vector3::new(15.0, 25.0, 95.0)));
    }

    #[test]
    fn test_aabb_contains_aabb() {
        let large = AABB::new(0.0, 0.0, 0.0, 100.0, 100.0, 100.0);
        let small = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);
        let overlapping = AABB::new(50.0, 50.0, 50.0, 100.0, 100.0, 100.0);
        let outside = AABB::new(200.0, 200.0, 200.0, 10.0, 10.0, 10.0);

        assert!(large.contains_aabb(small));
        assert!(!large.contains_aabb(overlapping));
        assert!(!large.contains_aabb(outside));
        assert!(!small.contains_aabb(large));
    }

    #[test]
    fn test_aabb_intersects() {
        let bbox1 = AABB::new(0.0, 0.0, 0.0, 10.0, 10.0, 10.0);
        let bbox2 = AABB::new(5.0, 5.0, 5.0, 10.0, 10.0, 10.0); // Overlapping
        let bbox3 = AABB::new(10.0, 10.0, 10.0, 10.0, 10.0, 10.0); // Touching
        let bbox4 = AABB::new(20.0, 20.0, 20.0, 10.0, 10.0, 10.0); // Separate

        assert!(bbox1.intersects(bbox2));
        assert!(bbox1.intersects(bbox3));
        assert!(!bbox1.intersects(bbox4));

        // Test symmetry
        assert!(bbox2.intersects(bbox1));
        assert!(bbox3.intersects(bbox1));
        assert!(!bbox4.intersects(bbox1));
    }

    #[test]
    fn test_aabb_intersection() {
        let bbox1 = AABB::new(0.0, 0.0, 0.0, 10.0, 10.0, 10.0);
        let bbox2 = AABB::new(5.0, 5.0, 5.0, 10.0, 10.0, 10.0);
        let bbox3 = AABB::new(20.0, 20.0, 20.0, 10.0, 10.0, 10.0);

        let intersection = bbox1.intersection(bbox2).unwrap();
        assert_eq!(intersection.position, Vector3::new(5.0, 5.0, 5.0));
        assert_eq!(intersection.size, Vector3::new(5.0, 5.0, 5.0));

        let no_intersection = bbox1.intersection(bbox3);
        assert!(no_intersection.is_none());
    }

    #[test]
    fn test_aabb_union() {
        let bbox1 = AABB::new(0.0, 0.0, 0.0, 10.0, 10.0, 10.0);
        let bbox2 = AABB::new(5.0, 5.0, 5.0, 10.0, 10.0, 10.0);

        let union = bbox1.union(bbox2);
        assert_eq!(union.position, Vector3::new(0.0, 0.0, 0.0));
        assert_eq!(union.size, Vector3::new(15.0, 15.0, 15.0));
    }

    #[test]
    fn test_aabb_expand_operations() {
        let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);

        // Expand to point
        let expanded = bbox.expand_to_point(Vector3::new(5.0, 35.0, 15.0));
        assert_eq!(expanded.position, Vector3::new(5.0, 10.0, 10.0));
        assert_eq!(expanded.get_end(), Vector3::new(30.0, 35.0, 30.0));

        // Uniform expansion
        let uniform_expanded = bbox.expand(5.0);
        assert_eq!(uniform_expanded.position, Vector3::new(5.0, 5.0, 5.0));
        assert_eq!(uniform_expanded.size, Vector3::new(30.0, 30.0, 30.0));

        // Individual expansion
        let individual_expanded = bbox.expand_individual(2.0, 3.0, 4.0);
        assert_eq!(individual_expanded.position, Vector3::new(8.0, 7.0, 6.0));
        assert_eq!(individual_expanded.size, Vector3::new(24.0, 26.0, 28.0));
    }

    #[test]
    fn test_aabb_closest_point_and_distance() {
        let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);

        // Point inside (should return the point itself)
        let inside_point = Vector3::new(15.0, 15.0, 15.0);
        let closest_inside = bbox.get_closest_point(inside_point);
        assert_eq!(closest_inside, inside_point);
        assert_eq!(bbox.distance_to_point(inside_point), 0.0);

        // Point outside on X axis
        let outside_x = Vector3::new(5.0, 15.0, 15.0);
        let closest_x = bbox.get_closest_point(outside_x);
        assert_eq!(closest_x, Vector3::new(10.0, 15.0, 15.0));
        assert!((bbox.distance_to_point(outside_x) - 5.0).abs() < 0.001);

        // Point outside on corner
        let outside_corner = Vector3::new(5.0, 5.0, 5.0);
        let closest_corner = bbox.get_closest_point(outside_corner);
        assert_eq!(closest_corner, Vector3::new(10.0, 10.0, 10.0));
        let expected_distance = (Vector3::new(5.0, 5.0, 5.0)).length();
        assert!((bbox.distance_to_point(outside_corner) - expected_distance).abs() < 0.001);
    }

    #[test]
    fn test_aabb_ray_intersection() {
        let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);

        // Ray hitting from outside
        let ray_origin = Vector3::new(0.0, 15.0, 15.0);
        let ray_direction = Vector3::RIGHT;
        let distance = bbox.intersect_ray(ray_origin, ray_direction);
        assert_eq!(distance, Some(10.0));

        // Ray starting inside
        let inside_origin = Vector3::new(15.0, 15.0, 15.0);
        let inside_distance = bbox.intersect_ray(inside_origin, ray_direction);
        assert_eq!(inside_distance, Some(15.0)); // Distance to exit

        // Ray missing the box
        let miss_origin = Vector3::new(0.0, 5.0, 15.0);
        let miss_distance = bbox.intersect_ray(miss_origin, ray_direction);
        assert!(miss_distance.is_none());

        // Ray pointing away
        let away_origin = Vector3::new(0.0, 15.0, 15.0);
        let away_direction = Vector3::LEFT;
        let away_distance = bbox.intersect_ray(away_origin, away_direction);
        assert!(away_distance.is_none());
    }

    #[test]
    fn test_aabb_plane_intersection() {
        let bbox = AABB::new(10.0, 10.0, 10.0, 20.0, 20.0, 20.0);

        // Plane cutting through the middle
        let cutting_plane = Plane::new(Vector3::UP, -20.0); // y = 20
        assert!(bbox.intersects_plane(cutting_plane));

        // Plane touching the edge
        let touching_plane = Plane::new(Vector3::UP, -10.0); // y = 10
        assert!(bbox.intersects_plane(touching_plane));

        // Plane completely outside
        let outside_plane = Plane::new(Vector3::UP, -5.0); // y = 5
        assert!(!bbox.intersects_plane(outside_plane));

        // Plane on the other side
        let far_plane = Plane::new(Vector3::UP, -35.0); // y = 35
        assert!(!bbox.intersects_plane(far_plane));
    }

    #[test]
    fn test_aabb_abs() {
        // Normal AABB (no change needed)
        let normal = AABB::new(10.0, 20.0, 30.0, 5.0, 10.0, 15.0);
        let abs_normal = normal.abs();
        assert_eq!(abs_normal, normal);

        // AABB with negative size
        let negative = AABB::new(10.0, 20.0, 30.0, -5.0, -10.0, -15.0);
        let abs_negative = negative.abs();
        assert_eq!(abs_negative.position, Vector3::new(5.0, 10.0, 15.0));
        assert_eq!(abs_negative.size, Vector3::new(5.0, 10.0, 15.0));

        // Mixed positive and negative
        let mixed = AABB::new(10.0, 20.0, 30.0, 5.0, -10.0, 15.0);
        let abs_mixed = mixed.abs();
        assert_eq!(abs_mixed.position, Vector3::new(10.0, 10.0, 30.0));
        assert_eq!(abs_mixed.size, Vector3::new(5.0, 10.0, 15.0));
    }

    #[test]
    fn test_aabb_validity_checks() {
        let valid = AABB::new(0.0, 0.0, 0.0, 10.0, 20.0, 30.0);
        assert!(valid.has_valid_size());

        let invalid_x = AABB::new(0.0, 0.0, 0.0, -10.0, 20.0, 30.0);
        assert!(!invalid_x.has_valid_size());

        let invalid_y = AABB::new(0.0, 0.0, 0.0, 10.0, -20.0, 30.0);
        assert!(!invalid_y.has_valid_size());

        let invalid_z = AABB::new(0.0, 0.0, 0.0, 10.0, 20.0, -30.0);
        assert!(!invalid_z.has_valid_size());

        let zero_size = AABB::new(0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
        assert!(!zero_size.has_valid_size());
    }

    #[test]
    fn test_aabb_equality_checks() {
        let bbox1 = AABB::new(10.0, 20.0, 30.0, 100.0, 200.0, 300.0);
        let bbox2 = AABB::new(10.0, 20.0, 30.0, 100.0, 200.0, 300.0);
        assert!(bbox1.is_equal_approx(bbox2));

        let bbox3 = AABB::new(10.001, 20.0, 30.0, 100.0, 200.0, 300.0);
        assert!(!bbox1.is_equal_approx(bbox3));
    }

    #[test]
    fn test_aabb_display() {
        let bbox = AABB::new(10.0, 20.0, 30.0, 100.0, 200.0, 300.0);
        let display_str = format!("{}", bbox);
        assert!(display_str.contains("AABB"));
        assert!(display_str.contains("pos"));
        assert!(display_str.contains("size"));
        assert!(display_str.contains("10"));
        assert!(display_str.contains("100"));
    }

    #[test]
    fn test_aabb_edge_cases() {
        // Zero-size AABB
        let zero = AABB::new(10.0, 10.0, 10.0, 0.0, 0.0, 0.0);
        assert_eq!(zero.get_volume(), 0.0);
        assert_eq!(zero.get_surface_area(), 0.0);
        assert!(zero.contains_point(Vector3::new(10.0, 10.0, 10.0)));
        assert!(!zero.contains_point(Vector3::new(10.1, 10.0, 10.0)));

        // Very large AABB
        let large = AABB::new(-1e6, -1e6, -1e6, 2e6, 2e6, 2e6);
        assert!(large.contains_point(Vector3::ZERO));
        assert!(large.has_valid_size());

        // Very small AABB
        let small = AABB::new(0.0, 0.0, 0.0, 1e-6, 1e-6, 1e-6);
        assert!(small.has_valid_size());
        assert!((small.get_volume() - 1e-18).abs() < 1e-20);
    }
}
