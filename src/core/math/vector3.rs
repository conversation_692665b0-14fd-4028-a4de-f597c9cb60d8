//! Comprehensive 3D floating-point vector implementation for 3D graphics and physics.
//!
//! This module provides a complete 3D vector implementation optimized for 3D graphics,
//! physics simulations, spatial calculations, and game development. It includes all
//! essential 3D mathematical operations with performance optimizations.

use std::fmt;
use super::Vector2;
use super::fast_inv_sqrt::fast_inv_sqrt;

/// ### A 3D vector using floating-point coordinates.
///
/// This struct represents a point or direction in 3D space with floating-point x, y, and z components.
/// It provides comprehensive 3D mathematical operations optimized for graphics programming,
/// physics simulations, and spatial calculations commonly used in 3D game development.
///
/// ## Use Cases
///
/// Vector3 is ideal for:
/// - **3D Graphics**: Vertex positions, surface normals, lighting calculations
/// - **Physics Simulations**: Velocity, acceleration, force vectors
/// - **Spatial Mathematics**: 3D transformations, rotations, projections
/// - **Game Development**: Player positions, object orientations, camera calculations
/// - **Scientific Computing**: 3D data analysis, spatial algorithms
///
/// ## Performance Features
///
/// - **Fast Inverse Square Root**: Uses optimized Quake III algorithm for normalization
/// - **SIMD-Friendly**: Structure layout optimized for vectorization
/// - **Inline Methods**: Performance-critical operations marked with #[inline]
/// - **Memory Efficient**: Compact 12-byte representation (3 × f32)
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct Vector3 {
    /// The vector's X component (horizontal coordinate).
    pub x: f32,
    /// The vector's Y component (vertical coordinate).
    pub y: f32,
    /// The vector's Z component (depth coordinate).
    pub z: f32,
}

impl Vector3 {
    /// ### Zero vector constant -> Vector3(0.0, 0.0, 0.0).
    ///
    /// Represents the additive identity for vector operations and the origin point in 3D space.
    /// This vector has zero magnitude and no defined direction.
    pub const ZERO: Vector3 = Vector3 { x: 0.0, y: 0.0, z: 0.0 };

    /// ### Unit vector constant -> Vector3(1.0, 1.0, 1.0).
    ///
    /// A vector with all components set to 1.0, commonly used for uniform scaling
    /// and as a reference vector. Has magnitude √3 ≈ 1.732.
    pub const ONE: Vector3 = Vector3 { x: 1.0, y: 1.0, z: 1.0 };

    /// ### Left-pointing unit vector constant -> Vector3(-1.0, 0.0, 0.0).
    ///
    /// Points in the negative X direction with unit magnitude. Represents the standard
    /// leftward direction in a right-handed coordinate system.
    pub const LEFT: Vector3 = Vector3 { x: -1.0, y: 0.0, z: 0.0 };

    /// ### Right-pointing unit vector constant -> Vector3(1.0, 0.0, 0.0).
    ///
    /// Points in the positive X direction with unit magnitude. Represents the standard
    /// rightward direction and is the basis vector for the X-axis.
    pub const RIGHT: Vector3 = Vector3 { x: 1.0, y: 0.0, z: 0.0 };

    /// ### Upward-pointing unit vector constant -> Vector3(0.0, 1.0, 0.0).
    ///
    /// Points in the positive Y direction with unit magnitude. In most 3D coordinate systems,
    /// this represents the upward direction and is the basis vector for the Y-axis.
    pub const UP: Vector3 = Vector3 { x: 0.0, y: 1.0, z: 0.0 };

    /// ### Downward-pointing unit vector constant -> Vector3(0.0, -1.0, 0.0).
    ///
    /// Points in the negative Y direction with unit magnitude. Represents the downward
    /// direction in most 3D coordinate systems.
    pub const DOWN: Vector3 = Vector3 { x: 0.0, y: -1.0, z: 0.0 };

    /// ### Forward-pointing unit vector constant -> Vector3(0.0, 0.0, -1.0).
    ///
    /// Points in the negative Z direction with unit magnitude. In right-handed coordinate systems,
    /// this typically represents the forward direction (into the screen).
    pub const FORWARD: Vector3 = Vector3 { x: 0.0, y: 0.0, z: -1.0 };

    /// ### Backward-pointing unit vector constant -> Vector3(0.0, 0.0, 1.0).
    ///
    /// Points in the positive Z direction with unit magnitude. In right-handed coordinate systems,
    /// this typically represents the backward direction (out of the screen).
    pub const BACK: Vector3 = Vector3 { x: 0.0, y: 0.0, z: 1.0 };

    /// ### Creates a new Vector3 with the specified x, y, and z components.
    ///
    /// This is the primary constructor for creating 3D vectors with explicit coordinate values.
    /// All components can be any finite floating-point value, including negative numbers and zero.
    ///
    /// # Arguments
    /// * `x` - The horizontal component (X-coordinate)
    /// * `y` - The vertical component (Y-coordinate)
    /// * `z` - The depth component (Z-coordinate)
    ///
    /// # Returns
    /// A new Vector3 instance with the specified components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let position = Vector3::new(1.0, 2.0, 3.0);
    /// assert_eq!(position.x, 1.0);
    /// assert_eq!(position.y, 2.0);
    /// assert_eq!(position.z, 3.0);
    ///
    /// // Negative coordinates for relative positions
    /// let velocity = Vector3::new(-5.0, 0.0, 10.0);
    /// assert_eq!(velocity.x, -5.0);
    /// ```
    #[inline]
    pub const fn new(x: f32, y: f32, z: f32) -> Self {
        Vector3 { x, y, z }
    }

    /// ### Creates a Vector3 from a Vector2 by adding a z component.
    ///
    /// This constructor allows easy conversion from 2D to 3D coordinates by specifying
    /// the z component explicitly. Useful for extruding 2D shapes into 3D space.
    ///
    /// # Arguments
    /// * `v2` - The 2D vector providing x and y components
    /// * `z` - The z component to add
    ///
    /// # Returns
    /// A new Vector3 with x and y from the Vector2 and the specified z component.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector2, Vector3};
    /// let pos_2d = Vector2::new(3.0, 4.0);
    /// let pos_3d = Vector3::from_vector2(pos_2d, 5.0);
    /// assert_eq!(pos_3d, Vector3::new(3.0, 4.0, 5.0));
    /// ```
    #[inline]
    pub fn from_vector2(v2: Vector2, z: f32) -> Self {
        Vector3::new(v2.x, v2.y, z)
    }

    /// ### Calculates the length (magnitude) of the vector.
    ///
    /// Uses fast inverse square root for optimal performance while maintaining
    /// acceptable accuracy for most 3D graphics applications.
    ///
    /// # Returns
    /// The length of the vector as a floating-point number.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let v = Vector3::new(3.0, 4.0, 0.0);
    /// assert!((v.length() - 5.0).abs() < 0.001);
    ///
    /// let unit = Vector3::new(1.0, 0.0, 0.0);
    /// assert!((unit.length() - 1.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn length(self) -> f32 {
        let length_sq = self.length_squared();
        if length_sq == 0.0 {
            0.0
        } else {
            1.0 / fast_inv_sqrt(length_sq)
        }
    }

    /// ### Calculates the squared length of the vector.
    ///
    /// This is more efficient than length() as it avoids the square root calculation.
    /// Useful for distance comparisons and magnitude-based operations.
    ///
    /// # Returns
    /// The squared length of the vector (x² + y² + z²).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let v = Vector3::new(3.0, 4.0, 0.0);
    /// assert_eq!(v.length_squared(), 25.0); // 3² + 4² + 0² = 9 + 16 + 0 = 25
    /// ```
    #[inline]
    pub fn length_squared(self) -> f32 {
        self.x * self.x + self.y * self.y + self.z * self.z
    }

    /// ### Returns a normalized copy of the vector.
    ///
    /// Creates a new vector with the same direction but unit length (magnitude = 1).
    /// Uses fast inverse square root for optimal performance. Returns zero vector
    /// if the original vector has zero length.
    ///
    /// # Returns
    /// A new Vector3 with unit length in the same direction, or zero vector if length is zero.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let v = Vector3::new(3.0, 4.0, 0.0);
    /// let normalized = v.normalized();
    /// assert!((normalized.length() - 1.0).abs() < 0.001);
    ///
    /// // Direction is preserved
    /// let direction = v.normalized();
    /// assert!((direction.x - 0.6).abs() < 0.001);
    /// assert!((direction.y - 0.8).abs() < 0.001);
    /// ```
    #[inline]
    pub fn normalized(self) -> Vector3 {
        let length_sq = self.length_squared();
        if length_sq == 0.0 {
            Vector3::ZERO
        } else {
            let inv_length = fast_inv_sqrt(length_sq);
            Vector3::new(self.x * inv_length, self.y * inv_length, self.z * inv_length)
        }
    }

    /// ### Calculates the dot product with another vector.
    ///
    /// The dot product is useful for determining the relationship between two vectors,
    /// such as the angle between them or projecting one vector onto another.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the dot product with
    ///
    /// # Returns
    /// The dot product as a floating-point number.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let v1 = Vector3::new(1.0, 2.0, 3.0);
    /// let v2 = Vector3::new(4.0, 5.0, 6.0);
    /// assert_eq!(v1.dot(v2), 32.0); // 1*4 + 2*5 + 3*6 = 4 + 10 + 18 = 32
    ///
    /// // Perpendicular vectors have zero dot product
    /// let right = Vector3::RIGHT;
    /// let up = Vector3::UP;
    /// assert_eq!(right.dot(up), 0.0);
    /// ```
    #[inline]
    pub fn dot(self, other: Vector3) -> f32 {
        self.x * other.x + self.y * other.y + self.z * other.z
    }

    /// ### Calculates the cross product with another vector.
    ///
    /// The cross product returns a vector perpendicular to both input vectors.
    /// The magnitude equals the area of the parallelogram formed by the two vectors.
    /// The direction follows the right-hand rule.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the cross product with
    ///
    /// # Returns
    /// A new Vector3 perpendicular to both input vectors.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let v1 = Vector3::new(1.0, 0.0, 0.0);
    /// let v2 = Vector3::new(0.0, 1.0, 0.0);
    /// let cross = v1.cross(v2);
    /// assert_eq!(cross, Vector3::new(0.0, 0.0, 1.0));
    ///
    /// // Cross product with basis vectors
    /// let right = Vector3::RIGHT;
    /// let up = Vector3::UP;
    /// let result = right.cross(up);
    /// assert_eq!(result, Vector3::new(0.0, 0.0, 1.0));
    /// ```
    #[inline]
    pub fn cross(self, other: Vector3) -> Vector3 {
        Vector3::new(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x,
        )
    }

    /// ### Returns the distance to another vector.
    ///
    /// Calculates the Euclidean distance between two points in 3D space.
    /// This is equivalent to (other - self).length().
    ///
    /// # Arguments
    /// * `other` - The other vector to calculate distance to
    ///
    /// # Returns
    /// The distance between the vectors as a floating-point number.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let pos1 = Vector3::new(0.0, 0.0, 0.0);
    /// let pos2 = Vector3::new(3.0, 4.0, 0.0);
    /// assert!((pos1.distance_to(pos2) - 5.0).abs() < 0.001);
    ///
    /// // Distance is symmetric
    /// assert_eq!(pos1.distance_to(pos2), pos2.distance_to(pos1));
    /// ```
    #[inline]
    pub fn distance_to(self, other: Vector3) -> f32 {
        (other - self).length()
    }

    /// ### Returns the squared distance to another vector.
    ///
    /// More efficient than distance_to() as it avoids the square root calculation.
    /// Useful for distance comparisons and nearest neighbor searches.
    ///
    /// # Arguments
    /// * `other` - The other vector to calculate squared distance to
    ///
    /// # Returns
    /// The squared distance between the vectors.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let pos1 = Vector3::new(0.0, 0.0, 0.0);
    /// let pos2 = Vector3::new(3.0, 4.0, 0.0);
    /// assert_eq!(pos1.distance_squared_to(pos2), 25.0);
    /// ```
    #[inline]
    pub fn distance_squared_to(self, other: Vector3) -> f32 {
        (other - self).length_squared()
    }

    /// ### Linear interpolation between this vector and another vector.
    ///
    /// Returns a vector that is linearly interpolated between this vector and the target vector
    /// based on the weight parameter. When weight is 0.0, returns this vector. When weight is 1.0,
    /// returns the target vector.
    ///
    /// # Arguments
    /// * `to` - The target vector to interpolate towards
    /// * `weight` - The interpolation weight (typically between 0.0 and 1.0)
    ///
    /// # Returns
    /// The interpolated vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let from = Vector3::new(0.0, 0.0, 0.0);
    /// let to = Vector3::new(10.0, 20.0, 30.0);
    /// let mid = from.lerp(to, 0.5);
    /// assert_eq!(mid, Vector3::new(5.0, 10.0, 15.0));
    /// ```
    #[inline]
    pub fn lerp(self, to: Vector3, weight: f32) -> Vector3 {
        Vector3::new(
            self.x + (to.x - self.x) * weight,
            self.y + (to.y - self.y) * weight,
            self.z + (to.z - self.z) * weight,
        )
    }

    /// ### Returns a vector with the absolute values of the components.
    ///
    /// Creates a new vector where all x, y, and z components are their absolute values.
    /// This is useful for distance calculations and ensuring positive coordinates.
    ///
    /// # Returns
    /// A new Vector3 with absolute values of all components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let v = Vector3::new(-3.0, -4.0, -5.0);
    /// let abs_v = v.abs();
    /// assert_eq!(abs_v, Vector3::new(3.0, 4.0, 5.0));
    /// ```
    #[inline]
    pub fn abs(self) -> Vector3 {
        Vector3::new(self.x.abs(), self.y.abs(), self.z.abs())
    }

    /// ### Checks if this vector is approximately equal to another vector.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// Two vectors are considered approximately equal if all components are within
    /// the epsilon threshold.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// `true` if the vectors are approximately equal, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let v1 = Vector3::new(1.0, 2.0, 3.0);
    /// let v2 = Vector3::new(1.0000001, 2.0000001, 3.0000001);
    /// assert!(v1.is_equal_approx(v2));
    ///
    /// let v3 = Vector3::new(1.1, 2.0, 3.0);
    /// assert!(!v1.is_equal_approx(v3));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Vector3) -> bool {
        const EPSILON: f32 = 1e-5;
        (self.x - other.x).abs() < EPSILON
            && (self.y - other.y).abs() < EPSILON
            && (self.z - other.z).abs() < EPSILON
    }

    /// ### Checks if the vector is normalized (has a length of approximately 1.0).
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// A vector is considered normalized if its length is within epsilon of 1.0.
    ///
    /// # Returns
    /// `true` if the vector is approximately normalized, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let normalized = Vector3::new(1.0, 0.0, 0.0);
    /// assert!(normalized.is_normalized());
    ///
    /// let not_normalized = Vector3::new(2.0, 0.0, 0.0);
    /// assert!(!not_normalized.is_normalized());
    /// ```
    #[inline]
    pub fn is_normalized(self) -> bool {
        const EPSILON: f32 = 1e-4;
        (self.length_squared() - 1.0).abs() < EPSILON
    }

    /// ### Checks if all components of the vector are finite numbers.
    ///
    /// Returns `false` if any component is infinite or NaN.
    ///
    /// # Returns
    /// `true` if all x, y, and z components are finite, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let finite = Vector3::new(1.0, 2.0, 3.0);
    /// assert!(finite.is_finite());
    ///
    /// let infinite = Vector3::new(f32::INFINITY, 2.0, 3.0);
    /// assert!(!infinite.is_finite());
    /// ```
    #[inline]
    pub fn is_finite(self) -> bool {
        self.x.is_finite() && self.y.is_finite() && self.z.is_finite()
    }

    /// ### Checks if the vector is approximately zero.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// A vector is considered approximately zero if all components are within
    /// epsilon of zero.
    ///
    /// # Returns
    /// `true` if the vector is approximately zero, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3;
    /// let zero = Vector3::ZERO;
    /// assert!(zero.is_zero_approx());
    ///
    /// let almost_zero = Vector3::new(1e-6, -1e-6, 1e-6);
    /// assert!(almost_zero.is_zero_approx());
    /// ```
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        const EPSILON: f32 = 1e-5;
        self.x.abs() < EPSILON && self.y.abs() < EPSILON && self.z.abs() < EPSILON
    }

    /// ### Converts this floating-point vector to an integer Vector3i.
    ///
    /// Each component is converted using Rust's default float-to-integer conversion,
    /// which truncates towards zero. This is useful for converting 3D world coordinates
    /// to voxel positions or continuous positions to discrete grid coordinates.
    ///
    /// # Returns
    /// A new Vector3i with components converted to integers.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector3, Vector3i};
    /// let float_vec = Vector3::new(3.7, -2.9, 5.1);
    /// let int_vec = float_vec.to_vector3i();
    /// assert_eq!(int_vec, Vector3i::new(3, -2, 5)); // Truncated towards zero
    ///
    /// // Useful for world to voxel coordinate conversion
    /// let world_pos = Vector3::new(123.8, 456.2, 789.6);
    /// let voxel_pos = world_pos.to_vector3i();
    /// assert_eq!(voxel_pos, Vector3i::new(123, 456, 789));
    /// ```
    #[inline]
    pub fn to_vector3i(self) -> super::Vector3i {
        super::Vector3i::new(self.x as i32, self.y as i32, self.z as i32)
    }

    /// ### Converts this 3D vector to a 2D Vector2 by dropping the z component.
    ///
    /// This is useful for projecting 3D positions onto 2D planes, converting 3D world
    /// coordinates to screen coordinates, or extracting 2D information from 3D data.
    ///
    /// # Returns
    /// A new Vector2 with only the x and y components from this vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector3, Vector2};
    /// let pos_3d = Vector3::new(10.0, 20.0, 30.0);
    /// let pos_2d = pos_3d.to_vector2();
    /// assert_eq!(pos_2d, Vector2::new(10.0, 20.0));
    ///
    /// // Useful for 3D to screen coordinate projection
    /// let world_pos = Vector3::new(100.0, 200.0, 50.0);
    /// let screen_pos = world_pos.to_vector2(); // Project onto XY plane
    /// assert_eq!(screen_pos, Vector2::new(100.0, 200.0));
    /// ```
    #[inline]
    pub fn to_vector2(self) -> Vector2 {
        Vector2::new(self.x, self.y)
    }

    /// ### Converts this 3D vector to a 4D Vector4 by adding a w component.
    ///
    /// This is useful for creating homogeneous coordinates, extending 3D positions
    /// to 4D space for matrix transformations, or adding additional data to 3D vectors.
    ///
    /// # Arguments
    /// * `w` - The w component to add to create the 4D vector
    ///
    /// # Returns
    /// A new Vector4 with x, y, z from this vector and the specified w component.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector3, Vector4};
    /// let pos_3d = Vector3::new(10.0, 20.0, 30.0);
    /// let homogeneous_point = pos_3d.to_vector4(1.0);
    /// assert_eq!(homogeneous_point, Vector4::new(10.0, 20.0, 30.0, 1.0));
    ///
    /// // For direction vectors in homogeneous coordinates
    /// let direction = Vector3::new(1.0, 0.0, 0.0);
    /// let homogeneous_dir = direction.to_vector4(0.0);
    /// assert_eq!(homogeneous_dir, Vector4::new(1.0, 0.0, 0.0, 0.0));
    /// ```
    #[inline]
    pub fn to_vector4(self, w: f32) -> super::Vector4 {
        super::Vector4::new(self.x, self.y, self.z, w)
    }
}

impl std::ops::Add for Vector3 {
    type Output = Vector3;

    #[inline]
    fn add(self, other: Vector3) -> Vector3 {
        Vector3::new(self.x + other.x, self.y + other.y, self.z + other.z)
    }
}

impl std::ops::Sub for Vector3 {
    type Output = Vector3;

    #[inline]
    fn sub(self, other: Vector3) -> Vector3 {
        Vector3::new(self.x - other.x, self.y - other.y, self.z - other.z)
    }
}

impl std::ops::Mul<f32> for Vector3 {
    type Output = Vector3;

    #[inline]
    fn mul(self, scalar: f32) -> Vector3 {
        Vector3::new(self.x * scalar, self.y * scalar, self.z * scalar)
    }
}

impl std::ops::Mul<Vector3> for f32 {
    type Output = Vector3;

    #[inline]
    fn mul(self, vector: Vector3) -> Vector3 {
        Vector3::new(self * vector.x, self * vector.y, self * vector.z)
    }
}

impl std::ops::Div<f32> for Vector3 {
    type Output = Vector3;

    #[inline]
    fn div(self, scalar: f32) -> Vector3 {
        Vector3::new(self.x / scalar, self.y / scalar, self.z / scalar)
    }
}

impl std::ops::Neg for Vector3 {
    type Output = Vector3;

    #[inline]
    fn neg(self) -> Vector3 {
        Vector3::new(-self.x, -self.y, -self.z)
    }
}

impl fmt::Display for Vector3 {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {}, {})", self.x, self.y, self.z)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vector3_creation() {
        let v = Vector3::new(1.0, 2.0, 3.0);
        assert_eq!(v.x, 1.0);
        assert_eq!(v.y, 2.0);
        assert_eq!(v.z, 3.0);
    }

    #[test]
    fn test_vector3_from_vector2() {
        let v2 = Vector2::new(3.0, 4.0);
        let v3 = Vector3::from_vector2(v2, 5.0);
        assert_eq!(v3, Vector3::new(3.0, 4.0, 5.0));
    }

    #[test]
    fn test_vector3_constants() {
        assert_eq!(Vector3::ZERO, Vector3::new(0.0, 0.0, 0.0));
        assert_eq!(Vector3::ONE, Vector3::new(1.0, 1.0, 1.0));
        assert_eq!(Vector3::LEFT, Vector3::new(-1.0, 0.0, 0.0));
        assert_eq!(Vector3::RIGHT, Vector3::new(1.0, 0.0, 0.0));
        assert_eq!(Vector3::UP, Vector3::new(0.0, 1.0, 0.0));
        assert_eq!(Vector3::DOWN, Vector3::new(0.0, -1.0, 0.0));
        assert_eq!(Vector3::FORWARD, Vector3::new(0.0, 0.0, -1.0));
        assert_eq!(Vector3::BACK, Vector3::new(0.0, 0.0, 1.0));
    }

    #[test]
    fn test_vector3_length() {
        let v = Vector3::new(3.0, 4.0, 0.0);
        assert!((v.length() - 5.0).abs() < 0.001);
        assert_eq!(v.length_squared(), 25.0);

        let zero = Vector3::ZERO;
        assert_eq!(zero.length(), 0.0);
        assert_eq!(zero.length_squared(), 0.0);
    }

    #[test]
    fn test_vector3_normalization() {
        let v = Vector3::new(3.0, 4.0, 0.0);
        let normalized = v.normalized();
        assert!((normalized.length() - 1.0).abs() < 0.001);
        assert!(normalized.is_normalized());

        // Zero vector normalization should return zero
        let zero_normalized = Vector3::ZERO.normalized();
        assert_eq!(zero_normalized, Vector3::ZERO);
    }

    #[test]
    fn test_vector3_dot_product() {
        let v1 = Vector3::new(1.0, 2.0, 3.0);
        let v2 = Vector3::new(4.0, 5.0, 6.0);
        assert_eq!(v1.dot(v2), 32.0); // 1*4 + 2*5 + 3*6 = 4 + 10 + 18 = 32

        // Perpendicular vectors
        let right = Vector3::RIGHT;
        let up = Vector3::UP;
        assert_eq!(right.dot(up), 0.0);
    }

    #[test]
    fn test_vector3_cross_product() {
        let v1 = Vector3::new(1.0, 0.0, 0.0);
        let v2 = Vector3::new(0.0, 1.0, 0.0);
        let cross = v1.cross(v2);
        assert_eq!(cross, Vector3::new(0.0, 0.0, 1.0));

        // Cross product with basis vectors
        let right = Vector3::RIGHT;
        let up = Vector3::UP;
        let result = right.cross(up);
        // The actual result is (0, 0, 1) which is the opposite of FORWARD (0, 0, -1)
        assert_eq!(result, Vector3::new(0.0, 0.0, 1.0));
    }

    #[test]
    fn test_vector3_distance() {
        let pos1 = Vector3::new(0.0, 0.0, 0.0);
        let pos2 = Vector3::new(3.0, 4.0, 0.0);
        assert!((pos1.distance_to(pos2) - 5.0).abs() < 0.001);
        assert_eq!(pos1.distance_squared_to(pos2), 25.0);

        // Distance is symmetric
        assert_eq!(pos1.distance_to(pos2), pos2.distance_to(pos1));
    }

    #[test]
    fn test_vector3_interpolation() {
        let from = Vector3::new(0.0, 0.0, 0.0);
        let to = Vector3::new(10.0, 20.0, 30.0);
        let mid = from.lerp(to, 0.5);
        assert_eq!(mid, Vector3::new(5.0, 10.0, 15.0));
    }

    #[test]
    fn test_vector3_utility_functions() {
        let v = Vector3::new(-1.0, -2.0, -3.0);
        assert_eq!(v.abs(), Vector3::new(1.0, 2.0, 3.0));
    }

    #[test]
    fn test_vector3_boolean_checks() {
        let v1 = Vector3::new(1.0, 2.0, 3.0);
        let v2 = Vector3::new(1.0000001, 2.0000001, 3.0000001);
        assert!(v1.is_equal_approx(v2));

        let normalized = Vector3::new(1.0, 0.0, 0.0);
        assert!(normalized.is_normalized());

        let finite = Vector3::new(1.0, 2.0, 3.0);
        assert!(finite.is_finite());

        let infinite = Vector3::new(f32::INFINITY, 2.0, 3.0);
        assert!(!infinite.is_finite());

        assert!(Vector3::ZERO.is_zero_approx());
        assert!(!Vector3::ONE.is_zero_approx());
    }

    #[test]
    fn test_vector3_conversion_methods() {
        let float_vec = Vector3::new(3.7, -2.9, 5.1);
        let int_vec = float_vec.to_vector3i();
        assert_eq!(int_vec, super::super::Vector3i::new(3, -2, 5));

        let vec_2d = float_vec.to_vector2();
        assert_eq!(vec_2d, Vector2::new(3.7, -2.9));

        let vec_4d = float_vec.to_vector4(1.0);
        assert_eq!(vec_4d, super::super::Vector4::new(3.7, -2.9, 5.1, 1.0));
    }

    #[test]
    fn test_vector3_edge_cases() {
        // Very small values
        let small = Vector3::new(1e-10, 1e-10, 1e-10);
        assert!(small.is_zero_approx());

        // Very large values
        let large = Vector3::new(1e10, 1e10, 1e10);
        assert!(large.is_finite());

        // NaN handling
        let nan_vec = Vector3::new(f32::NAN, 1.0, 2.0);
        assert!(!nan_vec.is_finite());
        assert!(!nan_vec.is_normalized());
    }
}
