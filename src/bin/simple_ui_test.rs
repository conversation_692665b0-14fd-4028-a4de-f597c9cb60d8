//! Simple UI Rendering Test
//!
//! This is a minimal test to verify that the UI rendering pipeline works correctly.
//! It renders a simple colored rectangle to the screen.

use winit::{
    application::ApplicationHandler,
    event::{ElementState, WindowEvent},
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    keyboard::{<PERSON><PERSON><PERSON>, PhysicalKey},
    window::WindowId,
};

use verturion::core::{
    renderer::{Window, WindowConfig, Renderer},
    math::{Vector2, Rect2},
    variant::Color,
};

/// ### Simple UI test application.
pub struct SimpleUITest {
    /// Main window for rendering
    window: Window,
    /// Graphics renderer
    renderer: Option<Renderer>,
    /// Whether the application should exit
    should_exit: bool,
}

impl SimpleUITest {
    /// ### Creates a new simple UI test application.
    pub fn new() -> Self {
        let window_config = WindowConfig {
            title: "Simple UI Rendering Test".to_string(),
            width: 800,
            height: 600,
            resizable: true,
            vsync: true,
            clear_color: [0.1, 0.1, 0.2, 1.0], // Dark blue background
        };

        let window = Window::new(window_config);

        Self {
            window,
            renderer: None,
            should_exit: false,
        }
    }

    /// ### Initializes the simple UI test.
    pub async fn initialize(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        // Initialize window
        self.window.initialize(event_loop).await?;

        // Create renderer
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        let renderer = Renderer::new(device, queue, config.format).await?;
        self.renderer = Some(renderer);

        println!("Simple UI test initialized successfully!");
        println!("Window size: {}x{}", config.width, config.height);
        println!("Surface format: {:?}", config.format);

        Ok(())
    }

    /// ### Renders the test.
    pub fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let renderer = self.renderer.as_mut().ok_or("No renderer available")?;
        
        // Get rendering resources
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        // Begin frame
        let surface_texture = self.window.begin_frame().ok_or("Failed to get surface texture")?;
        let view = surface_texture.texture.create_view(&wgpu::TextureViewDescriptor::default());

        // Create command encoder
        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some("Render Encoder"),
        });

        // Store window size for rendering
        let window_size = Vector2::new(config.width as f32, config.height as f32);

        // Begin render pass
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Main Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.1,
                            b: 0.2,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Clear UI renderer for new frame
            renderer.ui_renderer_mut().clear();

            // Add a simple test rectangle
            let ui_renderer = renderer.ui_renderer_mut();
            
            // Add a red rectangle in the center
            let rect_size = Vector2::new(200.0, 100.0);
            let rect_position = Vector2::new(
                (window_size.x - rect_size.x) / 2.0,
                (window_size.y - rect_size.y) / 2.0,
            );
            
            ui_renderer.add_quad(
                Rect2::from_position_size(rect_position, rect_size),
                Color::new(1.0, 0.0, 0.0, 1.0) // Red color
            );

            // Flush UI rendering
            ui_renderer.flush(&mut render_pass, queue, window_size)?;
        }

        // Submit commands
        queue.submit(std::iter::once(encoder.finish()));
        surface_texture.present();

        Ok(())
    }

    /// ### Handles window events.
    pub fn handle_window_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                self.should_exit = true;
            }
            WindowEvent::Resized(physical_size) => {
                self.window.resize(*physical_size);
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state == ElementState::Pressed {
                    match event.physical_key {
                        PhysicalKey::Code(KeyCode::Escape) => {
                            self.should_exit = true;
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }
    }

    /// ### Checks if the application should exit.
    pub fn should_exit(&self) -> bool {
        self.should_exit || self.window.should_close()
    }
}

impl ApplicationHandler for SimpleUITest {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.renderer.is_none() {
            // Initialize on first resume
            pollster::block_on(async {
                if let Err(e) = self.initialize(event_loop).await {
                    eprintln!("Failed to initialize simple UI test: {}", e);
                    event_loop.exit();
                }
            });
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        self.handle_window_event(&event);
        
        if self.should_exit() {
            event_loop.exit();
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        // Render
        if let Err(e) = self.render() {
            eprintln!("Render error: {}", e);
        }
        
        // Request redraw
        if let Some(window) = self.window.window() {
            window.request_redraw();
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎮 Simple UI Rendering Test - Starting...");
    println!("======================================");
    println!("Controls:");
    println!("  ESC - Exit application");
    println!("======================================\n");

    let event_loop = EventLoop::new()?;
    event_loop.set_control_flow(ControlFlow::Poll);

    let mut app = SimpleUITest::new();
    event_loop.run_app(&mut app)?;

    println!("\n🎮 Simple UI Rendering Test - Exiting...");
    Ok(())
}
