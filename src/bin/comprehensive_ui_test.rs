//! Comprehensive UI Visual Test Application
//!
//! This application demonstrates all implemented UI nodes in the Verturion game engine
//! with organized tabs, interactive functionality, and real-time visual feedback.
//! It serves as both a testing platform and a showcase of the engine's UI capabilities.

use winit::{
    application::ApplicationHandler,
    event::{<PERSON>ementState, WindowEvent, <PERSON><PERSON><PERSON>on as Winit<PERSON><PERSON>Button},
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    keyboard::{KeyCode, PhysicalKey},
    window::WindowId,
};

use verturion::core::{
    renderer::{Window, WindowConfig, Renderer},
    scene::Node,
    scene::nodes::{
        Timer, AudioStreamPlayer2D, Camera2D, AnimationPlayer,
        Label, Button, LineEdit, ProgressBar, CheckBox,
        StaticBody2D,
    },
    // scene::nodes::ui::{TabContainer, VBoxContainer, HBoxContainer}, // TODO: Fix container exports
    signal::SignalManager,
    math::Vector2,
    variant::Color,
    input::{Input, InputMap, KeyCode as InputKeyCode},
};

/// ### Comprehensive UI test application.
pub struct ComprehensiveUITest {
    /// Main window for rendering
    window: Window,
    /// Graphics renderer
    renderer: Option<Renderer>,
    /// Root scene node
    root_node: Option<Node>,
    /// Signal manager for events
    signal_manager: SignalManager,
    /// Input system
    input: Input,
    /// Input action mapping
    input_map: InputMap,
    /// UI test components
    ui_components: UITestComponents,
    /// Performance metrics
    performance_metrics: PerformanceMetrics,
    /// Whether the application should exit
    should_exit: bool,
    /// Current mouse position
    mouse_position: Vector2,
    /// Frame counter for animations
    frame_counter: u64,
}

/// ### Collection of UI test components organized by tabs.
pub struct UITestComponents {
    /// Main tab container
    pub tab_container: TabContainer,
    /// Basic controls tab content
    pub basic_controls: BasicControlsTab,
    /// Text input tab content
    pub text_input: TextInputTab,
    /// Progress and feedback tab content
    pub progress_feedback: ProgressFeedbackTab,
    /// Layout containers tab content
    pub layout_containers: LayoutContainersTab,
    /// Essential nodes tab content
    pub essential_nodes: EssentialNodesTab,
}

/// ### Basic controls tab containing buttons and checkboxes.
#[derive(Debug, Clone)]
pub struct BasicControlsTab {
    /// Container for basic controls
    pub container: VBoxContainer,
    /// Primary action button
    pub primary_button: Button,
    /// Secondary action button
    pub secondary_button: Button,
    /// Disabled button for testing
    pub disabled_button: Button,
    /// Toggle button
    pub toggle_button: Button,
    /// Settings checkbox
    pub settings_checkbox: CheckBox,
    /// Feature checkbox
    pub feature_checkbox: CheckBox,
    /// Debug checkbox
    pub debug_checkbox: CheckBox,
}

/// ### Text input tab containing various text input controls.
#[derive(Debug, Clone)]
pub struct TextInputTab {
    /// Container for text inputs
    pub container: VBoxContainer,
    /// Name input field
    pub name_input: LineEdit,
    /// Email input field
    pub email_input: LineEdit,
    /// Password input field
    pub password_input: LineEdit,
    /// Search input field
    pub search_input: LineEdit,
    /// Multiline text area (using LineEdit for now)
    pub text_area: LineEdit,
    /// Input labels
    pub name_label: Label,
    pub email_label: Label,
    pub password_label: Label,
    pub search_label: Label,
    pub text_area_label: Label,
}

/// ### Progress and feedback tab containing progress bars and status displays.
#[derive(Debug, Clone)]
pub struct ProgressFeedbackTab {
    /// Container for progress controls
    pub container: VBoxContainer,
    /// Loading progress bar
    pub loading_progress: ProgressBar,
    /// Health progress bar
    pub health_progress: ProgressBar,
    /// Experience progress bar
    pub experience_progress: ProgressBar,
    /// Download progress bar
    pub download_progress: ProgressBar,
    /// Status label
    pub status_label: Label,
    /// Progress labels
    pub loading_label: Label,
    pub health_label: Label,
    pub experience_label: Label,
    pub download_label: Label,
}

/// ### Layout containers tab demonstrating container functionality.
#[derive(Debug, Clone)]
pub struct LayoutContainersTab {
    /// Main container
    pub container: VBoxContainer,
    /// Horizontal layout example
    pub hbox_example: HBoxContainer,
    /// Vertical layout example
    pub vbox_example: VBoxContainer,
    /// Nested layout example
    pub nested_container: VBoxContainer,
    /// Layout demonstration buttons
    pub hbox_buttons: Vec<Button>,
    pub vbox_buttons: Vec<Button>,
    /// Layout labels
    pub hbox_label: Label,
    pub vbox_label: Label,
    pub nested_label: Label,
}

/// ### Essential nodes tab containing core engine nodes.
#[derive(Debug, Clone)]
pub struct EssentialNodesTab {
    /// Container for essential nodes
    pub container: VBoxContainer,
    /// Timer for demonstrations
    pub demo_timer: Timer,
    /// Audio player
    pub audio_player: AudioStreamPlayer2D,
    /// Camera
    pub camera: Camera2D,
    /// Animation player
    pub animation_player: AnimationPlayer,
    /// Static body
    pub static_body: StaticBody2D,
    /// Node status labels
    pub timer_label: Label,
    pub audio_label: Label,
    pub camera_label: Label,
    pub animation_label: Label,
    pub physics_label: Label,
}

/// ### Performance metrics tracking.
#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    /// Frames per second
    pub fps: f32,
    /// Frame time in milliseconds
    pub frame_time: f32,
    /// Memory usage (placeholder)
    pub memory_usage: f32,
    /// UI element count
    pub ui_element_count: u32,
    /// Input events processed
    pub input_events_processed: u32,
    /// Last frame timestamp
    pub last_frame_time: std::time::Instant,
    /// Frame time accumulator
    pub frame_time_accumulator: f32,
    /// Frame count for averaging
    pub frame_count: u32,
}

impl PerformanceMetrics {
    /// ### Creates new performance metrics tracker.
    pub fn new() -> Self {
        Self {
            fps: 0.0,
            frame_time: 0.0,
            memory_usage: 0.0,
            ui_element_count: 0,
            input_events_processed: 0,
            last_frame_time: std::time::Instant::now(),
            frame_time_accumulator: 0.0,
            frame_count: 0,
        }
    }

    /// ### Updates performance metrics.
    pub fn update(&mut self) {
        let now = std::time::Instant::now();
        let delta = now.duration_since(self.last_frame_time).as_secs_f32();
        self.last_frame_time = now;

        self.frame_time_accumulator += delta;
        self.frame_count += 1;

        // Update FPS every 60 frames
        if self.frame_count >= 60 {
            self.fps = self.frame_count as f32 / self.frame_time_accumulator;
            self.frame_time = (self.frame_time_accumulator / self.frame_count as f32) * 1000.0;
            self.frame_time_accumulator = 0.0;
            self.frame_count = 0;
        }
    }
}

impl ComprehensiveUITest {
    /// ### Creates a new comprehensive UI test application.
    pub fn new() -> Self {
        let window_config = WindowConfig {
            title: "Verturion Comprehensive UI Test - All Components".to_string(),
            width: 1400,
            height: 900,
            resizable: true,
            vsync: true,
            clear_color: [0.15, 0.15, 0.2, 1.0], // Dark blue-gray background
        };

        let window = Window::new(window_config);
        let signal_manager = SignalManager::new();
        let input = Input::new();
        let mut input_map = InputMap::new();

        // Set up input actions
        input_map.add_action("ui_accept", InputKeyCode::Enter);
        input_map.add_action("ui_cancel", InputKeyCode::Escape);
        input_map.add_action("ui_select", InputKeyCode::Space);
        input_map.add_action("toggle_debug", InputKeyCode::F1);
        input_map.add_action("reset_demo", InputKeyCode::F5);

        let ui_components = UITestComponents::new();
        let performance_metrics = PerformanceMetrics::new();

        Self {
            window,
            renderer: None,
            root_node: None,
            signal_manager,
            input,
            input_map,
            ui_components,
            performance_metrics,
            should_exit: false,
            mouse_position: Vector2::ZERO,
            frame_counter: 0,
        }
    }

    /// ### Initializes the comprehensive UI test.
    pub async fn initialize(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        // Initialize window
        self.window.initialize(event_loop).await?;

        // Create renderer
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        let renderer = Renderer::new(device, queue, config.format).await?;
        self.renderer = Some(renderer);

        // Create root scene node
        let root = Node::new("UITestRoot");
        self.root_node = Some(root);

        // Initialize UI components
        self.ui_components.initialize(&mut self.signal_manager);

        println!("🎮 Comprehensive UI Test initialized successfully!");
        println!("Window size: {}x{}", config.width, config.height);
        println!("Surface format: {:?}", config.format);
        println!("UI Components: {} tabs with {} total elements",
                 self.ui_components.tab_container.get_tab_count(),
                 self.performance_metrics.ui_element_count);

        Ok(())
    }

    /// ### Updates the test application state.
    pub fn update(&mut self, delta_time: f32) {
        self.frame_counter += 1;
        self.performance_metrics.update();

        // Update input system
        self.input.update(&self.input_map, (delta_time * 1000.0) as u64);

        // Update UI components
        self.ui_components.update(delta_time, &mut self.signal_manager);

        // Handle input actions
        if self.input.is_action_just_pressed("ui_cancel") {
            self.should_exit = true;
        }

        if self.input.is_action_just_pressed("toggle_debug") {
            if self.ui_components.essential_nodes.demo_timer.is_paused() {
                self.ui_components.essential_nodes.demo_timer.resume();
            } else {
                self.ui_components.essential_nodes.demo_timer.pause();
            }
        }

        if self.input.is_action_just_pressed("reset_demo") {
            self.reset_demo_state();
        }
    }

    /// ### Resets the demo to initial state.
    fn reset_demo_state(&mut self) {
        // Reset progress bars
        self.ui_components.progress_feedback.loading_progress.set_value(0.0, &mut self.signal_manager);
        self.ui_components.progress_feedback.health_progress.set_value(100.0, &mut self.signal_manager);
        self.ui_components.progress_feedback.experience_progress.set_value(0.0, &mut self.signal_manager);
        self.ui_components.progress_feedback.download_progress.set_value(0.0, &mut self.signal_manager);

        // Reset checkboxes
        self.ui_components.basic_controls.settings_checkbox.set_checked(false, &mut self.signal_manager);
        self.ui_components.basic_controls.feature_checkbox.set_checked(false, &mut self.signal_manager);
        self.ui_components.basic_controls.debug_checkbox.set_checked(false, &mut self.signal_manager);

        // Clear text inputs
        self.ui_components.text_input.name_input.set_text("".to_string());
        self.ui_components.text_input.email_input.set_text("".to_string());
        self.ui_components.text_input.password_input.set_text("".to_string());
        self.ui_components.text_input.search_input.set_text("".to_string());
        self.ui_components.text_input.text_area.set_text("".to_string());

        // Reset timer
        self.ui_components.essential_nodes.demo_timer.stop();
        self.ui_components.essential_nodes.demo_timer.set_wait_time(5.0);

        println!("Demo state reset to initial values");
    }

    /// ### Renders the comprehensive UI test.
    pub fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Get rendering resources
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        // Begin frame
        let surface_texture = self.window.begin_frame().ok_or("Failed to get surface texture")?;
        let view = surface_texture.texture.create_view(&wgpu::TextureViewDescriptor::default());

        // Create command encoder
        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some("UI Test Render Encoder"),
        });

        // Store window size for rendering
        let window_size = Vector2::new(config.width as f32, config.height as f32);

        // Begin render pass
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("UI Test Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.15,
                            g: 0.15,
                            b: 0.2,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Get renderer and clear UI renderer for new frame
            let renderer = self.renderer.as_mut().ok_or("No renderer available")?;
            renderer.ui_renderer_mut().clear();

            // Render UI components directly
            let ui_renderer = renderer.ui_renderer_mut();

            // Calculate tab container area (leave space for performance metrics)
            let tab_area = Vector2::new(window_size.x - 20.0, window_size.y - 120.0);
            let tab_position = Vector2::new(10.0, 10.0);

            // Render tab container background
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(tab_position, tab_area),
                Color::new(0.2, 0.2, 0.25, 0.9)
            );

            // Render simple placeholder content for now
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(tab_position.x + 50.0, tab_position.y + 50.0),
                    Vector2::new(200.0, 100.0)
                ),
                Color::new(0.5, 0.5, 0.7, 1.0)
            );

            // Render performance metrics
            let metrics_height = 100.0;
            let metrics_position = Vector2::new(10.0, window_size.y - metrics_height - 10.0);
            let metrics_size = Vector2::new(window_size.x - 20.0, metrics_height);

            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(metrics_position, metrics_size),
                Color::new(0.1, 0.1, 0.15, 0.9)
            );

            // Flush UI rendering
            ui_renderer.flush(&mut render_pass, queue, window_size)?;
        }

        // Submit commands
        queue.submit(std::iter::once(encoder.finish()));
        surface_texture.present();

        Ok(())
    }

    /// ### Renders all UI content in one method to avoid borrowing issues.
    fn render_all_ui_content(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Render UI components
        self.render_ui_components(ui_renderer, window_size)?;

        // Render performance metrics
        self.render_performance_metrics(ui_renderer, window_size)?;

        Ok(())
    }

    /// ### Renders all UI components.
    fn render_ui_components(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Calculate tab container area (leave space for performance metrics)
        let tab_area = Vector2::new(window_size.x - 20.0, window_size.y - 120.0);
        let tab_position = Vector2::new(10.0, 10.0);

        // Render tab container background
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(tab_position, tab_area),
            Color::new(0.2, 0.2, 0.25, 0.9)
        );

        // Render tab buttons
        self.render_tab_buttons(ui_renderer, tab_position, tab_area)?;

        // Render current tab content
        let content_position = Vector2::new(tab_position.x + 10.0, tab_position.y + 40.0);
        let content_size = Vector2::new(tab_area.x - 20.0, tab_area.y - 50.0);

        match self.ui_components.tab_container.get_current_tab() {
            0 => self.render_basic_controls_tab(ui_renderer, content_position, content_size)?,
            1 => self.render_text_input_tab(ui_renderer, content_position, content_size)?,
            2 => self.render_progress_feedback_tab(ui_renderer, content_position, content_size)?,
            3 => self.render_layout_containers_tab(ui_renderer, content_position, content_size)?,
            4 => self.render_essential_nodes_tab(ui_renderer, content_position, content_size)?,
            _ => {}
        }

        Ok(())
    }

    /// ### Renders tab buttons.
    fn render_tab_buttons(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        tab_position: Vector2,
        tab_area: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let tab_count = self.ui_components.tab_container.get_tab_count();
        if tab_count == 0 { return Ok(()); }

        let tab_width = (tab_area.x / tab_count as f32).min(150.0);
        let tab_height = 32.0;

        for i in 0..tab_count {
            let tab_x = tab_position.x + (i as f32 * tab_width);
            let tab_pos = Vector2::new(tab_x, tab_position.y);
            let tab_size = Vector2::new(tab_width, tab_height);

            // Choose tab color based on whether it's active
            let tab_color = if i as i32 == self.ui_components.tab_container.get_current_tab() {
                Color::new(0.4, 0.4, 0.5, 1.0) // Active tab
            } else {
                Color::new(0.25, 0.25, 0.3, 1.0) // Inactive tab
            };

            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(tab_pos, tab_size),
                tab_color
            );

            // TODO: Add text rendering for tab titles when TextRenderer is implemented
        }

        Ok(())
    }

    /// ### Renders performance metrics display.
    fn render_performance_metrics(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let metrics_height = 100.0;
        let metrics_position = Vector2::new(10.0, window_size.y - metrics_height - 10.0);
        let metrics_size = Vector2::new(window_size.x - 20.0, metrics_height);

        // Render metrics background
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(metrics_position, metrics_size),
            Color::new(0.1, 0.1, 0.15, 0.9)
        );

        // TODO: Add text rendering for metrics when TextRenderer is implemented
        // For now, just render colored bars representing the metrics

        let bar_width = 200.0;
        let bar_height = 20.0;
        let bar_spacing = 30.0;

        // FPS bar (green = good, yellow = ok, red = bad)
        let fps_color = if self.performance_metrics.fps > 50.0 {
            Color::new(0.0, 1.0, 0.0, 1.0) // Green
        } else if self.performance_metrics.fps > 30.0 {
            Color::new(1.0, 1.0, 0.0, 1.0) // Yellow
        } else {
            Color::new(1.0, 0.0, 0.0, 1.0) // Red
        };

        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(metrics_position.x + 10.0, metrics_position.y + 10.0),
                Vector2::new(bar_width * (self.performance_metrics.fps / 60.0).min(1.0), bar_height)
            ),
            fps_color
        );

        // Frame time bar
        let frame_time_ratio = (self.performance_metrics.frame_time / 16.67).min(1.0); // 16.67ms = 60fps
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(metrics_position.x + 10.0, metrics_position.y + 10.0 + bar_spacing),
                Vector2::new(bar_width * frame_time_ratio, bar_height)
            ),
            Color::new(0.0, 0.5, 1.0, 1.0) // Blue
        );

        // Memory usage bar (placeholder)
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(metrics_position.x + 10.0, metrics_position.y + 10.0 + bar_spacing * 2.0),
                Vector2::new(bar_width * 0.3, bar_height) // Placeholder 30%
            ),
            Color::new(1.0, 0.5, 0.0, 1.0) // Orange
        );

        Ok(())
    }

    /// ### Renders the basic controls tab.
    fn render_basic_controls_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut y_offset = 0.0;
        let element_height = 40.0;
        let element_spacing = 50.0;

        // Render buttons
        ui_renderer.render_button(
            &self.ui_components.basic_controls.primary_button,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(150.0, element_height)
        )?;
        y_offset += element_spacing;

        ui_renderer.render_button(
            &self.ui_components.basic_controls.secondary_button,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(150.0, element_height)
        )?;
        y_offset += element_spacing;

        ui_renderer.render_button(
            &self.ui_components.basic_controls.disabled_button,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(150.0, element_height)
        )?;
        y_offset += element_spacing;

        // Render checkboxes
        ui_renderer.render_checkbox(
            &self.ui_components.basic_controls.settings_checkbox,
            Vector2::new(position.x + 200.0, position.y),
            Vector2::new(200.0, 30.0)
        )?;

        ui_renderer.render_checkbox(
            &self.ui_components.basic_controls.feature_checkbox,
            Vector2::new(position.x + 200.0, position.y + element_spacing),
            Vector2::new(200.0, 30.0)
        )?;

        ui_renderer.render_checkbox(
            &self.ui_components.basic_controls.debug_checkbox,
            Vector2::new(position.x + 200.0, position.y + element_spacing * 2.0),
            Vector2::new(200.0, 30.0)
        )?;

        Ok(())
    }

    /// ### Renders the text input tab.
    fn render_text_input_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut y_offset = 0.0;
        let element_height = 30.0;
        let element_spacing = 50.0;
        let input_width = 300.0;

        // Render text inputs
        ui_renderer.render_line_edit(
            &self.ui_components.text_input.name_input,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(input_width, element_height)
        )?;
        y_offset += element_spacing;

        ui_renderer.render_line_edit(
            &self.ui_components.text_input.email_input,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(input_width, element_height)
        )?;
        y_offset += element_spacing;

        ui_renderer.render_line_edit(
            &self.ui_components.text_input.password_input,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(input_width, element_height)
        )?;
        y_offset += element_spacing;

        ui_renderer.render_line_edit(
            &self.ui_components.text_input.search_input,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(input_width, element_height)
        )?;

        Ok(())
    }

    /// ### Renders the progress and feedback tab.
    fn render_progress_feedback_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let mut y_offset = 0.0;
        let element_height = 20.0;
        let element_spacing = 50.0;
        let progress_width = 400.0;

        // Render progress bars
        ui_renderer.render_progress_bar(
            &self.ui_components.progress_feedback.loading_progress,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(progress_width, element_height)
        )?;
        y_offset += element_spacing;

        ui_renderer.render_progress_bar(
            &self.ui_components.progress_feedback.health_progress,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(progress_width, element_height)
        )?;
        y_offset += element_spacing;

        ui_renderer.render_progress_bar(
            &self.ui_components.progress_feedback.experience_progress,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(progress_width, element_height)
        )?;
        y_offset += element_spacing;

        ui_renderer.render_progress_bar(
            &self.ui_components.progress_feedback.download_progress,
            Vector2::new(position.x, position.y + y_offset),
            Vector2::new(progress_width, element_height)
        )?;

        Ok(())
    }

    /// ### Renders the layout containers tab.
    fn render_layout_containers_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Render container demonstration areas
        let container_width = size.x / 2.0 - 20.0;
        let container_height = size.y / 2.0 - 20.0;

        // HBox container area
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                position,
                Vector2::new(container_width, container_height)
            ),
            Color::new(0.3, 0.3, 0.4, 0.5)
        );

        // VBox container area
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(position.x + container_width + 20.0, position.y),
                Vector2::new(container_width, container_height)
            ),
            Color::new(0.4, 0.3, 0.3, 0.5)
        );

        // TODO: Render actual container contents when layout system is implemented

        Ok(())
    }

    /// ### Renders the essential nodes tab.
    fn render_essential_nodes_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        position: Vector2,
        size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Render node status indicators
        let mut y_offset = 0.0;
        let indicator_size = 20.0;
        let indicator_spacing = 40.0;

        // Timer indicator
        let timer_color = if !self.ui_components.essential_nodes.demo_timer.is_active() {
            Color::new(0.5, 0.5, 0.5, 1.0) // Gray - stopped
        } else {
            Color::new(0.0, 1.0, 0.0, 1.0) // Green - running
        };

        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(position.x, position.y + y_offset),
                Vector2::new(indicator_size, indicator_size)
            ),
            timer_color
        );
        y_offset += indicator_spacing;

        // Audio player indicator
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(position.x, position.y + y_offset),
                Vector2::new(indicator_size, indicator_size)
            ),
            Color::new(0.0, 0.5, 1.0, 1.0) // Blue
        );
        y_offset += indicator_spacing;

        // Camera indicator
        let camera_color = if self.ui_components.essential_nodes.camera.is_current() {
            Color::new(1.0, 1.0, 0.0, 1.0) // Yellow - active
        } else {
            Color::new(0.5, 0.5, 0.0, 1.0) // Dark yellow - inactive
        };

        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(position.x, position.y + y_offset),
                Vector2::new(indicator_size, indicator_size)
            ),
            camera_color
        );

        Ok(())
    }

    /// ### Handles window events.
    pub fn handle_window_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                self.should_exit = true;
            }
            WindowEvent::Resized(physical_size) => {
                self.window.resize(*physical_size);
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state == ElementState::Pressed {
                    match event.physical_key {
                        PhysicalKey::Code(KeyCode::Escape) => {
                            self.should_exit = true;
                        }
                        PhysicalKey::Code(KeyCode::F1) => {
                            // Toggle debug mode
                            println!("Debug mode toggled");
                        }
                        PhysicalKey::Code(KeyCode::F5) => {
                            self.reset_demo_state();
                        }
                        PhysicalKey::Code(KeyCode::Tab) => {
                            // Switch to next tab
                            let current_tab = self.ui_components.tab_container.get_current_tab();
                            let tab_count = self.ui_components.tab_container.get_tab_count() as i32;
                            let next_tab = (current_tab + 1) % tab_count;
                            self.ui_components.tab_container.set_current_tab(next_tab, &mut self.signal_manager);
                        }
                        _ => {}
                    }
                }
            }
            WindowEvent::MouseInput { button, state, .. } => {
                if *state == ElementState::Pressed && *button == WinitMouseButton::Left {
                    // Handle tab clicks
                    self.ui_components.tab_container.handle_tab_click(self.mouse_position, &mut self.signal_manager);
                }
            }
            WindowEvent::CursorMoved { position, .. } => {
                self.mouse_position = Vector2::new(position.x as f32, position.y as f32);
            }
            _ => {}
        }
    }

    /// ### Checks if the application should exit.
    pub fn should_exit(&self) -> bool {
        self.should_exit || self.window.should_close()
    }
}

impl UITestComponents {
    /// ### Creates new UI test components.
    pub fn new() -> Self {
        let mut tab_container = TabContainer::new("MainTabs");

        // Add tabs
        tab_container.add_tab("Basic Controls");
        tab_container.add_tab("Text Input");
        tab_container.add_tab("Progress & Feedback");
        tab_container.add_tab("Layout Containers");
        tab_container.add_tab("Essential Nodes");

        Self {
            tab_container,
            basic_controls: BasicControlsTab::new(),
            text_input: TextInputTab::new(),
            progress_feedback: ProgressFeedbackTab::new(),
            layout_containers: LayoutContainersTab::new(),
            essential_nodes: EssentialNodesTab::new(),
        }
    }

    /// ### Initializes UI components.
    pub fn initialize(&mut self, signal_manager: &mut SignalManager) {
        // Register signals
        signal_manager.register_signal(self.tab_container.get_tab_changed_signal().clone());
        signal_manager.register_signal(self.basic_controls.primary_button.get_pressed_signal().clone());
        signal_manager.register_signal(self.basic_controls.settings_checkbox.get_toggled_signal().clone());
        signal_manager.register_signal(self.essential_nodes.demo_timer.get_timeout_signal().clone());

        // Start demo timer
        self.essential_nodes.demo_timer.start(signal_manager);
    }

    /// ### Updates UI components.
    pub fn update(&mut self, delta_time: f32, signal_manager: &mut SignalManager) {
        // Update timer
        self.essential_nodes.demo_timer.update(delta_time as f64, signal_manager);

        // Update animation player
        self.essential_nodes.animation_player.update(delta_time, signal_manager);

        // Animate progress bars
        let time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f32();

        // Loading progress (0-100% cycle)
        let loading_value = ((time * 0.5).sin() * 0.5 + 0.5) * 100.0;
        self.progress_feedback.loading_progress.set_value(loading_value as f64, signal_manager);

        // Health progress (slowly decreasing)
        let current_health = self.progress_feedback.health_progress.get_value();
        if current_health > 0.0 {
            let new_health = (current_health - delta_time as f64 * 5.0).max(0.0);
            self.progress_feedback.health_progress.set_value(new_health, signal_manager);
        }

        // Experience progress (slowly increasing)
        let current_exp = self.progress_feedback.experience_progress.get_value();
        if current_exp < 100.0 {
            let new_exp = (current_exp + delta_time as f64 * 3.0).min(100.0);
            self.progress_feedback.experience_progress.set_value(new_exp, signal_manager);
        }

        // Download progress (step-wise)
        let download_value = ((time * 0.2) as i32 % 101) as f64;
        self.progress_feedback.download_progress.set_value(download_value, signal_manager);
    }
}

impl BasicControlsTab {
    /// ### Creates new basic controls tab.
    pub fn new() -> Self {
        let container = VBoxContainer::new("BasicControlsContainer");

        let mut primary_button = Button::new("PrimaryButton");
        primary_button.set_text(verturion::core::variant::String::from("Primary Action"));

        let mut secondary_button = Button::new("SecondaryButton");
        secondary_button.set_text(verturion::core::variant::String::from("Secondary Action"));

        let mut disabled_button = Button::new("DisabledButton");
        disabled_button.set_text(verturion::core::variant::String::from("Disabled Button"));
        disabled_button.set_disabled(true);

        let mut toggle_button = Button::new("ToggleButton");
        toggle_button.set_text(verturion::core::variant::String::from("Toggle"));
        toggle_button.set_toggle_mode(true);

        let mut settings_checkbox = CheckBox::new("SettingsCheckBox");
        settings_checkbox.set_text("Enable advanced settings".to_string());

        let mut feature_checkbox = CheckBox::new("FeatureCheckBox");
        feature_checkbox.set_text("Enable experimental features".to_string());

        let mut debug_checkbox = CheckBox::new("DebugCheckBox");
        debug_checkbox.set_text("Show debug information".to_string());

        Self {
            container,
            primary_button,
            secondary_button,
            disabled_button,
            toggle_button,
            settings_checkbox,
            feature_checkbox,
            debug_checkbox,
        }
    }
}

impl TextInputTab {
    /// ### Creates new text input tab.
    pub fn new() -> Self {
        let container = VBoxContainer::new("TextInputContainer");

        let mut name_input = LineEdit::new("NameInput");
        name_input.set_placeholder_text("Enter your name".to_string());
        name_input.set_max_length(50);

        let mut email_input = LineEdit::new("EmailInput");
        email_input.set_placeholder_text("Enter your email".to_string());
        email_input.set_max_length(100);

        let mut password_input = LineEdit::new("PasswordInput");
        password_input.set_placeholder_text("Enter password".to_string());
        password_input.set_secret(true);
        password_input.set_max_length(50);

        let mut search_input = LineEdit::new("SearchInput");
        search_input.set_placeholder_text("Search...".to_string());
        search_input.set_max_length(200);

        let mut text_area = LineEdit::new("TextArea");
        text_area.set_placeholder_text("Enter multiline text here...".to_string());
        text_area.set_max_length(1000);

        let mut name_label = Label::new("NameLabel");
        name_label.set_text(verturion::core::variant::String::from("Name:"));

        let mut email_label = Label::new("EmailLabel");
        email_label.set_text(verturion::core::variant::String::from("Email:"));

        let mut password_label = Label::new("PasswordLabel");
        password_label.set_text(verturion::core::variant::String::from("Password:"));

        let mut search_label = Label::new("SearchLabel");
        search_label.set_text(verturion::core::variant::String::from("Search:"));

        let mut text_area_label = Label::new("TextAreaLabel");
        text_area_label.set_text(verturion::core::variant::String::from("Text Area:"));

        Self {
            container,
            name_input,
            email_input,
            password_input,
            search_input,
            text_area,
            name_label,
            email_label,
            password_label,
            search_label,
            text_area_label,
        }
    }
}

impl ProgressFeedbackTab {
    /// ### Creates new progress feedback tab.
    pub fn new() -> Self {
        let container = VBoxContainer::new("ProgressFeedbackContainer");

        let mut loading_progress = ProgressBar::new("LoadingProgress");
        loading_progress.set_min_value(0.0, &mut SignalManager::new());
        loading_progress.set_max_value(100.0, &mut SignalManager::new());
        loading_progress.set_value(0.0, &mut SignalManager::new());

        let mut health_progress = ProgressBar::new("HealthProgress");
        health_progress.set_min_value(0.0, &mut SignalManager::new());
        health_progress.set_max_value(100.0, &mut SignalManager::new());
        health_progress.set_value(100.0, &mut SignalManager::new());

        let mut experience_progress = ProgressBar::new("ExperienceProgress");
        experience_progress.set_min_value(0.0, &mut SignalManager::new());
        experience_progress.set_max_value(100.0, &mut SignalManager::new());
        experience_progress.set_value(0.0, &mut SignalManager::new());

        let mut download_progress = ProgressBar::new("DownloadProgress");
        download_progress.set_min_value(0.0, &mut SignalManager::new());
        download_progress.set_max_value(100.0, &mut SignalManager::new());
        download_progress.set_value(0.0, &mut SignalManager::new());

        let mut status_label = Label::new("StatusLabel");
        status_label.set_text(verturion::core::variant::String::from("Status: Ready"));

        let mut loading_label = Label::new("LoadingLabel");
        loading_label.set_text(verturion::core::variant::String::from("Loading Progress:"));

        let mut health_label = Label::new("HealthLabel");
        health_label.set_text(verturion::core::variant::String::from("Health:"));

        let mut experience_label = Label::new("ExperienceLabel");
        experience_label.set_text(verturion::core::variant::String::from("Experience:"));

        let mut download_label = Label::new("DownloadLabel");
        download_label.set_text(verturion::core::variant::String::from("Download:"));

        Self {
            container,
            loading_progress,
            health_progress,
            experience_progress,
            download_progress,
            status_label,
            loading_label,
            health_label,
            experience_label,
            download_label,
        }
    }
}

impl LayoutContainersTab {
    /// ### Creates new layout containers tab.
    pub fn new() -> Self {
        let container = VBoxContainer::new("LayoutContainersContainer");
        let hbox_example = HBoxContainer::new("HBoxExample");
        let vbox_example = VBoxContainer::new("VBoxExample");
        let nested_container = VBoxContainer::new("NestedContainer");

        let mut hbox_buttons = Vec::new();
        for i in 0..3 {
            let mut button = Button::new(&format!("HBoxButton{}", i));
            button.set_text(verturion::core::variant::String::from(format!("HBox {}", i + 1)));
            hbox_buttons.push(button);
        }

        let mut vbox_buttons = Vec::new();
        for i in 0..3 {
            let mut button = Button::new(&format!("VBoxButton{}", i));
            button.set_text(verturion::core::variant::String::from(format!("VBox {}", i + 1)));
            vbox_buttons.push(button);
        }

        let mut hbox_label = Label::new("HBoxLabel");
        hbox_label.set_text(verturion::core::variant::String::from("Horizontal Layout:"));

        let mut vbox_label = Label::new("VBoxLabel");
        vbox_label.set_text(verturion::core::variant::String::from("Vertical Layout:"));

        let mut nested_label = Label::new("NestedLabel");
        nested_label.set_text(verturion::core::variant::String::from("Nested Containers:"));

        Self {
            container,
            hbox_example,
            vbox_example,
            nested_container,
            hbox_buttons,
            vbox_buttons,
            hbox_label,
            vbox_label,
            nested_label,
        }
    }
}

impl EssentialNodesTab {
    /// ### Creates new essential nodes tab.
    pub fn new() -> Self {
        let container = VBoxContainer::new("EssentialNodesContainer");

        let mut demo_timer = Timer::new("DemoTimer");
        demo_timer.set_wait_time(5.0);
        demo_timer.set_autostart(false);

        let audio_player = AudioStreamPlayer2D::new("DemoAudio");
        let camera = Camera2D::new("DemoCamera");
        let animation_player = AnimationPlayer::new("DemoAnimationPlayer");
        let static_body = StaticBody2D::new("DemoStaticBody");

        let mut timer_label = Label::new("TimerLabel");
        timer_label.set_text(verturion::core::variant::String::from("Timer: Stopped"));

        let mut audio_label = Label::new("AudioLabel");
        audio_label.set_text(verturion::core::variant::String::from("Audio Player: Ready"));

        let mut camera_label = Label::new("CameraLabel");
        camera_label.set_text(verturion::core::variant::String::from("Camera: Inactive"));

        let mut animation_label = Label::new("AnimationLabel");
        animation_label.set_text(verturion::core::variant::String::from("Animation Player: Stopped"));

        let mut physics_label = Label::new("PhysicsLabel");
        physics_label.set_text(verturion::core::variant::String::from("Static Body: Ready"));

        Self {
            container,
            demo_timer,
            audio_player,
            camera,
            animation_player,
            static_body,
            timer_label,
            audio_label,
            camera_label,
            animation_label,
            physics_label,
        }
    }
}

impl ApplicationHandler for ComprehensiveUITest {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.renderer.is_none() {
            // Initialize on first resume
            pollster::block_on(async {
                if let Err(e) = self.initialize(event_loop).await {
                    eprintln!("Failed to initialize comprehensive UI test: {}", e);
                    event_loop.exit();
                }
            });
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        self.handle_window_event(&event);

        if self.should_exit() {
            event_loop.exit();
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        // Update and render
        self.update(1.0 / 60.0); // Assume 60 FPS for now

        if let Err(e) = self.render() {
            eprintln!("Render error: {}", e);
        }

        // Request redraw
        if let Some(window) = self.window.window() {
            window.request_redraw();
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎮 Verturion Comprehensive UI Test - Starting...");
    println!("==============================================");
    println!("Controls:");
    println!("  ESC - Exit application");
    println!("  F1 - Toggle debug mode");
    println!("  F5 - Reset demo state");
    println!("  TAB - Switch between tabs");
    println!("  Mouse - Click tabs and interact with UI");
    println!("==============================================\n");

    let event_loop = EventLoop::new()?;
    event_loop.set_control_flow(ControlFlow::Poll);

    let mut app = ComprehensiveUITest::new();
    event_loop.run_app(&mut app)?;

    println!("\n🎮 Verturion Comprehensive UI Test - Exiting...");
    Ok(())
}
