//! Basic UI Visual Test Application
//!
//! This application demonstrates all implemented UI nodes in the Verturion game engine
//! with interactive functionality and real-time visual feedback.

use winit::{
    application::ApplicationHandler,
    event::{ElementState, WindowEvent},
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    keyboard::{Key<PERSON><PERSON>, PhysicalKey},
    window::WindowId,
};

use verturion::core::{
    renderer::{Window, WindowConfig, Renderer},
    scene::Node,
    scene::nodes::{
        Timer, Label, Button, LineEdit, ProgressBar, CheckBox,
    },
    scene::nodes::ui::label::TextAlign,
    signal::SignalManager,
    math::Vector2,
    variant::Color,
    input::{Input, InputMap, KeyCode as InputKeyCode},
};

/// ### Basic UI test application.
pub struct BasicUITest {
    /// Main window for rendering
    window: Window,
    /// Graphics renderer
    renderer: Option<Renderer>,
    /// Root scene node
    root_node: Option<Node>,
    /// Signal manager for events
    signal_manager: SignalManager,
    /// Input system
    input: Input,
    /// Input action mapping
    input_map: InputMap,
    /// UI test components
    ui_components: UIComponents,
    /// Whether the application should exit
    should_exit: bool,
    /// Frame counter for animations
    frame_counter: u64,
    /// Current demo tab (0-6 for enhanced text testing)
    current_tab: usize,
    /// Text testing components
    text_test_components: TextTestComponents,
}

/// ### Collection of text testing components for comprehensive Label and LineEdit testing.
#[derive(Debug, Clone)]
pub struct TextTestComponents {
    /// Static text labels with different content lengths
    pub short_label: Label,
    pub medium_label: Label,
    pub long_label: Label,
    pub multiline_label: Label,

    /// Dynamic text labels for runtime updates
    pub dynamic_counter_label: Label,
    pub dynamic_time_label: Label,
    pub dynamic_status_label: Label,

    /// Styled text labels for formatting tests
    pub styled_title_label: Label,
    pub styled_subtitle_label: Label,
    pub styled_warning_label: Label,

    /// Text input fields with different configurations
    pub basic_input: LineEdit,
    pub placeholder_input: LineEdit,
    pub limited_input: LineEdit,
    pub password_input: LineEdit,
    pub validated_input: LineEdit,

    /// Performance testing text components
    pub performance_labels: Vec<Label>,
    pub performance_inputs: Vec<LineEdit>,

    /// Text content for dynamic updates
    pub counter_value: i32,
    pub last_update_time: std::time::Instant,
    pub status_messages: Vec<String>,
    pub current_status_index: usize,
}

/// ### Collection of UI test components.
#[derive(Debug, Clone)]
pub struct UIComponents {
    /// Basic UI controls
    pub primary_button: Button,
    pub secondary_button: Button,
    pub disabled_button: Button,
    
    /// Text input controls
    pub name_input: LineEdit,
    pub email_input: LineEdit,
    
    /// Progress and feedback
    pub loading_progress: ProgressBar,
    pub health_progress: ProgressBar,
    
    /// Checkboxes
    pub settings_checkbox: CheckBox,
    pub feature_checkbox: CheckBox,
    
    /// Labels
    pub title_label: Label,
    pub status_label: Label,
    
    /// Essential nodes
    pub demo_timer: Timer,
}

impl UIComponents {
    /// ### Creates new UI components.
    pub fn new() -> Self {
        // Create buttons
        let mut primary_button = Button::new("PrimaryButton");
        primary_button.set_text(verturion::core::variant::String::from("Primary Action"));
        
        let mut secondary_button = Button::new("SecondaryButton");
        secondary_button.set_text(verturion::core::variant::String::from("Secondary Action"));
        
        let mut disabled_button = Button::new("DisabledButton");
        disabled_button.set_text(verturion::core::variant::String::from("Disabled Button"));
        disabled_button.set_disabled(true);
        
        // Create text inputs
        let mut name_input = LineEdit::new("NameInput");
        name_input.set_placeholder_text("Enter your name".to_string());
        name_input.set_max_length(50);
        
        let mut email_input = LineEdit::new("EmailInput");
        email_input.set_placeholder_text("Enter your email".to_string());
        email_input.set_max_length(100);
        
        // Create progress bars
        let mut loading_progress = ProgressBar::new("LoadingProgress");
        loading_progress.set_min_value(0.0, &mut SignalManager::new());
        loading_progress.set_max_value(100.0, &mut SignalManager::new());
        loading_progress.set_value(0.0, &mut SignalManager::new());
        
        let mut health_progress = ProgressBar::new("HealthProgress");
        health_progress.set_min_value(0.0, &mut SignalManager::new());
        health_progress.set_max_value(100.0, &mut SignalManager::new());
        health_progress.set_value(100.0, &mut SignalManager::new());
        
        // Create checkboxes
        let mut settings_checkbox = CheckBox::new("SettingsCheckBox");
        settings_checkbox.set_text("Enable advanced settings".to_string());
        
        let mut feature_checkbox = CheckBox::new("FeatureCheckBox");
        feature_checkbox.set_text("Enable experimental features".to_string());
        
        // Create labels
        let mut title_label = Label::new("TitleLabel");
        title_label.set_text(verturion::core::variant::String::from("Verturion UI Test"));
        
        let mut status_label = Label::new("StatusLabel");
        status_label.set_text(verturion::core::variant::String::from("Status: Ready"));
        
        // Create essential nodes
        let mut demo_timer = Timer::new("DemoTimer");
        demo_timer.set_wait_time(5.0);
        demo_timer.set_autostart(false);
        
        Self {
            primary_button,
            secondary_button,
            disabled_button,
            name_input,
            email_input,
            loading_progress,
            health_progress,
            settings_checkbox,
            feature_checkbox,
            title_label,
            status_label,
            demo_timer,
        }
    }

    /// ### Updates UI components.
    pub fn update(&mut self, delta_time: f32, signal_manager: &mut SignalManager) {
        // Update timer
        self.demo_timer.update(delta_time as f64, signal_manager);

        // Animate progress bars
        let time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f32();

        // Loading progress (0-100% cycle)
        let loading_value = ((time * 0.5).sin() * 0.5 + 0.5) * 100.0;
        self.loading_progress.set_value(loading_value as f64, signal_manager);

        // Health progress (slowly decreasing)
        let current_health = self.health_progress.get_value();
        if current_health > 0.0 {
            let new_health = (current_health - delta_time as f64 * 5.0).max(0.0);
            self.health_progress.set_value(new_health, signal_manager);
        }
    }
}

impl TextTestComponents {
    /// ### Creates new text testing components with comprehensive Label and LineEdit examples.
    pub fn new() -> Self {
        // Create static text labels with different content lengths
        let mut short_label = Label::new("ShortLabel");
        short_label.set_text(verturion::core::variant::String::from("Short"));
        short_label.set_font_size(16);
        short_label.set_font_color(verturion::core::variant::Color::new(1.0, 1.0, 1.0, 1.0));

        let mut medium_label = Label::new("MediumLabel");
        medium_label.set_text(verturion::core::variant::String::from("This is a medium length text label for testing"));
        medium_label.set_font_size(14);
        medium_label.set_font_color(verturion::core::variant::Color::new(0.9, 0.9, 0.9, 1.0));

        let mut long_label = Label::new("LongLabel");
        long_label.set_text(verturion::core::variant::String::from("This is a very long text label that demonstrates how the Label component handles extended text content and word wrapping capabilities when the text exceeds the available display area"));
        long_label.set_font_size(12);
        long_label.set_autowrap_mode(true);
        long_label.set_font_color(verturion::core::variant::Color::new(0.8, 0.8, 0.8, 1.0));

        let mut multiline_label = Label::new("MultilineLabel");
        multiline_label.set_text(verturion::core::variant::String::from("Line 1: First line of text\nLine 2: Second line of text\nLine 3: Third line of text\nLine 4: Fourth line of text"));
        multiline_label.set_font_size(13);
        multiline_label.set_autowrap_mode(true);
        multiline_label.set_font_color(verturion::core::variant::Color::new(0.7, 0.9, 0.7, 1.0));

        // Create dynamic text labels for runtime updates
        let mut dynamic_counter_label = Label::new("CounterLabel");
        dynamic_counter_label.set_text(verturion::core::variant::String::from("Counter: 0"));
        dynamic_counter_label.set_font_size(18);
        dynamic_counter_label.set_font_color(verturion::core::variant::Color::new(0.0, 1.0, 0.0, 1.0));

        let mut dynamic_time_label = Label::new("TimeLabel");
        dynamic_time_label.set_text(verturion::core::variant::String::from("Time: 00:00:00"));
        dynamic_time_label.set_font_size(16);
        dynamic_time_label.set_font_color(verturion::core::variant::Color::new(0.0, 0.8, 1.0, 1.0));

        let mut dynamic_status_label = Label::new("StatusLabel");
        dynamic_status_label.set_text(verturion::core::variant::String::from("Status: Initializing..."));
        dynamic_status_label.set_font_size(14);
        dynamic_status_label.set_font_color(verturion::core::variant::Color::new(1.0, 1.0, 0.0, 1.0));

        // Create styled text labels for formatting tests
        let mut styled_title_label = Label::new("TitleLabel");
        styled_title_label.set_text(verturion::core::variant::String::from("🎮 VERTURION TEXT TESTING"));
        styled_title_label.set_font_size(24);
        styled_title_label.set_text_align(TextAlign::Center);
        styled_title_label.set_font_color(verturion::core::variant::Color::new(1.0, 0.8, 0.0, 1.0));

        let mut styled_subtitle_label = Label::new("SubtitleLabel");
        styled_subtitle_label.set_text(verturion::core::variant::String::from("Comprehensive Label & LineEdit Component Testing"));
        styled_subtitle_label.set_font_size(16);
        styled_subtitle_label.set_text_align(TextAlign::Center);
        styled_subtitle_label.set_font_color(verturion::core::variant::Color::new(0.8, 0.8, 1.0, 1.0));

        let mut styled_warning_label = Label::new("WarningLabel");
        styled_warning_label.set_text(verturion::core::variant::String::from("⚠️ WARNING: Performance testing in progress"));
        styled_warning_label.set_font_size(14);
        styled_warning_label.set_font_color(verturion::core::variant::Color::new(1.0, 0.5, 0.0, 1.0));

        // Create text input fields with different configurations
        let mut basic_input = LineEdit::new("BasicInput");
        basic_input.set_text("Basic text input".to_string());
        basic_input.set_max_length(100);

        let mut placeholder_input = LineEdit::new("PlaceholderInput");
        placeholder_input.set_placeholder_text("Enter your message here...".to_string());
        placeholder_input.set_max_length(200);

        let mut limited_input = LineEdit::new("LimitedInput");
        limited_input.set_placeholder_text("Max 20 chars".to_string());
        limited_input.set_max_length(20);

        let mut password_input = LineEdit::new("PasswordInput");
        password_input.set_placeholder_text("Enter password".to_string());
        password_input.set_secret(true);
        password_input.set_max_length(50);

        let mut validated_input = LineEdit::new("ValidatedInput");
        validated_input.set_placeholder_text("Numbers only (0-9)".to_string());
        validated_input.set_max_length(10);

        // Create performance testing components
        let mut performance_labels = Vec::new();
        let mut performance_inputs = Vec::new();

        for i in 0..20 {
            let mut perf_label = Label::new(&format!("PerfLabel{}", i));
            perf_label.set_text(verturion::core::variant::String::from(format!("Performance Label #{}", i + 1)));
            perf_label.set_font_size(12);
            perf_label.set_font_color(verturion::core::variant::Color::new(0.6, 0.6, 0.8, 1.0));
            performance_labels.push(perf_label);

            let mut perf_input = LineEdit::new(&format!("PerfInput{}", i));
            perf_input.set_placeholder_text(format!("Input #{}", i + 1));
            perf_input.set_max_length(50);
            performance_inputs.push(perf_input);
        }

        // Initialize status messages for dynamic updates
        let status_messages = vec![
            "Status: Ready".to_string(),
            "Status: Processing...".to_string(),
            "Status: Loading data...".to_string(),
            "Status: Rendering text...".to_string(),
            "Status: Optimizing performance...".to_string(),
            "Status: Testing complete".to_string(),
        ];

        Self {
            short_label,
            medium_label,
            long_label,
            multiline_label,
            dynamic_counter_label,
            dynamic_time_label,
            dynamic_status_label,
            styled_title_label,
            styled_subtitle_label,
            styled_warning_label,
            basic_input,
            placeholder_input,
            limited_input,
            password_input,
            validated_input,
            performance_labels,
            performance_inputs,
            counter_value: 0,
            last_update_time: std::time::Instant::now(),
            status_messages,
            current_status_index: 0,
        }
    }

    /// ### Updates text testing components with dynamic content.
    pub fn update(&mut self, _delta_time: f32, _signal_manager: &mut SignalManager) {
        let now = std::time::Instant::now();

        // Update counter every second
        if now.duration_since(self.last_update_time).as_secs_f32() >= 1.0 {
            self.counter_value += 1;
            self.dynamic_counter_label.set_text(verturion::core::variant::String::from(format!("Counter: {}", self.counter_value)));

            // Update time display
            let elapsed = now.duration_since(self.last_update_time);
            let total_seconds = elapsed.as_secs();
            let hours = total_seconds / 3600;
            let minutes = (total_seconds % 3600) / 60;
            let seconds = total_seconds % 60;
            self.dynamic_time_label.set_text(verturion::core::variant::String::from(format!("Time: {:02}:{:02}:{:02}", hours, minutes, seconds)));

            // Cycle through status messages
            self.current_status_index = (self.current_status_index + 1) % self.status_messages.len();
            self.dynamic_status_label.set_text(verturion::core::variant::String::from(self.status_messages[self.current_status_index].clone()));

            self.last_update_time = now;
        }

        // Update performance labels with animated content
        let time_factor = (now.duration_since(self.last_update_time).as_secs_f32() * 2.0).sin();
        for (i, label) in self.performance_labels.iter_mut().enumerate() {
            let animated_value = ((time_factor + i as f32 * 0.1) * 10.0) as i32;
            label.set_text(verturion::core::variant::String::from(format!("Perf #{}: {}", i + 1, animated_value)));
        }
    }

    /// ### Gets the total number of text components for performance metrics.
    pub fn get_text_component_count(&self) -> u32 {
        let static_labels = 10; // short, medium, long, multiline, dynamic_counter, dynamic_time, dynamic_status, styled_title, styled_subtitle, styled_warning
        let input_fields = 5; // basic, placeholder, limited, password, validated
        let performance_components = self.performance_labels.len() + self.performance_inputs.len();

        (static_labels + input_fields + performance_components) as u32
    }
}

impl BasicUITest {
    /// ### Creates a new basic UI test application.
    pub fn new() -> Self {
        let window_config = WindowConfig {
            title: "Verturion Enhanced UI Test - Label & LineEdit Testing".to_string(),
            width: 1200,
            height: 800,
            resizable: true,
            vsync: true,
            clear_color: [0.15, 0.15, 0.2, 1.0],
        };

        let window = Window::new(window_config);
        let signal_manager = SignalManager::new();
        let input = Input::new();
        let mut input_map = InputMap::new();

        // Set up input actions
        input_map.add_action("ui_cancel", InputKeyCode::Escape);
        input_map.add_action("reset_demo", InputKeyCode::F5);
        input_map.add_action("next_tab", InputKeyCode::Tab);

        let ui_components = UIComponents::new();
        let text_test_components = TextTestComponents::new();

        Self {
            window,
            renderer: None,
            root_node: None,
            signal_manager,
            input,
            input_map,
            ui_components,
            should_exit: false,
            frame_counter: 0,
            current_tab: 0,
            text_test_components,
        }
    }

    /// ### Initializes the basic UI test.
    pub async fn initialize(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        // Initialize window
        self.window.initialize(event_loop).await?;

        // Create renderer
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        let renderer = Renderer::new(device, queue, config.format).await?;
        self.renderer = Some(renderer);

        // Create root scene node
        let root = Node::new("UITestRoot");
        self.root_node = Some(root);

        // Initialize UI components
        self.signal_manager.register_signal(self.ui_components.demo_timer.get_timeout_signal().clone());
        self.signal_manager.register_signal(self.ui_components.primary_button.get_pressed_signal().clone());
        self.signal_manager.register_signal(self.ui_components.settings_checkbox.get_toggled_signal().clone());
        
        // Start demo timer
        self.ui_components.demo_timer.start(&mut self.signal_manager);

        println!("🎮 Basic UI Test initialized successfully!");
        println!("Window size: {}x{}", config.width, config.height);

        Ok(())
    }

    /// ### Updates the test application state.
    pub fn update(&mut self, delta_time: f32) {
        self.frame_counter += 1;

        // Update input system
        self.input.update(&self.input_map, (delta_time * 1000.0) as u64);

        // Update UI components
        self.ui_components.update(delta_time, &mut self.signal_manager);

        // Handle input actions
        if self.input.is_action_just_pressed("ui_cancel") {
            self.should_exit = true;
        }

        if self.input.is_action_just_pressed("reset_demo") {
            self.reset_demo_state();
        }

        if self.input.is_action_just_pressed("next_tab") {
            self.current_tab = (self.current_tab + 1) % 7; // Updated for 7 tabs including text testing
            println!("Switched to tab {}", self.current_tab);
        }

        // Update text testing components
        self.text_test_components.update(delta_time, &mut self.signal_manager);
    }

    /// ### Resets the demo to initial state.
    fn reset_demo_state(&mut self) {
        // Reset progress bars
        self.ui_components.loading_progress.set_value(0.0, &mut self.signal_manager);
        self.ui_components.health_progress.set_value(100.0, &mut self.signal_manager);

        // Reset checkboxes
        self.ui_components.settings_checkbox.set_checked(false, &mut self.signal_manager);
        self.ui_components.feature_checkbox.set_checked(false, &mut self.signal_manager);

        // Clear text inputs
        self.ui_components.name_input.set_text("".to_string());
        self.ui_components.email_input.set_text("".to_string());

        // Reset timer
        self.ui_components.demo_timer.stop();
        self.ui_components.demo_timer.set_wait_time(5.0);
        self.ui_components.demo_timer.start(&mut self.signal_manager);

        println!("Demo state reset to initial values");
    }

    /// ### Renders the basic UI test.
    pub fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Get rendering resources
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        // Begin frame
        let surface_texture = self.window.begin_frame().ok_or("Failed to get surface texture")?;
        let view = surface_texture.texture.create_view(&wgpu::TextureViewDescriptor::default());

        // Create command encoder
        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some("UI Test Render Encoder"),
        });

        // Store window size for rendering
        let window_size = Vector2::new(config.width as f32, config.height as f32);

        // Begin render pass
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("UI Test Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.15,
                            g: 0.15,
                            b: 0.2,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Get renderer and clear UI renderer for new frame
            let renderer = self.renderer.as_mut().ok_or("No renderer available")?;
            let ui_renderer = renderer.ui_renderer_mut();
            ui_renderer.clear();

            // Store current tab to avoid borrowing issues
            let current_tab = self.current_tab;

            // Render UI content based on current tab
            match current_tab {
                0 => {
                    // Buttons tab
                    let title_rect = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 10.0),
                        Vector2::new(window_size.x - 20.0, 40.0)
                    );
                    ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.3, 1.0));

                    let button_area = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 60.0),
                        Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
                    );
                    ui_renderer.add_quad(button_area, Color::new(0.15, 0.15, 0.2, 0.9));

                    // Render buttons
                    let button_width = 150.0;
                    let button_height = 40.0;
                    let button_spacing = 60.0;
                    let start_x = 50.0;
                    let start_y = 100.0;

                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y),
                            Vector2::new(button_width, button_height)
                        ),
                        Color::new(0.2, 0.7, 0.2, 1.0)
                    );

                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + button_spacing),
                            Vector2::new(button_width, button_height)
                        ),
                        Color::new(0.2, 0.2, 0.7, 1.0)
                    );

                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + button_spacing * 2.0),
                            Vector2::new(button_width, button_height)
                        ),
                        Color::new(0.3, 0.3, 0.3, 1.0)
                    );
                }
                1 => {
                    // Text inputs tab
                    let title_rect = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 10.0),
                        Vector2::new(window_size.x - 20.0, 40.0)
                    );
                    ui_renderer.add_quad(title_rect, Color::new(0.2, 0.3, 0.2, 1.0));

                    let input_area = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 60.0),
                        Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
                    );
                    ui_renderer.add_quad(input_area, Color::new(0.15, 0.2, 0.15, 0.9));

                    // Render text inputs
                    let input_width = 300.0;
                    let input_height = 30.0;
                    let input_spacing = 60.0;
                    let start_x = 50.0;
                    let start_y = 100.0;

                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y),
                            Vector2::new(input_width, input_height)
                        ),
                        Color::new(0.9, 0.9, 0.9, 1.0)
                    );

                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + input_spacing),
                            Vector2::new(input_width, input_height)
                        ),
                        Color::new(0.9, 0.9, 0.9, 1.0)
                    );
                }
                2 => {
                    // Progress tab
                    let title_rect = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 10.0),
                        Vector2::new(window_size.x - 20.0, 40.0)
                    );
                    ui_renderer.add_quad(title_rect, Color::new(0.3, 0.2, 0.2, 1.0));

                    let progress_area = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 60.0),
                        Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
                    );
                    ui_renderer.add_quad(progress_area, Color::new(0.2, 0.15, 0.15, 0.9));

                    // Render animated progress bars
                    let progress_width = 400.0;
                    let progress_height = 20.0;
                    let progress_spacing = 60.0;
                    let start_x = 50.0;
                    let start_y = 100.0;

                    // Loading progress
                    let loading_value = self.ui_components.loading_progress.get_value() as f32 / 100.0;
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y),
                            Vector2::new(progress_width, progress_height)
                        ),
                        Color::new(0.2, 0.2, 0.2, 1.0)
                    );
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y),
                            Vector2::new(progress_width * loading_value, progress_height)
                        ),
                        Color::new(0.0, 0.8, 0.4, 1.0)
                    );

                    // Health progress
                    let health_value = self.ui_components.health_progress.get_value() as f32 / 100.0;
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + progress_spacing),
                            Vector2::new(progress_width, progress_height)
                        ),
                        Color::new(0.2, 0.2, 0.2, 1.0)
                    );
                    let health_color = if health_value > 0.5 {
                        Color::new(0.0, 0.8, 0.0, 1.0)
                    } else if health_value > 0.2 {
                        Color::new(0.8, 0.8, 0.0, 1.0)
                    } else {
                        Color::new(0.8, 0.0, 0.0, 1.0)
                    };
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + progress_spacing),
                            Vector2::new(progress_width * health_value, progress_height)
                        ),
                        health_color
                    );
                }
                3 => {
                    // Static Labels tab
                    let title_rect = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 10.0),
                        Vector2::new(window_size.x - 20.0, 40.0)
                    );
                    ui_renderer.add_quad(title_rect, Color::new(0.2, 0.4, 0.2, 1.0));

                    let label_area = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 60.0),
                        Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
                    );
                    ui_renderer.add_quad(label_area, Color::new(0.15, 0.25, 0.15, 0.9));

                    // Render static labels with different sizes and colors
                    let label_width = 400.0;
                    let label_height = 25.0;
                    let label_spacing = 35.0;
                    let start_x = 50.0;
                    let start_y = 100.0;

                    // Short label (white)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y),
                            Vector2::new(100.0, label_height)
                        ),
                        Color::new(1.0, 1.0, 1.0, 1.0)
                    );

                    // Medium label (light gray)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + label_spacing),
                            Vector2::new(label_width, label_height)
                        ),
                        Color::new(0.9, 0.9, 0.9, 1.0)
                    );

                    // Long label (gray, wrapped)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + label_spacing * 2.0),
                            Vector2::new(label_width, label_height * 3.0)
                        ),
                        Color::new(0.8, 0.8, 0.8, 1.0)
                    );

                    // Multiline label (light green)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + label_spacing * 5.0),
                            Vector2::new(label_width, label_height * 4.0)
                        ),
                        Color::new(0.7, 0.9, 0.7, 1.0)
                    );
                }
                4 => {
                    // Dynamic Labels tab
                    let title_rect = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 10.0),
                        Vector2::new(window_size.x - 20.0, 40.0)
                    );
                    ui_renderer.add_quad(title_rect, Color::new(0.4, 0.2, 0.4, 1.0));

                    let dynamic_area = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 60.0),
                        Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
                    );
                    ui_renderer.add_quad(dynamic_area, Color::new(0.25, 0.15, 0.25, 0.9));

                    // Render dynamic labels with animated colors
                    let label_width = 300.0;
                    let label_height = 30.0;
                    let label_spacing = 50.0;
                    let start_x = 50.0;
                    let start_y = 100.0;

                    // Counter label (green)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y),
                            Vector2::new(label_width, label_height)
                        ),
                        Color::new(0.0, 1.0, 0.0, 1.0)
                    );

                    // Time label (cyan)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + label_spacing),
                            Vector2::new(label_width, label_height)
                        ),
                        Color::new(0.0, 0.8, 1.0, 1.0)
                    );

                    // Status label (yellow)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + label_spacing * 2.0),
                            Vector2::new(label_width, label_height)
                        ),
                        Color::new(1.0, 1.0, 0.0, 1.0)
                    );
                }
                5 => {
                    // LineEdit Input tab
                    let title_rect = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 10.0),
                        Vector2::new(window_size.x - 20.0, 40.0)
                    );
                    ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.4, 1.0));

                    let input_area = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 60.0),
                        Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
                    );
                    ui_renderer.add_quad(input_area, Color::new(0.15, 0.15, 0.25, 0.9));

                    // Render text input fields
                    let input_width = 350.0;
                    let input_height = 35.0;
                    let input_spacing = 50.0;
                    let start_x = 50.0;
                    let start_y = 100.0;

                    // Basic input (white)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y),
                            Vector2::new(input_width, input_height)
                        ),
                        Color::new(1.0, 1.0, 1.0, 1.0)
                    );

                    // Placeholder input (light blue)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + input_spacing),
                            Vector2::new(input_width, input_height)
                        ),
                        Color::new(0.9, 0.9, 1.0, 1.0)
                    );

                    // Limited input (yellow)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + input_spacing * 2.0),
                            Vector2::new(input_width * 0.6, input_height)
                        ),
                        Color::new(1.0, 1.0, 0.8, 1.0)
                    );

                    // Password input (pink)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + input_spacing * 3.0),
                            Vector2::new(input_width, input_height)
                        ),
                        Color::new(1.0, 0.9, 0.9, 1.0)
                    );

                    // Validated input (light green)
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(start_x, start_y + input_spacing * 4.0),
                            Vector2::new(input_width * 0.4, input_height)
                        ),
                        Color::new(0.9, 1.0, 0.9, 1.0)
                    );
                }
                6 => {
                    // Performance Text Testing tab
                    let title_rect = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 10.0),
                        Vector2::new(window_size.x - 20.0, 40.0)
                    );
                    ui_renderer.add_quad(title_rect, Color::new(0.4, 0.2, 0.2, 1.0));

                    let perf_area = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 60.0),
                        Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
                    );
                    ui_renderer.add_quad(perf_area, Color::new(0.25, 0.15, 0.15, 0.9));

                    // Render performance testing components in a grid
                    let element_width = 120.0;
                    let element_height = 20.0;
                    let spacing_x = 130.0;
                    let spacing_y = 25.0;
                    let start_x = 20.0;
                    let start_y = 80.0;
                    let elements_per_row = ((window_size.x - 40.0) / spacing_x) as usize;

                    // Render performance labels and inputs
                    for i in 0..20 {
                        let row = i / elements_per_row;
                        let col = i % elements_per_row;
                        let x = start_x + (col as f32 * spacing_x);
                        let y = start_y + (row as f32 * spacing_y * 2.0);

                        // Performance label (blue)
                        ui_renderer.add_quad(
                            verturion::core::math::Rect2::from_position_size(
                                Vector2::new(x, y),
                                Vector2::new(element_width, element_height)
                            ),
                            Color::new(0.6, 0.6, 0.8, 1.0)
                        );

                        // Performance input (light gray)
                        ui_renderer.add_quad(
                            verturion::core::math::Rect2::from_position_size(
                                Vector2::new(x, y + spacing_y),
                                Vector2::new(element_width, element_height)
                            ),
                            Color::new(0.8, 0.8, 0.8, 1.0)
                        );
                    }
                }
                _ => {
                    // Default to simple colored background
                    let default_rect = verturion::core::math::Rect2::from_position_size(
                        Vector2::new(10.0, 60.0),
                        Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
                    );
                    ui_renderer.add_quad(default_rect, Color::new(0.2, 0.2, 0.2, 0.9));
                }
            }

            // Render tab indicator
            let indicator_y = window_size.y - 50.0;
            let tab_width = 80.0;
            let tab_height = 30.0;
            let start_x = 10.0;

            for i in 0..7 {
                let tab_x = start_x + (i as f32 * (tab_width + 5.0));
                let tab_color = if i == current_tab {
                    Color::new(0.5, 0.5, 0.7, 1.0) // Active tab
                } else {
                    Color::new(0.3, 0.3, 0.3, 1.0) // Inactive tab
                };

                ui_renderer.add_quad(
                    verturion::core::math::Rect2::from_position_size(
                        Vector2::new(tab_x, indicator_y),
                        Vector2::new(tab_width, tab_height)
                    ),
                    tab_color
                );
            }

            // Flush UI rendering
            ui_renderer.flush(&mut render_pass, queue, window_size)?;
        }

        // Submit commands
        queue.submit(std::iter::once(encoder.finish()));
        surface_texture.present();

        Ok(())
    }

    /// ### Renders the buttons tab.
    fn render_buttons_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Title
        let title_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 10.0),
            Vector2::new(window_size.x - 20.0, 40.0)
        );
        ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.3, 1.0));

        // Button area
        let button_area = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 60.0),
            Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
        );
        ui_renderer.add_quad(button_area, Color::new(0.15, 0.15, 0.2, 0.9));

        // Render buttons as colored rectangles
        let button_width = 150.0;
        let button_height = 40.0;
        let button_spacing = 60.0;
        let start_x = 50.0;
        let start_y = 100.0;

        // Primary button (green)
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y),
                Vector2::new(button_width, button_height)
            ),
            Color::new(0.2, 0.7, 0.2, 1.0)
        );

        // Secondary button (blue)
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y + button_spacing),
                Vector2::new(button_width, button_height)
            ),
            Color::new(0.2, 0.2, 0.7, 1.0)
        );

        // Disabled button (gray)
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y + button_spacing * 2.0),
                Vector2::new(button_width, button_height)
            ),
            Color::new(0.3, 0.3, 0.3, 1.0)
        );

        Ok(())
    }

    /// ### Renders the text inputs tab.
    fn render_text_inputs_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Title
        let title_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 10.0),
            Vector2::new(window_size.x - 20.0, 40.0)
        );
        ui_renderer.add_quad(title_rect, Color::new(0.2, 0.3, 0.2, 1.0));

        // Input area
        let input_area = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 60.0),
            Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
        );
        ui_renderer.add_quad(input_area, Color::new(0.15, 0.2, 0.15, 0.9));

        // Render text inputs as rectangles
        let input_width = 300.0;
        let input_height = 30.0;
        let input_spacing = 60.0;
        let start_x = 50.0;
        let start_y = 100.0;

        // Name input
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y),
                Vector2::new(input_width, input_height)
            ),
            Color::new(0.9, 0.9, 0.9, 1.0)
        );

        // Email input
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y + input_spacing),
                Vector2::new(input_width, input_height)
            ),
            Color::new(0.9, 0.9, 0.9, 1.0)
        );

        Ok(())
    }

    /// ### Renders the progress tab.
    fn render_progress_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Title
        let title_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 10.0),
            Vector2::new(window_size.x - 20.0, 40.0)
        );
        ui_renderer.add_quad(title_rect, Color::new(0.3, 0.2, 0.2, 1.0));

        // Progress area
        let progress_area = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 60.0),
            Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
        );
        ui_renderer.add_quad(progress_area, Color::new(0.2, 0.15, 0.15, 0.9));

        // Render progress bars
        let progress_width = 400.0;
        let progress_height = 20.0;
        let progress_spacing = 60.0;
        let start_x = 50.0;
        let start_y = 100.0;

        // Loading progress bar
        let loading_value = self.ui_components.loading_progress.get_value() as f32 / 100.0;

        // Background
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y),
                Vector2::new(progress_width, progress_height)
            ),
            Color::new(0.2, 0.2, 0.2, 1.0)
        );

        // Fill
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y),
                Vector2::new(progress_width * loading_value, progress_height)
            ),
            Color::new(0.0, 0.8, 0.4, 1.0)
        );

        // Health progress bar
        let health_value = self.ui_components.health_progress.get_value() as f32 / 100.0;

        // Background
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y + progress_spacing),
                Vector2::new(progress_width, progress_height)
            ),
            Color::new(0.2, 0.2, 0.2, 1.0)
        );

        // Fill (red to green based on health)
        let health_color = if health_value > 0.5 {
            Color::new(0.0, 0.8, 0.0, 1.0) // Green
        } else if health_value > 0.2 {
            Color::new(0.8, 0.8, 0.0, 1.0) // Yellow
        } else {
            Color::new(0.8, 0.0, 0.0, 1.0) // Red
        };

        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y + progress_spacing),
                Vector2::new(progress_width * health_value, progress_height)
            ),
            health_color
        );

        Ok(())
    }

    /// ### Renders the checkboxes tab.
    fn render_checkboxes_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Title
        let title_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 10.0),
            Vector2::new(window_size.x - 20.0, 40.0)
        );
        ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.4, 1.0));

        // Checkbox area
        let checkbox_area = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 60.0),
            Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
        );
        ui_renderer.add_quad(checkbox_area, Color::new(0.15, 0.15, 0.25, 0.9));

        // Render checkboxes
        let checkbox_size = 20.0;
        let checkbox_spacing = 50.0;
        let start_x = 50.0;
        let start_y = 100.0;

        // Settings checkbox
        let settings_color = if self.ui_components.settings_checkbox.is_checked() {
            Color::new(0.0, 0.8, 0.0, 1.0) // Green when checked
        } else {
            Color::new(0.3, 0.3, 0.3, 1.0) // Gray when unchecked
        };
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y),
                Vector2::new(checkbox_size, checkbox_size)
            ),
            settings_color
        );

        // Feature checkbox
        let feature_color = if self.ui_components.feature_checkbox.is_checked() {
            Color::new(0.0, 0.8, 0.0, 1.0) // Green when checked
        } else {
            Color::new(0.3, 0.3, 0.3, 1.0) // Gray when unchecked
        };
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y + checkbox_spacing),
                Vector2::new(checkbox_size, checkbox_size)
            ),
            feature_color
        );

        Ok(())
    }

    /// ### Renders the labels tab.
    fn render_labels_tab(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Title
        let title_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 10.0),
            Vector2::new(window_size.x - 20.0, 40.0)
        );
        ui_renderer.add_quad(title_rect, Color::new(0.4, 0.2, 0.2, 1.0));

        // Label area
        let label_area = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 60.0),
            Vector2::new(window_size.x - 20.0, window_size.y - 120.0)
        );
        ui_renderer.add_quad(label_area, Color::new(0.25, 0.15, 0.15, 0.9));

        // Render label backgrounds
        let label_width = 300.0;
        let label_height = 30.0;
        let label_spacing = 50.0;
        let start_x = 50.0;
        let start_y = 100.0;

        // Title label
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y),
                Vector2::new(label_width, label_height)
            ),
            Color::new(0.8, 0.8, 0.9, 1.0)
        );

        // Status label
        ui_renderer.add_quad(
            verturion::core::math::Rect2::from_position_size(
                Vector2::new(start_x, start_y + label_spacing),
                Vector2::new(label_width, label_height)
            ),
            Color::new(0.9, 0.8, 0.8, 1.0)
        );

        Ok(())
    }

    /// ### Renders the tab indicator.
    fn render_tab_indicator(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let indicator_y = window_size.y - 50.0;
        let tab_width = 80.0;
        let tab_height = 30.0;
        let start_x = 10.0;

        for i in 0..5 {
            let tab_x = start_x + (i as f32 * (tab_width + 5.0));
            let tab_color = if i == self.current_tab {
                Color::new(0.5, 0.5, 0.7, 1.0) // Active tab
            } else {
                Color::new(0.3, 0.3, 0.3, 1.0) // Inactive tab
            };

            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(tab_x, indicator_y),
                    Vector2::new(tab_width, tab_height)
                ),
                tab_color
            );
        }

        Ok(())
    }

    /// ### Handles window events.
    pub fn handle_window_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                self.should_exit = true;
            }
            WindowEvent::Resized(physical_size) => {
                self.window.resize(*physical_size);
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state == ElementState::Pressed {
                    match event.physical_key {
                        PhysicalKey::Code(KeyCode::Escape) => {
                            self.should_exit = true;
                        }
                        PhysicalKey::Code(KeyCode::F5) => {
                            self.reset_demo_state();
                        }
                        PhysicalKey::Code(KeyCode::Tab) => {
                            self.current_tab = (self.current_tab + 1) % 5;
                            println!("Switched to tab {}", self.current_tab);
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }
    }

    /// ### Checks if the application should exit.
    pub fn should_exit(&self) -> bool {
        self.should_exit || self.window.should_close()
    }
}

impl ApplicationHandler for BasicUITest {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.renderer.is_none() {
            // Initialize on first resume
            pollster::block_on(async {
                if let Err(e) = self.initialize(event_loop).await {
                    eprintln!("Failed to initialize basic UI test: {}", e);
                    event_loop.exit();
                }
            });
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        self.handle_window_event(&event);

        if self.should_exit() {
            event_loop.exit();
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        // Update and render
        self.update(1.0 / 60.0); // Assume 60 FPS for now

        if let Err(e) = self.render() {
            eprintln!("Render error: {}", e);
        }

        // Request redraw
        if let Some(window) = self.window.window() {
            window.request_redraw();
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎮 Verturion Enhanced UI Test - Label & LineEdit Testing");
    println!("=========================================================");
    println!("Comprehensive testing of Label and LineEdit components:");
    println!("  Tab 0: Buttons - Basic UI controls");
    println!("  Tab 1: Text Inputs - Original LineEdit examples");
    println!("  Tab 2: Progress Bars - Animated progress indicators");
    println!("  Tab 3: Static Labels - Different text lengths and formatting");
    println!("  Tab 4: Dynamic Labels - Real-time text updates");
    println!("  Tab 5: LineEdit Testing - Comprehensive input field testing");
    println!("  Tab 6: Performance Text - Stress testing with multiple components");
    println!("Controls:");
    println!("  ESC - Exit application");
    println!("  F5 - Reset demo state");
    println!("  TAB - Switch between tabs (0-6)");
    println!("=========================================================\n");

    let event_loop = EventLoop::new()?;
    event_loop.set_control_flow(ControlFlow::Poll);

    let mut app = BasicUITest::new();
    event_loop.run_app(&mut app)?;

    println!("\n🎮 Verturion Basic UI Test - Exiting...");
    Ok(())
}
