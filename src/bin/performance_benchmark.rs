//! Performance Benchmark Application
//!
//! This application provides comprehensive performance benchmarking for the Verturion
//! game engine, testing UI rendering performance, input responsiveness, and system
//! resource usage with multiple UI nodes active simultaneously.

use winit::{
    application::ApplicationHandler,
    event::{ElementState, WindowEvent},
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    keyboard::{KeyCode, PhysicalKey},
    window::WindowId,
};

use verturion::core::{
    renderer::{Window, WindowConfig, Renderer},
    scene::Node,
    signal::SignalManager,
    math::Vector2,
    variant::Color,
    input::{Input, InputMap, KeyCode as InputKeyCode},
    scene::nodes::{Button, Label, ProgressBar, CheckBox},
};

/// ### Performance benchmark application.
pub struct PerformanceBenchmark {
    /// Main window for rendering
    window: Window,
    /// Graphics renderer
    renderer: Option<Renderer>,
    /// Root scene node
    root_node: Option<Node>,
    /// Signal manager for events
    signal_manager: SignalManager,
    /// Input system
    input: Input,
    /// Input action mapping
    input_map: InputMap,
    /// Performance metrics
    metrics: PerformanceMetrics,
    /// Benchmark configuration
    config: BenchmarkConfig,
    /// UI stress test components
    stress_test_ui: StressTestUI,
    /// Text performance testing components
    text_performance_test: TextPerformanceTest,
    /// Whether the application should exit
    should_exit: bool,
    /// Frame counter
    frame_counter: u64,
}

/// ### Performance metrics tracking.
#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    /// Frames per second
    pub fps: f32,
    /// Frame time in milliseconds
    pub frame_time: f32,
    /// Minimum frame time
    pub min_frame_time: f32,
    /// Maximum frame time
    pub max_frame_time: f32,
    /// Average frame time over last 60 frames
    pub avg_frame_time: f32,
    /// Memory usage estimate (placeholder)
    pub memory_usage_mb: f32,
    /// UI elements rendered
    pub ui_elements_rendered: u32,
    /// Input events processed
    pub input_events_processed: u32,
    /// Frame time history for averaging
    frame_times: Vec<f32>,
    /// Last frame timestamp
    last_frame_time: std::time::Instant,
}

/// ### Benchmark configuration settings.
#[derive(Debug, Clone)]
pub struct BenchmarkConfig {
    /// Number of UI elements to stress test with
    pub ui_element_count: u32,
    /// Whether to enable stress testing
    pub stress_test_enabled: bool,
    /// Target FPS for performance evaluation
    pub target_fps: f32,
    /// Whether to show detailed metrics
    pub show_detailed_metrics: bool,
    /// Benchmark duration in seconds
    pub benchmark_duration: f32,
}

/// ### Text performance testing components for comprehensive Label and LineEdit benchmarking.
#[derive(Debug, Clone)]
pub struct TextPerformanceTest {
    /// Large collection of labels for text rendering performance
    pub performance_labels: Vec<verturion::core::scene::nodes::Label>,
    /// Large collection of line edits for input performance
    pub performance_inputs: Vec<verturion::core::scene::nodes::LineEdit>,
    /// Dynamic text content for runtime updates
    pub dynamic_text_content: Vec<String>,
    /// Text update counter
    pub text_update_counter: u32,
    /// Last text update time
    pub last_text_update: std::time::Instant,
    /// Text rendering performance metrics
    pub text_render_time: f32,
    /// Input processing performance metrics
    pub input_process_time: f32,
}

/// ### UI stress test components.
#[derive(Debug, Clone)]
pub struct StressTestUI {
    /// Multiple buttons for stress testing
    pub buttons: Vec<Button>,
    /// Multiple labels for text rendering stress
    pub labels: Vec<Label>,
    /// Multiple progress bars for animation stress
    pub progress_bars: Vec<ProgressBar>,
    /// Multiple checkboxes for interaction stress
    pub checkboxes: Vec<CheckBox>,
    /// Animation time for dynamic updates
    pub animation_time: f32,
}

impl PerformanceMetrics {
    /// ### Creates new performance metrics tracker.
    pub fn new() -> Self {
        Self {
            fps: 0.0,
            frame_time: 0.0,
            min_frame_time: f32::INFINITY,
            max_frame_time: 0.0,
            avg_frame_time: 0.0,
            memory_usage_mb: 0.0,
            ui_elements_rendered: 0,
            input_events_processed: 0,
            frame_times: Vec::with_capacity(60),
            last_frame_time: std::time::Instant::now(),
        }
    }

    /// ### Updates performance metrics.
    pub fn update(&mut self) {
        let now = std::time::Instant::now();
        let delta = now.duration_since(self.last_frame_time).as_secs_f32();
        self.last_frame_time = now;

        self.frame_time = delta * 1000.0; // Convert to milliseconds
        self.min_frame_time = self.min_frame_time.min(self.frame_time);
        self.max_frame_time = self.max_frame_time.max(self.frame_time);

        // Track frame times for averaging
        self.frame_times.push(self.frame_time);
        if self.frame_times.len() > 60 {
            self.frame_times.remove(0);
        }

        // Calculate FPS and average frame time
        if self.frame_time > 0.0 {
            self.fps = 1000.0 / self.frame_time;
        }

        if !self.frame_times.is_empty() {
            self.avg_frame_time = self.frame_times.iter().sum::<f32>() / self.frame_times.len() as f32;
        }

        // Estimate memory usage (placeholder)
        self.memory_usage_mb = 50.0 + (self.ui_elements_rendered as f32 * 0.1);
    }

    /// ### Resets performance metrics.
    pub fn reset(&mut self) {
        self.fps = 0.0;
        self.frame_time = 0.0;
        self.min_frame_time = f32::INFINITY;
        self.max_frame_time = 0.0;
        self.avg_frame_time = 0.0;
        self.ui_elements_rendered = 0;
        self.input_events_processed = 0;
        self.frame_times.clear();
        self.last_frame_time = std::time::Instant::now();
    }
}

impl BenchmarkConfig {
    /// ### Creates default benchmark configuration.
    pub fn new() -> Self {
        Self {
            ui_element_count: 100,
            stress_test_enabled: true,
            target_fps: 60.0,
            show_detailed_metrics: true,
            benchmark_duration: 30.0,
        }
    }

    /// ### Creates a light benchmark configuration.
    pub fn light() -> Self {
        Self {
            ui_element_count: 25,
            stress_test_enabled: true,
            target_fps: 60.0,
            show_detailed_metrics: false,
            benchmark_duration: 10.0,
        }
    }

    /// ### Creates a heavy benchmark configuration.
    pub fn heavy() -> Self {
        Self {
            ui_element_count: 500,
            stress_test_enabled: true,
            target_fps: 30.0,
            show_detailed_metrics: true,
            benchmark_duration: 60.0,
        }
    }
}

impl TextPerformanceTest {
    /// ### Creates new text performance testing components.
    pub fn new(config: &BenchmarkConfig) -> Self {
        let mut performance_labels = Vec::new();
        let mut performance_inputs = Vec::new();
        let mut dynamic_text_content = Vec::new();

        let text_elements_count = config.ui_element_count / 2; // Half labels, half inputs

        // Create performance labels with varying text content
        for i in 0..text_elements_count {
            let mut label = verturion::core::scene::nodes::Label::new(&format!("PerfLabel{}", i));

            // Vary text length for performance testing
            let text_content = match i % 4 {
                0 => format!("Short #{}", i),
                1 => format!("Medium length text for performance testing #{}", i),
                2 => format!("This is a very long text label that demonstrates text rendering performance with extended content that may require word wrapping and advanced text layout processing for label #{}", i),
                _ => format!("Multi-line\nText Label\nLine #{}\nPerformance\nTesting", i),
            };

            label.set_text(verturion::core::variant::String::from(text_content.clone()));
            label.set_font_size(12 + (i % 8) as i32); // Vary font sizes

            // Vary colors for rendering complexity
            let color_factor = (i as f32 / text_elements_count as f32);
            label.set_font_color(verturion::core::variant::Color::new(
                0.5 + color_factor * 0.5,
                0.3 + (1.0 - color_factor) * 0.7,
                0.8,
                1.0
            ));

            performance_labels.push(label);
            dynamic_text_content.push(text_content);
        }

        // Create performance line edits with varying configurations
        for i in 0..text_elements_count {
            let mut input = verturion::core::scene::nodes::LineEdit::new(&format!("PerfInput{}", i));

            // Vary input configurations
            match i % 3 {
                0 => {
                    input.set_placeholder_text(format!("Basic input #{}", i));
                    input.set_max_length(50);
                }
                1 => {
                    input.set_placeholder_text(format!("Limited input #{} (max 20)", i));
                    input.set_max_length(20);
                }
                _ => {
                    input.set_placeholder_text(format!("Extended input field #{} for performance testing", i));
                    input.set_max_length(100);
                    if i % 6 == 0 {
                        input.set_secret(true); // Some password fields
                    }
                }
            }

            // Pre-fill some inputs with text
            if i % 4 == 0 {
                input.set_text(format!("Pre-filled text {}", i));
            }

            performance_inputs.push(input);
        }

        Self {
            performance_labels,
            performance_inputs,
            dynamic_text_content,
            text_update_counter: 0,
            last_text_update: std::time::Instant::now(),
            text_render_time: 0.0,
            input_process_time: 0.0,
        }
    }

    /// ### Updates text performance testing components.
    pub fn update(&mut self, _delta_time: f32, _signal_manager: &mut verturion::core::signal::SignalManager) {
        let start_time = std::time::Instant::now();

        // Update text content every 100ms for performance testing
        if start_time.duration_since(self.last_text_update).as_millis() >= 100 {
            self.text_update_counter += 1;

            // Update a subset of labels each frame to test dynamic text performance
            let labels_to_update = (self.performance_labels.len() / 10).max(1);
            let start_index = (self.text_update_counter as usize * labels_to_update) % self.performance_labels.len();

            for i in 0..labels_to_update {
                let label_index = (start_index + i) % self.performance_labels.len();
                if let Some(label) = self.performance_labels.get_mut(label_index) {
                    let new_text = format!("{} [Updated: {}]",
                        self.dynamic_text_content[label_index],
                        self.text_update_counter
                    );
                    label.set_text(verturion::core::variant::String::from(new_text));
                }
            }

            // Update some input placeholders for input performance testing
            let inputs_to_update = (self.performance_inputs.len() / 20).max(1);
            for i in 0..inputs_to_update {
                let input_index = (start_index + i) % self.performance_inputs.len();
                if let Some(input) = self.performance_inputs.get_mut(input_index) {
                    input.set_placeholder_text(format!("Updated placeholder {} [{}]", input_index, self.text_update_counter));
                }
            }

            self.last_text_update = start_time;
        }

        // Simulate text input processing for performance measurement
        let input_start = std::time::Instant::now();
        for input in &mut self.performance_inputs {
            // Simulate input processing (placeholder for actual input handling)
            let _ = input.get_text().len();
        }
        self.input_process_time = input_start.elapsed().as_secs_f32() * 1000.0;

        // Measure text rendering time (placeholder for actual rendering measurement)
        self.text_render_time = start_time.elapsed().as_secs_f32() * 1000.0;
    }

    /// ### Gets the total number of text components.
    pub fn get_text_component_count(&self) -> u32 {
        (self.performance_labels.len() + self.performance_inputs.len()) as u32
    }

    /// ### Gets text rendering performance metrics.
    pub fn get_text_performance_metrics(&self) -> (f32, f32, u32) {
        (self.text_render_time, self.input_process_time, self.text_update_counter)
    }
}

impl StressTestUI {
    /// ### Creates new stress test UI components.
    pub fn new(config: &BenchmarkConfig) -> Self {
        let mut buttons = Vec::new();
        let mut labels = Vec::new();
        let mut progress_bars = Vec::new();
        let mut checkboxes = Vec::new();

        let elements_per_type = config.ui_element_count / 4;

        // Create buttons
        for i in 0..elements_per_type {
            let mut button = Button::new(&format!("StressButton{}", i));
            button.set_text(verturion::core::variant::String::from(format!("Button {}", i)));
            buttons.push(button);
        }

        // Create labels
        for i in 0..elements_per_type {
            let mut label = Label::new(&format!("StressLabel{}", i));
            label.set_text(verturion::core::variant::String::from(format!("Label {}", i)));
            labels.push(label);
        }

        // Create progress bars
        for i in 0..elements_per_type {
            let mut progress_bar = ProgressBar::new(&format!("StressProgress{}", i));
            progress_bar.set_min_value(0.0, &mut SignalManager::new());
            progress_bar.set_max_value(100.0, &mut SignalManager::new());
            progress_bar.set_value(50.0, &mut SignalManager::new());
            progress_bars.push(progress_bar);
        }

        // Create checkboxes
        for i in 0..elements_per_type {
            let mut checkbox = CheckBox::new(&format!("StressCheckBox{}", i));
            checkbox.set_text(format!("Checkbox {}", i));
            checkboxes.push(checkbox);
        }

        Self {
            buttons,
            labels,
            progress_bars,
            checkboxes,
            animation_time: 0.0,
        }
    }

    /// ### Updates stress test UI animations.
    pub fn update(&mut self, delta_time: f32, signal_manager: &mut SignalManager) {
        self.animation_time += delta_time;

        // Animate progress bars
        for (i, progress_bar) in self.progress_bars.iter_mut().enumerate() {
            let phase = (self.animation_time + i as f32 * 0.1) * 2.0;
            let value = (phase.sin() * 0.5 + 0.5) * 100.0;
            progress_bar.set_value(value as f64, signal_manager);
        }

        // Animate some checkboxes
        for (i, checkbox) in self.checkboxes.iter_mut().enumerate() {
            if i % 10 == 0 { // Only animate every 10th checkbox
                let should_check = ((self.animation_time + i as f32 * 0.2) * 0.5).sin() > 0.0;
                checkbox.set_checked(should_check, signal_manager);
            }
        }
    }

    /// ### Gets the total number of UI elements.
    pub fn get_element_count(&self) -> u32 {
        (self.buttons.len() + self.labels.len() + self.progress_bars.len() + self.checkboxes.len()) as u32
    }
}

impl PerformanceBenchmark {
    /// ### Creates a new performance benchmark application.
    pub fn new() -> Self {
        let window_config = WindowConfig {
            title: "Verturion Performance Benchmark".to_string(),
            width: 1600,
            height: 1000,
            resizable: true,
            vsync: false, // Disable VSync for accurate performance measurement
            clear_color: [0.1, 0.1, 0.1, 1.0],
        };

        let window = Window::new(window_config);
        let signal_manager = SignalManager::new();
        let input = Input::new();
        let mut input_map = InputMap::new();

        // Set up input actions
        input_map.add_action("exit", InputKeyCode::Escape);
        input_map.add_action("reset_benchmark", InputKeyCode::F5);
        input_map.add_action("toggle_stress", InputKeyCode::F1);
        input_map.add_action("light_benchmark", InputKeyCode::F2);
        input_map.add_action("heavy_benchmark", InputKeyCode::F3);

        let config = BenchmarkConfig::new();
        let stress_test_ui = StressTestUI::new(&config);
        let text_performance_test = TextPerformanceTest::new(&config);
        let metrics = PerformanceMetrics::new();

        Self {
            window,
            renderer: None,
            root_node: None,
            signal_manager,
            input,
            input_map,
            metrics,
            config,
            stress_test_ui,
            text_performance_test,
            should_exit: false,
            frame_counter: 0,
        }
    }

    /// ### Initializes the performance benchmark.
    pub async fn initialize(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        // Initialize window
        self.window.initialize(event_loop).await?;

        // Create renderer
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        let renderer = Renderer::new(device, queue, config.format).await?;
        self.renderer = Some(renderer);

        // Create root scene node
        let root = Node::new("BenchmarkRoot");
        self.root_node = Some(root);

        println!("🚀 Performance Benchmark initialized successfully!");
        println!("Window size: {}x{}", config.width, config.height);
        println!("UI Elements: {}", self.stress_test_ui.get_element_count());
        println!("VSync: Disabled for accurate measurement");

        Ok(())
    }

    /// ### Updates the benchmark state.
    pub fn update(&mut self, delta_time: f32) {
        self.frame_counter += 1;

        // Update performance metrics
        self.metrics.update();

        // Update input system
        self.input.update(&self.input_map, (delta_time * 1000.0) as u64);

        // Update stress test UI if enabled
        if self.config.stress_test_enabled {
            self.stress_test_ui.update(delta_time, &mut self.signal_manager);
        }

        // Update text performance testing
        self.text_performance_test.update(delta_time, &mut self.signal_manager);

        // Update UI element count for metrics (including text components)
        self.metrics.ui_elements_rendered = self.stress_test_ui.get_element_count() + self.text_performance_test.get_text_component_count();

        // Handle input actions
        if self.input.is_action_just_pressed("exit") {
            self.should_exit = true;
        }

        if self.input.is_action_just_pressed("reset_benchmark") {
            self.reset_benchmark();
        }

        if self.input.is_action_just_pressed("toggle_stress") {
            self.config.stress_test_enabled = !self.config.stress_test_enabled;
            println!("Stress test: {}", if self.config.stress_test_enabled { "Enabled" } else { "Disabled" });
        }

        if self.input.is_action_just_pressed("light_benchmark") {
            self.config = BenchmarkConfig::light();
            self.stress_test_ui = StressTestUI::new(&self.config);
            self.metrics.reset();
            println!("Switched to light benchmark (25 elements)");
        }

        if self.input.is_action_just_pressed("heavy_benchmark") {
            self.config = BenchmarkConfig::heavy();
            self.stress_test_ui = StressTestUI::new(&self.config);
            self.metrics.reset();
            println!("Switched to heavy benchmark (500 elements)");
        }

        // Print performance summary every 5 seconds
        if self.frame_counter % 300 == 0 {
            self.print_performance_summary();
        }
    }

    /// ### Resets the benchmark.
    fn reset_benchmark(&mut self) {
        self.metrics.reset();
        self.frame_counter = 0;
        println!("Benchmark reset - metrics cleared");
    }

    /// ### Prints performance summary.
    fn print_performance_summary(&self) {
        println!("\n📊 Performance Summary (Frame {}):", self.frame_counter);
        println!("  FPS: {:.1} (Target: {:.1})", self.metrics.fps, self.config.target_fps);
        println!("  Frame Time: {:.2}ms (Avg: {:.2}ms)", self.metrics.frame_time, self.metrics.avg_frame_time);
        println!("  Min/Max Frame Time: {:.2}ms / {:.2}ms", self.metrics.min_frame_time, self.metrics.max_frame_time);
        println!("  UI Elements: {} (Stress: {}, Text: {})",
            self.metrics.ui_elements_rendered,
            self.stress_test_ui.get_element_count(),
            self.text_performance_test.get_text_component_count()
        );
        println!("  Memory Usage: {:.1}MB", self.metrics.memory_usage_mb);

        // Text performance metrics
        let (text_render_time, input_process_time, text_updates) = self.text_performance_test.get_text_performance_metrics();
        println!("  Text Performance:");
        println!("    Text Render Time: {:.3}ms", text_render_time);
        println!("    Input Process Time: {:.3}ms", input_process_time);
        println!("    Text Updates: {}", text_updates);

        // Performance evaluation
        let performance_rating = if self.metrics.fps >= self.config.target_fps * 0.95 {
            "Excellent"
        } else if self.metrics.fps >= self.config.target_fps * 0.8 {
            "Good"
        } else if self.metrics.fps >= self.config.target_fps * 0.6 {
            "Fair"
        } else {
            "Poor"
        };
        println!("  Performance Rating: {}", performance_rating);
    }

    /// ### Renders the performance benchmark.
    pub fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Get rendering resources
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        // Begin frame
        let surface_texture = self.window.begin_frame().ok_or("Failed to get surface texture")?;
        let view = surface_texture.texture.create_view(&wgpu::TextureViewDescriptor::default());

        // Create command encoder
        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some("Benchmark Render Encoder"),
        });

        // Store window size for rendering
        let window_size = Vector2::new(config.width as f32, config.height as f32);

        // Begin render pass
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Benchmark Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.1,
                            b: 0.1,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Get renderer and clear UI renderer for new frame
            let renderer = self.renderer.as_mut().ok_or("No renderer available")?;
            let ui_renderer = renderer.ui_renderer_mut();
            ui_renderer.clear();

            // Render benchmark interface inline to avoid borrowing issues
            // Title bar
            let title_rect = verturion::core::math::Rect2::from_position_size(
                Vector2::new(0.0, 0.0),
                Vector2::new(window_size.x, 50.0)
            );
            ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.3, 1.0));

            // Performance metrics display
            let metrics_rect = verturion::core::math::Rect2::from_position_size(
                Vector2::new(10.0, 60.0),
                Vector2::new(window_size.x - 20.0, 100.0)
            );
            ui_renderer.add_quad(metrics_rect, Color::new(0.15, 0.15, 0.2, 0.9));

            // FPS bar
            let fps_ratio = (self.metrics.fps / self.config.target_fps).min(1.0);
            let fps_color = if fps_ratio > 0.9 {
                Color::new(0.0, 1.0, 0.0, 1.0) // Green
            } else if fps_ratio > 0.7 {
                Color::new(1.0, 1.0, 0.0, 1.0) // Yellow
            } else {
                Color::new(1.0, 0.0, 0.0, 1.0) // Red
            };

            let fps_bar_rect = verturion::core::math::Rect2::from_position_size(
                Vector2::new(20.0, 80.0),
                Vector2::new(300.0 * fps_ratio, 20.0)
            );
            ui_renderer.add_quad(fps_bar_rect, fps_color);

            // Frame time bar
            let frame_time_ratio = (self.metrics.frame_time / 33.33).min(1.0); // 33.33ms = 30fps
            let frame_time_bar_rect = verturion::core::math::Rect2::from_position_size(
                Vector2::new(20.0, 110.0),
                Vector2::new(300.0 * frame_time_ratio, 20.0)
            );
            ui_renderer.add_quad(frame_time_bar_rect, Color::new(0.0, 0.5, 1.0, 1.0));

            // Render stress test elements if enabled
            if self.config.stress_test_enabled {
                let start_y = 180.0;
                let element_size = Vector2::new(80.0, 25.0);
                let spacing = 5.0;
                let elements_per_row = ((window_size.x - 20.0) / (element_size.x + spacing)) as usize;

                let mut current_x = 10.0;
                let mut current_y = start_y;
                let mut elements_in_row = 0;

                let sample_size = (self.stress_test_ui.get_element_count() / 4).min(100);

                for i in 0..sample_size {
                    let color = match i % 4 {
                        0 => Color::new(0.3, 0.5, 0.7, 1.0), // Button
                        1 => Color::new(0.5, 0.3, 0.7, 1.0), // Label
                        2 => {
                            // Animated progress bar
                            let progress = ((self.stress_test_ui.animation_time + i as f32 * 0.1) * 2.0).sin() * 0.5 + 0.5;
                            let fill_width = element_size.x * progress;

                            ui_renderer.add_quad(
                                verturion::core::math::Rect2::from_position_size(
                                    Vector2::new(current_x, current_y),
                                    element_size
                                ),
                                Color::new(0.2, 0.2, 0.2, 1.0)
                            );

                            if fill_width > 0.0 {
                                ui_renderer.add_quad(
                                    verturion::core::math::Rect2::from_position_size(
                                        Vector2::new(current_x, current_y),
                                        Vector2::new(fill_width, element_size.y)
                                    ),
                                    Color::new(0.0, 0.8, 0.4, 1.0)
                                );
                            }

                            current_x += element_size.x + spacing;
                            elements_in_row += 1;

                            if elements_in_row >= elements_per_row {
                                current_x = 10.0;
                                current_y += element_size.y + spacing;
                                elements_in_row = 0;
                            }
                            continue;
                        }
                        _ => Color::new(0.7, 0.5, 0.3, 1.0), // Checkbox
                    };

                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(current_x, current_y),
                            element_size
                        ),
                        color
                    );

                    current_x += element_size.x + spacing;
                    elements_in_row += 1;

                    if elements_in_row >= elements_per_row {
                        current_x = 10.0;
                        current_y += element_size.y + spacing;
                        elements_in_row = 0;
                    }
                }
            }

            // Flush UI rendering
            ui_renderer.flush(&mut render_pass, queue, window_size)?;
        }

        // Submit commands
        queue.submit(std::iter::once(encoder.finish()));
        surface_texture.present();

        Ok(())
    }

    /// ### Renders the benchmark interface.
    fn render_benchmark_interface(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Render title bar
        let title_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(0.0, 0.0),
            Vector2::new(window_size.x, 50.0)
        );
        ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.3, 1.0));

        // Render performance metrics display
        let metrics_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 60.0),
            Vector2::new(window_size.x - 20.0, 100.0)
        );
        ui_renderer.add_quad(metrics_rect, Color::new(0.15, 0.15, 0.2, 0.9));

        // Render FPS bar
        let fps_ratio = (self.metrics.fps / self.config.target_fps).min(1.0);
        let fps_color = if fps_ratio > 0.9 {
            Color::new(0.0, 1.0, 0.0, 1.0) // Green
        } else if fps_ratio > 0.7 {
            Color::new(1.0, 1.0, 0.0, 1.0) // Yellow
        } else {
            Color::new(1.0, 0.0, 0.0, 1.0) // Red
        };

        let fps_bar_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(20.0, 80.0),
            Vector2::new(300.0 * fps_ratio, 20.0)
        );
        ui_renderer.add_quad(fps_bar_rect, fps_color);

        // Render frame time bar
        let frame_time_ratio = (self.metrics.frame_time / 33.33).min(1.0); // 33.33ms = 30fps
        let frame_time_bar_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(20.0, 110.0),
            Vector2::new(300.0 * frame_time_ratio, 20.0)
        );
        ui_renderer.add_quad(frame_time_bar_rect, Color::new(0.0, 0.5, 1.0, 1.0));

        // Add performance metrics text to test fontdue rendering
        let title_text = "Verturion Performance Benchmark";
        let fps_text = format!("FPS: {:.1} / {:.1}", self.metrics.fps, self.config.target_fps);
        let frame_time_text = format!("Frame Time: {:.2}ms", self.metrics.frame_time);
        let elements_text = format!("UI Elements: {}", self.metrics.ui_elements_rendered);

        let text_color = Color::new(1.0, 1.0, 1.0, 1.0); // White text

        // Render title
        if let Err(e) = ui_renderer.render_text(title_text, Vector2::new(20.0, 15.0), text_color, 18.0) {
            eprintln!("Title text rendering error: {}", e);
        }

        // Render FPS text
        if let Err(e) = ui_renderer.render_text(&fps_text, Vector2::new(330.0, 85.0), text_color, 14.0) {
            eprintln!("FPS text rendering error: {}", e);
        }

        // Render frame time text
        if let Err(e) = ui_renderer.render_text(&frame_time_text, Vector2::new(330.0, 115.0), text_color, 14.0) {
            eprintln!("Frame time text rendering error: {}", e);
        }

        // Render elements count text
        if let Err(e) = ui_renderer.render_text(&elements_text, Vector2::new(330.0, 145.0), text_color, 14.0) {
            eprintln!("Elements text rendering error: {}", e);
        }

        // Render stress test UI elements if enabled
        if self.config.stress_test_enabled {
            self.render_stress_test_elements(ui_renderer, window_size)?;
        }

        Ok(())
    }

    /// ### Renders stress test UI elements.
    fn render_stress_test_elements(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let start_y = 180.0;
        let element_size = Vector2::new(80.0, 25.0);
        let spacing = 5.0;
        let elements_per_row = ((window_size.x - 20.0) / (element_size.x + spacing)) as usize;

        let mut current_x = 10.0;
        let mut current_y = start_y;
        let mut elements_in_row = 0;

        // Render a sample of the stress test elements (not all to avoid performance issues)
        let sample_size = (self.stress_test_ui.get_element_count() / 4).min(100);

        for i in 0..sample_size {
            // Alternate between different element types for visual variety
            let color = match i % 4 {
                0 => Color::new(0.3, 0.5, 0.7, 1.0), // Button color
                1 => Color::new(0.5, 0.3, 0.7, 1.0), // Label color
                2 => {
                    // Progress bar with animated fill
                    let progress = ((self.stress_test_ui.animation_time + i as f32 * 0.1) * 2.0).sin() * 0.5 + 0.5;
                    let fill_width = element_size.x * progress;

                    // Background
                    ui_renderer.add_quad(
                        verturion::core::math::Rect2::from_position_size(
                            Vector2::new(current_x, current_y),
                            element_size
                        ),
                        Color::new(0.2, 0.2, 0.2, 1.0)
                    );

                    // Fill
                    if fill_width > 0.0 {
                        ui_renderer.add_quad(
                            verturion::core::math::Rect2::from_position_size(
                                Vector2::new(current_x, current_y),
                                Vector2::new(fill_width, element_size.y)
                            ),
                            Color::new(0.0, 0.8, 0.4, 1.0)
                        );
                    }

                    // Move to next position
                    current_x += element_size.x + spacing;
                    elements_in_row += 1;

                    if elements_in_row >= elements_per_row {
                        current_x = 10.0;
                        current_y += element_size.y + spacing;
                        elements_in_row = 0;
                    }
                    continue;
                }
                _ => Color::new(0.7, 0.5, 0.3, 1.0), // Checkbox color
            };

            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(current_x, current_y),
                    element_size
                ),
                color
            );

            // Add text rendering to stress test the fontdue system
            let text = match i % 4 {
                0 => format!("Btn{}", i),
                1 => format!("Label{}", i),
                2 => format!("{}%", ((i as f32 / sample_size as f32) * 100.0) as u32),
                _ => format!("☑{}", i),
            };

            // Render text on top of the UI element
            let text_color = Color::new(1.0, 1.0, 1.0, 1.0); // White text
            let font_size = 10.0 + (i % 3) as f32 * 2.0; // Vary font sizes for performance testing
            let text_position = Vector2::new(current_x + 2.0, current_y + 2.0);

            if let Err(e) = ui_renderer.render_text(&text, text_position, text_color, font_size) {
                // Don't panic on text rendering errors during stress testing
                eprintln!("Text rendering error: {}", e);
            }

            current_x += element_size.x + spacing;
            elements_in_row += 1;

            if elements_in_row >= elements_per_row {
                current_x = 10.0;
                current_y += element_size.y + spacing;
                elements_in_row = 0;
            }
        }

        Ok(())
    }

    /// ### Handles window events.
    pub fn handle_window_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                self.should_exit = true;
            }
            WindowEvent::Resized(physical_size) => {
                self.window.resize(*physical_size);
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state == ElementState::Pressed {
                    match event.physical_key {
                        PhysicalKey::Code(KeyCode::Escape) => {
                            self.should_exit = true;
                        }
                        PhysicalKey::Code(KeyCode::F1) => {
                            self.config.stress_test_enabled = !self.config.stress_test_enabled;
                            println!("Stress test: {}", if self.config.stress_test_enabled { "Enabled" } else { "Disabled" });
                        }
                        PhysicalKey::Code(KeyCode::F2) => {
                            self.config = BenchmarkConfig::light();
                            self.stress_test_ui = StressTestUI::new(&self.config);
                            self.metrics.reset();
                            println!("Switched to light benchmark (25 elements)");
                        }
                        PhysicalKey::Code(KeyCode::F3) => {
                            self.config = BenchmarkConfig::heavy();
                            self.stress_test_ui = StressTestUI::new(&self.config);
                            self.metrics.reset();
                            println!("Switched to heavy benchmark (500 elements)");
                        }
                        PhysicalKey::Code(KeyCode::F5) => {
                            self.reset_benchmark();
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }
    }

    /// ### Checks if the application should exit.
    pub fn should_exit(&self) -> bool {
        self.should_exit || self.window.should_close()
    }
}

impl ApplicationHandler for PerformanceBenchmark {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.renderer.is_none() {
            // Initialize on first resume
            pollster::block_on(async {
                if let Err(e) = self.initialize(event_loop).await {
                    eprintln!("Failed to initialize performance benchmark: {}", e);
                    event_loop.exit();
                }
            });
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        self.handle_window_event(&event);

        if self.should_exit() {
            event_loop.exit();
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        // Update and render
        self.update(1.0 / 60.0); // Assume 60 FPS for now

        if let Err(e) = self.render() {
            eprintln!("Render error: {}", e);
        }

        // Request redraw
        if let Some(window) = self.window.window() {
            window.request_redraw();
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Verturion Performance Benchmark - Starting...");
    println!("==============================================");
    println!("This benchmark tests UI rendering performance and system resource usage.");
    println!("Controls:");
    println!("  ESC - Exit application");
    println!("  F1 - Toggle stress test");
    println!("  F2 - Light benchmark (25 elements)");
    println!("  F3 - Heavy benchmark (500 elements)");
    println!("  F5 - Reset benchmark metrics");
    println!("==============================================\n");

    let event_loop = EventLoop::new()?;
    event_loop.set_control_flow(ControlFlow::Poll);

    let mut app = PerformanceBenchmark::new();
    event_loop.run_app(&mut app)?;

    println!("\n🚀 Verturion Performance Benchmark - Exiting...");
    Ok(())
}
