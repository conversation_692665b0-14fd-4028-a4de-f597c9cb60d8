//! Real-time Gamepad Testing Interface
//!
//! This application provides a comprehensive real-time testing interface for gamepad/joystick
//! input in the Verturion game engine. It displays all button states, analog stick positions,
//! trigger values, and D-pad states with visual indicators that update in real-time as the
//! user interacts with the gamepad.

use winit::{
    application::ApplicationHandler,
    event::{ElementState, WindowEvent},
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    keyboard::{KeyCode, PhysicalKey},
    window::WindowId,
};

use verturion::core::{
    renderer::{Window, WindowConfig, Renderer},
    scene::Node,
    signal::SignalManager,
    math::Vector2,
    variant::Color,
    input::{Input, InputMap, KeyCode as InputKeyCode, GamepadButton, GamepadAxis},
};

/// ### Real-time gamepad testing application.
pub struct GamepadTest {
    /// Main window for rendering
    window: Window,
    /// Graphics renderer
    renderer: Option<Renderer>,
    /// Root scene node
    root_node: Option<Node>,
    /// Signal manager for events
    signal_manager: SignalManager,
    /// Input system
    input: Input,
    /// Input action mapping
    input_map: InputMap,
    /// Gamepad state display
    gamepad_display: GamepadDisplay,
    /// Whether the application should exit
    should_exit: bool,
    /// Frame counter for animations
    frame_counter: u64,
}

/// ### Visual display for gamepad state.
#[derive(Debug, Clone)]
pub struct GamepadDisplay {
    /// Button states for visualization
    pub button_states: GamepadButtonStates,
    /// Analog stick positions
    pub analog_sticks: AnalogStickStates,
    /// Trigger values
    pub triggers: TriggerStates,
    /// D-pad state
    pub dpad: DPadState,
    /// Connected gamepad count
    pub connected_gamepads: u32,
    /// Current active gamepad ID
    pub active_gamepad: Option<u32>,
}

/// ### Button states for all gamepad buttons.
#[derive(Debug, Clone)]
pub struct GamepadButtonStates {
    // Face buttons (Xbox layout)
    pub a_button: bool,
    pub b_button: bool,
    pub x_button: bool,
    pub y_button: bool,
    
    // Shoulder buttons
    pub left_shoulder: bool,
    pub right_shoulder: bool,
    
    // Stick buttons
    pub left_stick_button: bool,
    pub right_stick_button: bool,
    
    // Menu buttons
    pub start_button: bool,
    pub select_button: bool,
    pub home_button: bool,
    
    // Additional buttons
    pub left_trigger_button: bool,
    pub right_trigger_button: bool,
}

/// ### Analog stick positions and states.
#[derive(Debug, Clone)]
pub struct AnalogStickStates {
    /// Left stick X axis (-1.0 to 1.0)
    pub left_x: f32,
    /// Left stick Y axis (-1.0 to 1.0)
    pub left_y: f32,
    /// Right stick X axis (-1.0 to 1.0)
    pub right_x: f32,
    /// Right stick Y axis (-1.0 to 1.0)
    pub right_y: f32,
    /// Left stick magnitude (0.0 to 1.0)
    pub left_magnitude: f32,
    /// Right stick magnitude (0.0 to 1.0)
    pub right_magnitude: f32,
}

/// ### Trigger values and states.
#[derive(Debug, Clone)]
pub struct TriggerStates {
    /// Left trigger value (0.0 to 1.0)
    pub left_trigger: f32,
    /// Right trigger value (0.0 to 1.0)
    pub right_trigger: f32,
}

/// ### D-pad directional states.
#[derive(Debug, Clone)]
pub struct DPadState {
    /// D-pad up pressed
    pub up: bool,
    /// D-pad down pressed
    pub down: bool,
    /// D-pad left pressed
    pub left: bool,
    /// D-pad right pressed
    pub right: bool,
}

impl GamepadDisplay {
    /// ### Creates a new gamepad display.
    pub fn new() -> Self {
        Self {
            button_states: GamepadButtonStates::new(),
            analog_sticks: AnalogStickStates::new(),
            triggers: TriggerStates::new(),
            dpad: DPadState::new(),
            connected_gamepads: 0,
            active_gamepad: None,
        }
    }

    /// ### Updates gamepad display from input system.
    pub fn update(&mut self, _input: &Input) {
        // TODO: Implement actual gamepad input when methods are available
        // For now, simulate gamepad data for demonstration

        // Simulate connected gamepad
        self.connected_gamepads = 1;
        self.active_gamepad = Some(0);

        // Simulate some button presses for demonstration
        let time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs_f32();

        // Animate some buttons
        self.button_states.a_button = (time * 2.0).sin() > 0.5;
        self.button_states.b_button = (time * 1.5).sin() > 0.7;
        self.button_states.x_button = (time * 1.8).sin() > 0.3;
        self.button_states.y_button = (time * 2.2).sin() > 0.6;

        // Animate analog sticks
        self.analog_sticks.left_x = (time * 1.0).sin() * 0.8;
        self.analog_sticks.left_y = (time * 1.2).cos() * 0.6;
        self.analog_sticks.right_x = (time * 0.8).sin() * 0.5;
        self.analog_sticks.right_y = (time * 0.9).cos() * 0.7;

        // Calculate magnitudes
        self.analog_sticks.left_magnitude = (self.analog_sticks.left_x.powi(2) + self.analog_sticks.left_y.powi(2)).sqrt().min(1.0);
        self.analog_sticks.right_magnitude = (self.analog_sticks.right_x.powi(2) + self.analog_sticks.right_y.powi(2)).sqrt().min(1.0);

        // Animate triggers
        self.triggers.left_trigger = ((time * 1.5).sin() * 0.5 + 0.5).max(0.0);
        self.triggers.right_trigger = ((time * 1.3).cos() * 0.5 + 0.5).max(0.0);

        // Animate D-pad
        let dpad_time = (time * 0.5) as i32 % 8;
        self.dpad.up = dpad_time == 0 || dpad_time == 1 || dpad_time == 7;
        self.dpad.right = dpad_time == 1 || dpad_time == 2 || dpad_time == 3;
        self.dpad.down = dpad_time == 3 || dpad_time == 4 || dpad_time == 5;
        self.dpad.left = dpad_time == 5 || dpad_time == 6 || dpad_time == 7;

        // Animate shoulder buttons
        self.button_states.left_shoulder = (time * 0.7).sin() > 0.8;
        self.button_states.right_shoulder = (time * 0.9).sin() > 0.8;
    }
}

impl GamepadButtonStates {
    /// ### Creates new button states (all released).
    pub fn new() -> Self {
        Self {
            a_button: false,
            b_button: false,
            x_button: false,
            y_button: false,
            left_shoulder: false,
            right_shoulder: false,
            left_stick_button: false,
            right_stick_button: false,
            start_button: false,
            select_button: false,
            home_button: false,
            left_trigger_button: false,
            right_trigger_button: false,
        }
    }
}

impl AnalogStickStates {
    /// ### Creates new analog stick states (centered).
    pub fn new() -> Self {
        Self {
            left_x: 0.0,
            left_y: 0.0,
            right_x: 0.0,
            right_y: 0.0,
            left_magnitude: 0.0,
            right_magnitude: 0.0,
        }
    }
}

impl TriggerStates {
    /// ### Creates new trigger states (not pressed).
    pub fn new() -> Self {
        Self {
            left_trigger: 0.0,
            right_trigger: 0.0,
        }
    }
}

impl DPadState {
    /// ### Creates new D-pad state (not pressed).
    pub fn new() -> Self {
        Self {
            up: false,
            down: false,
            left: false,
            right: false,
        }
    }
}

impl GamepadTest {
    /// ### Creates a new gamepad test application.
    pub fn new() -> Self {
        let window_config = WindowConfig {
            title: "Verturion Real-time Gamepad Test".to_string(),
            width: 1200,
            height: 800,
            resizable: true,
            vsync: true,
            clear_color: [0.1, 0.1, 0.15, 1.0], // Dark background
        };

        let window = Window::new(window_config);
        let signal_manager = SignalManager::new();
        let input = Input::new();
        let mut input_map = InputMap::new();

        // Set up input actions
        input_map.add_action("exit", InputKeyCode::Escape);
        input_map.add_action("refresh", InputKeyCode::F5);

        let gamepad_display = GamepadDisplay::new();

        Self {
            window,
            renderer: None,
            root_node: None,
            signal_manager,
            input,
            input_map,
            gamepad_display,
            should_exit: false,
            frame_counter: 0,
        }
    }

    /// ### Initializes the gamepad test.
    pub async fn initialize(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        // Initialize window
        self.window.initialize(event_loop).await?;

        // Create renderer
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        let renderer = Renderer::new(device, queue, config.format).await?;
        self.renderer = Some(renderer);

        // Create root scene node
        let root = Node::new("GamepadTestRoot");
        self.root_node = Some(root);

        println!("🎮 Real-time Gamepad Test initialized successfully!");
        println!("Window size: {}x{}", config.width, config.height);
        println!("Connect a gamepad and press buttons to see real-time feedback!");

        Ok(())
    }

    /// ### Updates the gamepad test state.
    pub fn update(&mut self, delta_time: f32) {
        self.frame_counter += 1;

        // Update input system
        self.input.update(&self.input_map, (delta_time * 1000.0) as u64);

        // Update gamepad display
        self.gamepad_display.update(&self.input);

        // Handle input actions
        if self.input.is_action_just_pressed("exit") {
            self.should_exit = true;
        }

        if self.input.is_action_just_pressed("refresh") {
            println!("Refreshing gamepad connections...");
            // TODO: Implement gamepad refresh when available in input system
        }
    }

    /// ### Renders the gamepad test.
    pub fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Get rendering resources
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        // Begin frame
        let surface_texture = self.window.begin_frame().ok_or("Failed to get surface texture")?;
        let view = surface_texture.texture.create_view(&wgpu::TextureViewDescriptor::default());

        // Create command encoder
        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some("Gamepad Test Render Encoder"),
        });

        // Store window size for rendering
        let window_size = Vector2::new(config.width as f32, config.height as f32);

        // Begin render pass
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Gamepad Test Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.1,
                            b: 0.15,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Get renderer and clear UI renderer for new frame
            let renderer = self.renderer.as_mut().ok_or("No renderer available")?;
            let ui_renderer = renderer.ui_renderer_mut();
            ui_renderer.clear();

            // Render gamepad interface directly inline to avoid borrowing issues
            // Title area
            let title_rect = verturion::core::math::Rect2::from_position_size(
                Vector2::new(0.0, 0.0),
                Vector2::new(window_size.x, 60.0)
            );
            ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.3, 1.0));

            // Connection status
            let status_color = if self.gamepad_display.connected_gamepads > 0 {
                Color::new(0.0, 0.8, 0.0, 0.8) // Green - connected
            } else {
                Color::new(0.8, 0.0, 0.0, 0.8) // Red - disconnected
            };

            let status_rect = verturion::core::math::Rect2::from_position_size(
                Vector2::new(10.0, 70.0),
                Vector2::new(window_size.x - 20.0, 40.0)
            );
            ui_renderer.add_quad(status_rect, status_color);

            if self.gamepad_display.active_gamepad.is_some() {
                // Gamepad layout background
                let layout_rect = verturion::core::math::Rect2::from_position_size(
                    Vector2::new(10.0, 120.0),
                    Vector2::new(window_size.x - 20.0, window_size.y - 140.0)
                );
                ui_renderer.add_quad(layout_rect, Color::new(0.15, 0.15, 0.2, 0.9));

                // Face buttons
                let button_size = 30.0;
                let button_center_x = window_size.x * 0.75;
                let button_center_y = window_size.y * 0.5;

                // A button
                let a_color = if self.gamepad_display.button_states.a_button {
                    Color::new(0.0, 1.0, 0.0, 1.0)
                } else {
                    Color::new(0.3, 0.3, 0.3, 1.0)
                };
                ui_renderer.add_quad(
                    verturion::core::math::Rect2::from_position_size(
                        Vector2::new(button_center_x - button_size/2.0, button_center_y + 20.0),
                        Vector2::new(button_size, button_size)
                    ),
                    a_color
                );

                // Analog stick
                let stick_center_x = window_size.x * 0.25;
                let stick_center_y = window_size.y * 0.6;
                let stick_radius = 40.0;

                ui_renderer.add_quad(
                    verturion::core::math::Rect2::from_position_size(
                        Vector2::new(stick_center_x - stick_radius, stick_center_y - stick_radius),
                        Vector2::new(stick_radius * 2.0, stick_radius * 2.0)
                    ),
                    Color::new(0.2, 0.2, 0.2, 1.0)
                );

                let stick_pos_x = stick_center_x + self.gamepad_display.analog_sticks.left_x * stick_radius * 0.8;
                let stick_pos_y = stick_center_y + self.gamepad_display.analog_sticks.left_y * stick_radius * 0.8;
                ui_renderer.add_quad(
                    verturion::core::math::Rect2::from_position_size(
                        Vector2::new(stick_pos_x - 8.0, stick_pos_y - 8.0),
                        Vector2::new(16.0, 16.0)
                    ),
                    Color::new(1.0, 1.0, 1.0, 1.0)
                );
            }

            // Flush UI rendering
            ui_renderer.flush(&mut render_pass, queue, window_size)?;
        }

        // Submit commands
        queue.submit(std::iter::once(encoder.finish()));
        surface_texture.present();

        Ok(())
    }

    /// ### Renders a simple gamepad interface.
    fn render_simple_gamepad_interface(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Render title area
        let title_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(0.0, 0.0),
            Vector2::new(window_size.x, 60.0)
        );
        ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.3, 1.0));

        // Render connection status
        let status_color = if self.gamepad_display.connected_gamepads > 0 {
            Color::new(0.0, 0.8, 0.0, 0.8) // Green - connected
        } else {
            Color::new(0.8, 0.0, 0.0, 0.8) // Red - disconnected
        };

        let status_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, 70.0),
            Vector2::new(window_size.x - 20.0, 40.0)
        );
        ui_renderer.add_quad(status_rect, status_color);

        if self.gamepad_display.active_gamepad.is_some() {
            // Render gamepad layout background
            let layout_rect = verturion::core::math::Rect2::from_position_size(
                Vector2::new(10.0, 120.0),
                Vector2::new(window_size.x - 20.0, window_size.y - 140.0)
            );
            ui_renderer.add_quad(layout_rect, Color::new(0.15, 0.15, 0.2, 0.9));

            // Render face buttons (A, B, X, Y)
            let button_size = 30.0;
            let button_center_x = window_size.x * 0.75;
            let button_center_y = window_size.y * 0.5;

            // A button (bottom)
            let a_color = if self.gamepad_display.button_states.a_button {
                Color::new(0.0, 1.0, 0.0, 1.0) // Green when pressed
            } else {
                Color::new(0.3, 0.3, 0.3, 1.0) // Gray when not pressed
            };
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(button_center_x - button_size/2.0, button_center_y + 20.0),
                    Vector2::new(button_size, button_size)
                ),
                a_color
            );

            // B button (right)
            let b_color = if self.gamepad_display.button_states.b_button {
                Color::new(1.0, 0.0, 0.0, 1.0) // Red when pressed
            } else {
                Color::new(0.3, 0.3, 0.3, 1.0)
            };
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(button_center_x + 20.0, button_center_y - button_size/2.0),
                    Vector2::new(button_size, button_size)
                ),
                b_color
            );

            // X button (left)
            let x_color = if self.gamepad_display.button_states.x_button {
                Color::new(0.0, 0.0, 1.0, 1.0) // Blue when pressed
            } else {
                Color::new(0.3, 0.3, 0.3, 1.0)
            };
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(button_center_x - 50.0, button_center_y - button_size/2.0),
                    Vector2::new(button_size, button_size)
                ),
                x_color
            );

            // Y button (top)
            let y_color = if self.gamepad_display.button_states.y_button {
                Color::new(1.0, 1.0, 0.0, 1.0) // Yellow when pressed
            } else {
                Color::new(0.3, 0.3, 0.3, 1.0)
            };
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(button_center_x - button_size/2.0, button_center_y - 50.0),
                    Vector2::new(button_size, button_size)
                ),
                y_color
            );

            // Render left analog stick
            let stick_center_x = window_size.x * 0.25;
            let stick_center_y = window_size.y * 0.6;
            let stick_radius = 40.0;

            // Stick background
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(stick_center_x - stick_radius, stick_center_y - stick_radius),
                    Vector2::new(stick_radius * 2.0, stick_radius * 2.0)
                ),
                Color::new(0.2, 0.2, 0.2, 1.0)
            );

            // Stick position indicator
            let stick_pos_x = stick_center_x + self.gamepad_display.analog_sticks.left_x * stick_radius * 0.8;
            let stick_pos_y = stick_center_y + self.gamepad_display.analog_sticks.left_y * stick_radius * 0.8;
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(stick_pos_x - 8.0, stick_pos_y - 8.0),
                    Vector2::new(16.0, 16.0)
                ),
                Color::new(1.0, 1.0, 1.0, 1.0)
            );

            // Render trigger bars
            let trigger_width = 200.0;
            let trigger_height = 20.0;

            // Left trigger
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(50.0, 150.0),
                    Vector2::new(trigger_width * self.gamepad_display.triggers.left_trigger, trigger_height)
                ),
                Color::new(0.0, 1.0, 0.5, 1.0)
            );

            // Right trigger
            ui_renderer.add_quad(
                verturion::core::math::Rect2::from_position_size(
                    Vector2::new(window_size.x - 250.0, 150.0),
                    Vector2::new(trigger_width * self.gamepad_display.triggers.right_trigger, trigger_height)
                ),
                Color::new(1.0, 0.5, 0.0, 1.0)
            );
        }

        Ok(())
    }

    /// ### Renders the title area.
    fn render_title_area(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let title_height = 60.0;
        let title_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(0.0, 0.0),
            Vector2::new(window_size.x, title_height)
        );

        ui_renderer.add_quad(title_rect, Color::new(0.2, 0.2, 0.3, 1.0));

        // TODO: Add title text when TextRenderer is implemented
        Ok(())
    }

    /// ### Renders connection status.
    fn render_connection_status(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let status_y = 70.0;
        let status_height = 40.0;
        let status_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, status_y),
            Vector2::new(window_size.x - 20.0, status_height)
        );

        let status_color = if self.gamepad_display.connected_gamepads > 0 {
            Color::new(0.0, 0.8, 0.0, 0.8) // Green - connected
        } else {
            Color::new(0.8, 0.0, 0.0, 0.8) // Red - disconnected
        };

        ui_renderer.add_quad(status_rect, status_color);

        // TODO: Add status text when TextRenderer is implemented
        Ok(())
    }

    /// ### Renders the gamepad layout background.
    fn render_gamepad_layout(
        &self,
        ui_renderer: &mut verturion::core::renderer::UIRenderer,
        window_size: Vector2,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let layout_y = 120.0;
        let layout_height = window_size.y - layout_y - 20.0;
        let layout_rect = verturion::core::math::Rect2::from_position_size(
            Vector2::new(10.0, layout_y),
            Vector2::new(window_size.x - 20.0, layout_height)
        );

        ui_renderer.add_quad(layout_rect, Color::new(0.15, 0.15, 0.2, 0.9));

        Ok(())
    }

    /// ### Handles window events.
    pub fn handle_window_event(&mut self, event: &WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                self.should_exit = true;
            }
            WindowEvent::Resized(physical_size) => {
                self.window.resize(*physical_size);
                println!("Window resized to: {}x{}", physical_size.width, physical_size.height);
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state == ElementState::Pressed {
                    match event.physical_key {
                        PhysicalKey::Code(KeyCode::Escape) => {
                            self.should_exit = true;
                        }
                        PhysicalKey::Code(KeyCode::F5) => {
                            println!("Refreshing gamepad connections...");
                        }
                        _ => {}
                    }
                }
            }
            _ => {}
        }
    }

    /// ### Checks if the application should exit.
    pub fn should_exit(&self) -> bool {
        self.should_exit || self.window.should_close()
    }
}

impl ApplicationHandler for GamepadTest {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if self.renderer.is_none() {
            // Initialize on first resume
            pollster::block_on(async {
                if let Err(e) = self.initialize(event_loop).await {
                    eprintln!("Failed to initialize gamepad test: {}", e);
                    event_loop.exit();
                }
            });
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        self.handle_window_event(&event);

        if self.should_exit() {
            event_loop.exit();
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        // Update and render
        self.update(1.0 / 60.0); // Assume 60 FPS for now

        if let Err(e) = self.render() {
            eprintln!("Render error: {}", e);
        }

        // Request redraw
        if let Some(window) = self.window.window() {
            window.request_redraw();
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎮 Verturion Real-time Gamepad Test - Starting...");
    println!("==============================================");
    println!("This demo shows animated gamepad input visualization.");
    println!("Controls:");
    println!("  ESC - Exit application");
    println!("  F5 - Refresh gamepad connections");
    println!("==============================================\n");

    let event_loop = EventLoop::new()?;
    event_loop.set_control_flow(ControlFlow::Poll);

    let mut app = GamepadTest::new();
    event_loop.run_app(&mut app)?;

    println!("\n🎮 Verturion Gamepad Test - Exiting...");
    Ok(())
}
