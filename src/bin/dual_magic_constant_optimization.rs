#![allow(dead_code, unused_imports, unused_variables)] // Research tool - comprehensive implementation

use std::time::Instant;

/// Dual Magic Constant Fast Inverse Square Root Optimization
/// 
/// This program explores dual magic constant approaches that use mathematical error
/// cancellation to achieve better accuracy than single-constant implementations.
/// 
/// Mathematical Approaches:
/// 1. Averaging: (result1 + result2) * 0.5
/// 2. Weighted Combination: alpha * result1 + (1-alpha) * result2  
/// 3. Error Correction: base_result + correction_term
/// 4. Harmonic Mean: 2 / (1/result1 + 1/result2)

#[derive(Debug, Clone)]
struct DualMagicResult {
    magic1: u32,
    magic2: u32,
    combination_method: String,
    max_error: f64,
    avg_error: f64,
    worst_case_input: f32,
    performance_estimate: f64, // Estimated ns/op based on operation count
}

impl DualMagicResult {
    fn new(magic1: u32, magic2: u32, method: &str) -> Self {
        Self {
            magic1,
            magic2,
            combination_method: method.to_string(),
            max_error: f64::INFINITY,
            avg_error: f64::INFINITY,
            worst_case_input: 0.0,
            performance_estimate: 0.0,
        }
    }
}

/// Dual magic constant implementations with different combination strategies
mod dual_implementations {
    /// Averaging approach: (result1 + result2) * 0.5
    #[inline]
    pub fn averaging(x: f32, magic1: u32, magic2: u32) -> f32 {
        if x == 0.0 { return 0.0; }
        if x < 0.0 { return f32::NAN; }
        if x < 1e-10 { return 1.0 / x.sqrt(); }

        let bits = x.to_bits();
        let result1 = f32::from_bits(magic1 - (bits >> 1));
        let result2 = f32::from_bits(magic2 - (bits >> 1));
        (result1 + result2) * 0.5
    }

    /// Weighted combination: alpha * result1 + (1-alpha) * result2
    #[inline]
    pub fn weighted(x: f32, magic1: u32, magic2: u32, alpha: f32) -> f32 {
        if x == 0.0 { return 0.0; }
        if x < 0.0 { return f32::NAN; }
        if x < 1e-10 { return 1.0 / x.sqrt(); }

        let bits = x.to_bits();
        let result1 = f32::from_bits(magic1 - (bits >> 1));
        let result2 = f32::from_bits(magic2 - (bits >> 1));
        alpha * result1 + (1.0 - alpha) * result2
    }

    /// Error correction: base_result + correction_term
    #[inline]
    pub fn error_correction(x: f32, magic1: u32, magic2: u32) -> f32 {
        if x == 0.0 { return 0.0; }
        if x < 0.0 { return f32::NAN; }
        if x < 1e-10 { return 1.0 / x.sqrt(); }

        let bits = x.to_bits();
        let base_result = f32::from_bits(magic1 - (bits >> 1));
        let correction_base = f32::from_bits(magic2 - (bits >> 1));
        
        // Use difference as correction term scaled by input magnitude
        let correction = (correction_base - base_result) * 0.1;
        base_result + correction
    }

    /// Harmonic mean: 2 / (1/result1 + 1/result2)
    #[inline]
    pub fn harmonic_mean(x: f32, magic1: u32, magic2: u32) -> f32 {
        if x == 0.0 { return 0.0; }
        if x < 0.0 { return f32::NAN; }
        if x < 1e-10 { return 1.0 / x.sqrt(); }

        let bits = x.to_bits();
        let result1 = f32::from_bits(magic1 - (bits >> 1));
        let result2 = f32::from_bits(magic2 - (bits >> 1));
        
        if result1 == 0.0 || result2 == 0.0 { return 0.0; }
        2.0 / (1.0/result1 + 1.0/result2)
    }

    /// Geometric mean: sqrt(result1 * result2)
    #[inline]
    pub fn geometric_mean(x: f32, magic1: u32, magic2: u32) -> f32 {
        if x == 0.0 { return 0.0; }
        if x < 0.0 { return f32::NAN; }
        if x < 1e-10 { return 1.0 / x.sqrt(); }

        let bits = x.to_bits();
        let result1 = f32::from_bits(magic1 - (bits >> 1));
        let result2 = f32::from_bits(magic2 - (bits >> 1));
        
        if result1 < 0.0 || result2 < 0.0 { return f32::NAN; }
        (result1 * result2).sqrt()
    }
}

/// Calculate accuracy metrics for a dual magic constant approach
fn calculate_dual_accuracy(
    magic1: u32, 
    magic2: u32, 
    method: &str,
    test_values: &[f32]
) -> DualMagicResult {
    let mut max_error = 0.0f64;
    let mut total_error = 0.0f64;
    let mut worst_case_input = 0.0f32;
    let mut valid_count = 0;

    // Estimate performance based on operation count
    let performance_estimate = match method {
        "averaging" => 2.5,      // 2 magic ops + 1 add + 1 mul
        "weighted_0.3" => 3.0,   // 2 magic ops + 2 mul + 1 add
        "weighted_0.7" => 3.0,   // 2 magic ops + 2 mul + 1 add
        "error_correction" => 3.2, // 2 magic ops + 1 sub + 1 mul + 1 add
        "harmonic_mean" => 4.0,  // 2 magic ops + 2 div + 1 add + 1 div
        "geometric_mean" => 3.5, // 2 magic ops + 1 mul + 1 sqrt
        _ => 3.0,
    };

    for &x in test_values {
        let fast_result = match method {
            "averaging" => dual_implementations::averaging(x, magic1, magic2),
            "weighted_0.3" => dual_implementations::weighted(x, magic1, magic2, 0.3),
            "weighted_0.7" => dual_implementations::weighted(x, magic1, magic2, 0.7),
            "error_correction" => dual_implementations::error_correction(x, magic1, magic2),
            "harmonic_mean" => dual_implementations::harmonic_mean(x, magic1, magic2),
            "geometric_mean" => dual_implementations::geometric_mean(x, magic1, magic2),
            _ => continue,
        };

        let accurate_result = 1.0 / x.sqrt();

        // Skip invalid results
        if fast_result.is_nan() || fast_result.is_infinite() || 
           accurate_result.is_nan() || accurate_result.is_infinite() {
            continue;
        }

        // Calculate relative error
        let relative_error = (((fast_result - accurate_result) / accurate_result).abs() * 100.0) as f64;
        
        if relative_error > max_error {
            max_error = relative_error;
            worst_case_input = x;
        }
        
        total_error += relative_error;
        valid_count += 1;
    }

    let avg_error = if valid_count > 0 { total_error / valid_count as f64 } else { f64::INFINITY };

    DualMagicResult {
        magic1,
        magic2,
        combination_method: method.to_string(),
        max_error,
        avg_error,
        worst_case_input,
        performance_estimate,
    }
}

/// Generate test values for Vector2 optimization range [0.1, 100]
fn generate_test_values() -> Vec<f32> {
    let mut values = Vec::new();
    
    // Linear distribution in [0.1, 100] range
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let value = 0.1 + t * (100.0 - 0.1);
        values.push(value);
    }
    
    // Logarithmic distribution for better coverage
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let log_min = 0.1f32.ln();
        let log_max = 100.0f32.ln();
        let value = (log_min + t * (log_max - log_min)).exp();
        values.push(value);
    }
    
    // Powers of 2 and their reciprocals
    for i in -10..=10 {
        let value = 2.0f32.powi(i);
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }
    
    // Common vector magnitudes
    let common_values = [
        0.1, 0.2, 0.5, 0.7071067811865476, // sqrt(0.5)
        1.0, 1.4142135623730951, // sqrt(2)
        2.0, 3.0, 5.0, 10.0, 20.0, 50.0, 100.0
    ];
    
    for &value in &common_values {
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }
    
    // Random values for comprehensive coverage
    use std::collections::HashSet;
    let mut rng_state = 12345u32;
    let mut seen = HashSet::new();
    
    while values.len() < 900 {
        rng_state = rng_state.wrapping_mul(1664525).wrapping_add(1013904223);
        let random_f32 = (rng_state as f32) / (u32::MAX as f32);
        let value = 0.1 + random_f32 * (100.0 - 0.1);
        
        let key = (value * 1000000.0) as u32;
        if seen.insert(key) {
            values.push(value);
        }
    }
    
    values.truncate(900);
    values.sort_by(|a, b| a.partial_cmp(b).unwrap());
    values
}

fn main() {
    println!("🔬 Dual Magic Constant Fast Inverse Square Root Optimization");
    println!("==============================================================");
    println!("Exploring mathematical error cancellation through dual constants");
    println!("Target: Improve upon V9 optimized (3.416589% max error, 2.06 ns/op)");
    println!();

    // Generate test values
    println!("📊 Generating test values...");
    let test_values = generate_test_values();
    println!("✅ Generated {} test values in range [0.1, 100]", test_values.len());
    println!();

    // Current V9 baseline for comparison
    const V9_OPTIMIZED_MAGIC: u32 = 0x5f376244;
    println!("🎯 V9 Optimized Baseline: 0x{:08x}", V9_OPTIMIZED_MAGIC);
    println!("   Max Error: 3.416589%, Performance: 2.06 ns/op");
    println!();

    // Define search parameters
    const SEARCH_START: u32 = 0x5f370000;
    const SEARCH_END: u32 = 0x5f380000;
    const SEARCH_STEP: u32 = 0x100; // Sample every 256 values for initial exploration
    
    let methods = [
        "averaging",
        "weighted_0.3", 
        "weighted_0.7",
        "error_correction",
        "harmonic_mean",
        "geometric_mean"
    ];

    println!("🚀 Starting dual magic constant exploration...");
    println!("Search Range: 0x{:08x} to 0x{:08x} (step: 0x{:x})", SEARCH_START, SEARCH_END, SEARCH_STEP);
    println!("Methods: {}", methods.join(", "));
    println!();

    let mut all_results: Vec<DualMagicResult> = Vec::new();
    let start_time = Instant::now();
    let mut combinations_tested = 0;

    // Test combinations of magic constants with different methods
    for magic1 in (SEARCH_START..SEARCH_END).step_by(SEARCH_STEP as usize) {
        for magic2 in (magic1..SEARCH_END).step_by(SEARCH_STEP as usize) {
            for &method in &methods {
                let result = calculate_dual_accuracy(magic1, magic2, method, &test_values);
                
                // Only keep results that show promise (better than V9 or reasonable performance)
                if result.max_error < 3.5 && result.performance_estimate <= 4.0 {
                    all_results.push(result);
                }
                
                combinations_tested += 1;
                
                // Progress update every 1000 combinations
                if combinations_tested % 1000 == 0 {
                    let elapsed = start_time.elapsed().as_secs_f64();
                    println!("📊 Tested {} combinations in {:.1}s - Best so far: {:.6}%", 
                             combinations_tested, elapsed, 
                             all_results.iter().map(|r| r.max_error).fold(f64::INFINITY, f64::min));
                }
            }
        }
    }

    let total_time = start_time.elapsed();
    println!();
    println!("✅ Exploration completed in {:.2} seconds", total_time.as_secs_f64());
    println!("   Total combinations tested: {}", combinations_tested);
    println!();

    // Sort results by accuracy (max error)
    all_results.sort_by(|a, b| a.max_error.partial_cmp(&b.max_error).unwrap());

    // Display results
    println!("🏆 DUAL MAGIC CONSTANT OPTIMIZATION RESULTS");
    println!("============================================");
    println!();

    if all_results.is_empty() {
        println!("❌ No dual constant combinations found that improve upon V9 baseline");
        println!("   This suggests single-constant V9 is near-optimal for the given constraints");
        return;
    }

    println!("🎯 TOP 10 DUAL MAGIC CONSTANT COMBINATIONS:");
    println!("   Rank | Magic1     | Magic2     | Method          | Max Error (%) | Avg Error (%) | Est. ns/op | Improvement");
    println!("   -----|------------|------------|-----------------|---------------|---------------|------------|------------");
    
    for (rank, result) in all_results.iter().take(10).enumerate() {
        let improvement = ((3.416589 - result.max_error) / 3.416589) * 100.0;
        println!("   {:4} | 0x{:08x} | 0x{:08x} | {:15} | {:11.6} | {:11.6} | {:8.1} | {:+9.4}%", 
                 rank + 1, result.magic1, result.magic2, result.combination_method,
                 result.max_error, result.avg_error, result.performance_estimate, improvement);
    }
    println!();

    // Analyze best result
    if let Some(best) = all_results.first() {
        println!("🥇 BEST DUAL CONSTANT RESULT:");
        println!("   Magic Constants: 0x{:08x}, 0x{:08x}", best.magic1, best.magic2);
        println!("   Method: {}", best.combination_method);
        println!("   Max Error: {:.6}%", best.max_error);
        println!("   Avg Error: {:.6}%", best.avg_error);
        println!("   Estimated Performance: {:.1} ns/op", best.performance_estimate);
        println!("   Worst Case Input: {}", best.worst_case_input);
        println!();

        let accuracy_improvement = ((3.416589 - best.max_error) / 3.416589) * 100.0;
        let performance_overhead = ((best.performance_estimate - 2.06) / 2.06) * 100.0;

        if best.max_error < 3.416589 {
            println!("✅ SUCCESS: Dual constant approach achieves better accuracy!");
            println!("   Accuracy Improvement: {:.4}% reduction in max error", accuracy_improvement);
            println!("   Performance Overhead: {:.1}% increase in execution time", performance_overhead);
            
            if best.performance_estimate <= 3.0 {
                println!("   ✅ Performance acceptable for Vector2 operations");
            } else {
                println!("   ⚠️  Performance overhead may be too high for some applications");
            }
        } else {
            println!("❌ RESULT: No dual constant combination improves upon V9 accuracy");
            println!("   Best dual approach: {:.6}% vs V9: 3.416589%", best.max_error);
        }
    }

    println!();
    println!("🔬 ANALYSIS SUMMARY:");
    println!("   Methods Tested: {}", methods.len());
    println!("   Magic Constant Pairs: {}", combinations_tested / methods.len());
    println!("   Promising Results: {}", all_results.len());
    println!("   Search Efficiency: {:.0} combinations/second", 
             combinations_tested as f64 / total_time.as_secs_f64());
    
    // Method effectiveness analysis
    println!();
    println!("📈 METHOD EFFECTIVENESS:");
    for method in &methods {
        let method_results: Vec<_> = all_results.iter()
            .filter(|r| r.combination_method == *method)
            .collect();
        
        if !method_results.is_empty() {
            let best_error = method_results.iter()
                .map(|r| r.max_error)
                .fold(f64::INFINITY, f64::min);
            let avg_performance = method_results.iter()
                .map(|r| r.performance_estimate)
                .sum::<f64>() / method_results.len() as f64;
            
            println!("   {:15}: Best {:.6}% error, Avg {:.1} ns/op, {} results", 
                     method, best_error, avg_performance, method_results.len());
        }
    }
}
