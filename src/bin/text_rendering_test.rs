//! Simple Text Rendering Test
//!
//! This application provides a focused test of the fontdue baseline alignment fix.
//! It renders various text samples to verify that characters align properly to a
//! consistent baseline without the "wobbly" appearance.

use winit::{
    application::ApplicationHandler,
    event::{ElementState, WindowEvent},
    event_loop::{ActiveEventLoop, ControlFlow, EventLoop},
    keyboard::PhysicalKey,
    window::WindowId,
};

use verturion::core::{
    renderer::{Window, WindowConfig, Renderer},
    scene::Node,
    scene::nodes::Label,
    signal::SignalManager,
    math::Vector2,
    variant::Color,
};

/// ### Simple text rendering test application.
pub struct TextRenderingTest {
    /// Main window for rendering
    window: Window,
    /// Graphics renderer
    renderer: Option<Renderer>,
    /// Root scene node
    root_node: Option<Node>,
    /// Signal manager for events
    signal_manager: SignalManager,
    /// Test labels for baseline alignment validation
    test_labels: Vec<Label>,
    /// Whether the application should exit
    should_exit: bool,
    /// Frame counter
    frame_counter: u64,
}

impl TextRenderingTest {
    /// ### Creates a new text rendering test application.
    pub fn new() -> Self {
        let window_config = WindowConfig {
            title: "Verturion Text Rendering Test - Baseline Alignment Validation".to_string(),
            width: 1000,
            height: 700,
            resizable: true,
            vsync: true,
            clear_color: [0.1, 0.1, 0.1, 1.0],
        };

        let window = Window::new(window_config);
        let signal_manager = SignalManager::new();

        // Create test labels with various text samples
        let mut test_labels = Vec::new();

        // Test 1: The original problematic text that showed "wobbly" alignment
        let mut label1 = Label::new("WobblyTest");
        label1.set_text(verturion::core::variant::String::from("HELLO WORLD - Testing simple text rendering"));
        label1.set_font_size(20);
        label1.set_font_color(Color::new(1.0, 1.0, 1.0, 1.0));
        test_labels.push(label1);

        // Test 2: Mixed case text that would show height variations
        let mut label2 = Label::new("MixedCaseTest");
        label2.set_text(verturion::core::variant::String::from("MiXeD CaSe TeXt WiTh VaRyInG hEiGhTs"));
        label2.set_font_size(18);
        label2.set_font_color(Color::new(0.8, 1.0, 0.8, 1.0));
        test_labels.push(label2);

        // Test 3: Characters with descenders
        let mut label3 = Label::new("DescenderTest");
        label3.set_text(verturion::core::variant::String::from("Descenders: gjpqy should align properly"));
        label3.set_font_size(16);
        label3.set_font_color(Color::new(1.0, 0.8, 0.8, 1.0));
        test_labels.push(label3);

        // Test 4: Characters with ascenders
        let mut label4 = Label::new("AscenderTest");
        label4.set_text(verturion::core::variant::String::from("Ascenders: bdfhklt should align properly"));
        label4.set_font_size(16);
        label4.set_font_color(Color::new(0.8, 0.8, 1.0, 1.0));
        test_labels.push(label4);

        // Test 5: Special characters and symbols
        let mut label5 = Label::new("SpecialCharsTest");
        label5.set_text(verturion::core::variant::String::from("Special chars: @#$%^&*()_+-=[]{}"));
        label5.set_font_size(14);
        label5.set_font_color(Color::new(1.0, 1.0, 0.8, 1.0));
        test_labels.push(label5);

        // Test 6: Numbers and punctuation
        let mut label6 = Label::new("NumbersTest");
        label6.set_text(verturion::core::variant::String::from("Numbers: 0123456789 and punctuation: .,;:!?"));
        label6.set_font_size(14);
        label6.set_font_color(Color::new(0.8, 1.0, 1.0, 1.0));
        test_labels.push(label6);

        // Test 7: Different font size for comparison
        let mut label7 = Label::new("LargeFontTest");
        label7.set_text(verturion::core::variant::String::from("Large font baseline test"));
        label7.set_font_size(24);
        label7.set_font_color(Color::new(1.0, 0.8, 1.0, 1.0));
        test_labels.push(label7);

        // Test 8: Small font for comparison
        let mut label8 = Label::new("SmallFontTest");
        label8.set_text(verturion::core::variant::String::from("Small font baseline test"));
        label8.set_font_size(12);
        label8.set_font_color(Color::new(0.9, 0.9, 0.9, 1.0));
        test_labels.push(label8);

        Self {
            window,
            renderer: None,
            root_node: None,
            signal_manager,
            test_labels,
            should_exit: false,
            frame_counter: 0,
        }
    }

    /// ### Initializes the text rendering test.
    pub async fn initialize(&mut self, event_loop: &ActiveEventLoop) -> Result<(), Box<dyn std::error::Error>> {
        // Initialize window
        self.window.initialize(event_loop).await?;

        // Create renderer
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        let renderer = Renderer::new(device, queue, config.format).await?;
        self.renderer = Some(renderer);

        // Create root scene node
        let root = Node::new("TextTestRoot");
        self.root_node = Some(root);

        println!("📝 Text Rendering Test initialized successfully!");
        println!("Window size: {}x{}", config.width, config.height);
        println!("Testing fontdue baseline alignment fix:");
        println!("  - Formula: baseline_y - glyph.height");
        println!("  - Expected: All characters should align to consistent baseline");
        println!("  - Test labels: {}", self.test_labels.len());
        println!();
        println!("Look for:");
        println!("  ✅ No 'wobbly' text where characters float at different heights");
        println!("  ✅ Consistent baseline alignment across all text samples");
        println!("  ✅ Proper alignment for descenders (g,j,p,q,y) and ascenders (b,d,f,h,k,l,t)");
        println!();
        println!("Press ESC to exit");

        Ok(())
    }

    /// ### Updates the text rendering test.
    pub fn update(&mut self, _delta_time: f32) {
        self.frame_counter += 1;
    }

    /// ### Renders the text rendering test.
    pub fn render(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Get rendering resources
        let device = self.window.device().ok_or("No device available")?;
        let queue = self.window.queue().ok_or("No queue available")?;
        let config = self.window.config().ok_or("No surface config available")?;

        // Begin frame
        let surface_texture = self.window.begin_frame().ok_or("Failed to get surface texture")?;
        let view = surface_texture.texture.create_view(&wgpu::TextureViewDescriptor::default());

        // Create command encoder
        let mut encoder = device.create_command_encoder(&wgpu::CommandEncoderDescriptor {
            label: Some("Text Test Render Encoder"),
        });

        // Store window size for rendering
        let window_size = Vector2::new(config.width as f32, config.height as f32);

        // Begin render pass
        {
            let mut render_pass = encoder.begin_render_pass(&wgpu::RenderPassDescriptor {
                label: Some("Text Test Render Pass"),
                color_attachments: &[Some(wgpu::RenderPassColorAttachment {
                    view: &view,
                    resolve_target: None,
                    ops: wgpu::Operations {
                        load: wgpu::LoadOp::Clear(wgpu::Color {
                            r: 0.1,
                            g: 0.1,
                            b: 0.1,
                            a: 1.0,
                        }),
                        store: wgpu::StoreOp::Store,
                    },
                })],
                depth_stencil_attachment: None,
                occlusion_query_set: None,
                timestamp_writes: None,
            });

            // Get renderer and clear UI renderer for new frame
            let renderer = self.renderer.as_mut().ok_or("No renderer available")?;
            renderer.ui_renderer_mut().clear();

            // Render text samples
            let ui_renderer = renderer.ui_renderer_mut();
            
            // Add title
            ui_renderer.render_text(
                "Fontdue Baseline Alignment Test",
                Vector2::new(50.0, 30.0),
                Color::new(1.0, 1.0, 0.0, 1.0),
                24.0
            )?;

            ui_renderer.render_text(
                "All text below should align to consistent baselines (no 'wobbly' effect):",
                Vector2::new(50.0, 70.0),
                Color::new(0.8, 0.8, 0.8, 1.0),
                14.0
            )?;

            // Render test text samples at different vertical positions
            let start_y = 120.0;
            let line_height = 50.0;

            for (i, label) in self.test_labels.iter().enumerate() {
                let y_pos = start_y + (i as f32 * line_height);

                ui_renderer.render_text(
                    &label.get_text().to_string(),
                    Vector2::new(50.0, y_pos),
                    label.get_font_color(),
                    label.get_font_size() as f32
                )?;
            }

            // Add instructions
            ui_renderer.render_text(
                "Instructions: Visually inspect that all characters sit on consistent baselines",
                Vector2::new(50.0, window_size.y - 80.0),
                Color::new(0.6, 0.6, 0.6, 1.0),
                12.0
            )?;

            ui_renderer.render_text(
                "Press ESC to exit",
                Vector2::new(50.0, window_size.y - 50.0),
                Color::new(0.6, 0.6, 0.6, 1.0),
                12.0
            )?;

            // Flush UI rendering
            ui_renderer.flush(&mut render_pass, queue, window_size)?;
        }

        // Submit commands
        queue.submit(std::iter::once(encoder.finish()));
        surface_texture.present();

        Ok(())
    }

    /// ### Checks if the application should exit.
    pub fn should_exit(&self) -> bool {
        self.should_exit
    }
}

impl ApplicationHandler for TextRenderingTest {
    fn resumed(&mut self, event_loop: &ActiveEventLoop) {
        if let Err(e) = pollster::block_on(self.initialize(event_loop)) {
            eprintln!("Failed to initialize text rendering test: {}", e);
            event_loop.exit();
        }
    }

    fn window_event(&mut self, event_loop: &ActiveEventLoop, _window_id: WindowId, event: WindowEvent) {
        match event {
            WindowEvent::CloseRequested => {
                println!("Text rendering test closing...");
                event_loop.exit();
            }
            WindowEvent::KeyboardInput { event, .. } => {
                if event.state == ElementState::Pressed {
                    if let PhysicalKey::Code(keycode) = event.physical_key {
                        match keycode {
                            winit::keyboard::KeyCode::Escape => {
                                println!("Exiting text rendering test...");
                                self.should_exit = true;
                            }
                            _ => {}
                        }
                    }
                }
            }
            WindowEvent::RedrawRequested => {
                // Update application state
                let delta_time = 1.0 / 60.0; // Assume 60 FPS
                self.update(delta_time);

                // Render the application
                if let Err(e) = self.render() {
                    eprintln!("Render error: {}", e);
                }

                // Check if we should exit
                if self.should_exit() {
                    event_loop.exit();
                }

                // Request next frame
                if let Some(window) = self.window.window() {
                    window.request_redraw();
                }
            }
            _ => {}
        }
    }

    fn about_to_wait(&mut self, _event_loop: &ActiveEventLoop) {
        if let Some(window) = self.window.window() {
            window.request_redraw();
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("📝 Starting Verturion Text Rendering Test");
    println!("=========================================");
    println!();
    println!("This test validates the fontdue baseline alignment fix:");
    println!("  - Problem: Characters had inconsistent vertical positioning ('wobbly' text)");
    println!("  - Solution: Changed formula from 'baseline_y + glyph.bearing_y - glyph.height'");
    println!("             to 'baseline_y - glyph.height'");
    println!("  - Expected: All characters should align to consistent baseline");
    println!();

    let event_loop = EventLoop::new()?;
    event_loop.set_control_flow(ControlFlow::Poll);

    let mut app = TextRenderingTest::new();
    event_loop.run_app(&mut app)?;

    Ok(())
}
