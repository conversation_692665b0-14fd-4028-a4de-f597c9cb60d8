[package]
name = "verturion"
version = "0.1.0"
edition = "2024"

[dependencies]
# Graphics and windowing
winit = "0.30"
wgpu = "22.0"
pollster = "0.3"

# Modern font rendering (Bevy-style) - simplified approach
ab_glyph = "0.2"

# Image loading
image = "0.25"

# Vertex data serialization
bytemuck = { version = "1.18", features = ["derive"] }

# Logging for test applications
env_logger = "0.11"

[dev-dependencies]
criterion = { version = "0.6.0", features = ["html_reports"] }

[[bench]]
name = "vector2_benchmarks"
harness = false

[lib]
name = "verturion"
path = "src/lib.rs"
